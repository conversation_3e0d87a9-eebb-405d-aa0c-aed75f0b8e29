import React from 'react';
import { StyleSheet, Dimensions, View, ActivityIndicator, Text } from 'react-native';
import Pdf from 'react-native-pdf';
import { useRoute } from '@react-navigation/native';

const PdfViewerScreen = () => {
    const route = useRoute();
    // Expecting the PDF source URI to be passed as a route parameter
    const { sourceUri } = route.params;

    if (!sourceUri) {
        return (
            <View style={styles.container}>
                <Text style={styles.errorText}>Error: No PDF source provided.</Text>
            </View>
        );
    }

    const pdfSource = { uri: sourceUri, cache: true };

    return (
        <View style={styles.container}>
            <Pdf
                trustAllCerts={false} // Recommended for security
                source={pdfSource}
                onLoadComplete={(numberOfPages, filePath) => {
                    console.log(`Number of pages: ${numberOfPages}`);
                }}
                onPageChanged={(page, numberOfPages) => {
                    console.log(`Current page: ${page}`);
                }}
                onError={(error) => {
                    console.error('PDF Error:', error);
                    // Optionally show an error message to the user
                }}
                onPressLink={(uri) => {
                    console.log(`Link pressed: ${uri}`);
                    // Handle link presses if needed, e.g., open in browser
                }}
                style={styles.pdf}
                renderActivityIndicator={() => <ActivityIndicator color="blue" size="large" />}
                enablePaging={true} // Enable swipe gestures to change pages
            />
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        justifyContent: 'flex-start',
        alignItems: 'center',
        marginTop: 25, // Adjust as needed for status bar height
    },
    pdf: {
        flex: 1,
        width: Dimensions.get('window').width,
        height: Dimensions.get('window').height,
    },
    errorText: {
        fontSize: 18,
        color: 'red',
        marginTop: 50,
    }
});

export default PdfViewerScreen;
