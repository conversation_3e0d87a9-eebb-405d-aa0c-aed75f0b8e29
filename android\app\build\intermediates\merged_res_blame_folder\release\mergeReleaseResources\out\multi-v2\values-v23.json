{"logs": [{"outputFile": "com.gokul719.snack97152fc1f368437dac54171df4ba22bd.app-mergeReleaseResources-74:/values-v23/values-v23.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\33933c9dfd3dea5e401b89560d839cba\\transformed\\work-runtime-2.7.0\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,121", "endColumns": "65,62", "endOffsets": "116,179"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ab468244bce13502a51b6cbb55b0c3ba\\transformed\\material-1.6.1\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2,5,8,13,17,20,23,26,31,34,38", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,271,481,801,1038,1245,1452,1655,1987,2189,2454", "endLines": "4,7,12,16,19,22,25,30,33,37,41", "endColumns": "10,10,10,10,10,10,10,10,10,10,10", "endOffsets": "266,476,796,1033,1240,1447,1650,1982,2184,2449,2722"}, "to": {"startLines": "55,58,61,66,70,73,76,79,84,87,91", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3511,3727,3937,4257,4494,4701,4908,5111,5443,5645,5910", "endLines": "57,60,65,69,72,75,78,83,86,90,94", "endColumns": "10,10,10,10,10,10,10,10,10,10,10", "endOffsets": "3722,3932,4252,4489,4696,4903,5106,5438,5640,5905,6178"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\42358a9cae14349299c1c7a268311762\\transformed\\appcompat-1.7.0\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2,3,4,5,6,20,34,35,36,39,43,44,45,46", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,190,325,400,487,1225,1975,2094,2221,2443,2667,2782,2889,3002", "endLines": "2,3,4,5,19,33,34,35,38,42,43,44,45,49", "endColumns": "134,134,74,86,12,12,118,126,12,12,114,106,112,12", "endOffsets": "185,320,395,482,1220,1970,2089,2216,2438,2662,2777,2884,2997,3227"}, "to": {"startLines": "4,5,6,7,8,22,36,37,38,41,45,46,47,48", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "184,319,454,529,616,1354,2104,2223,2350,2572,2796,2911,3018,3131", "endLines": "4,5,6,7,21,35,36,37,40,44,45,46,47,51", "endColumns": "134,134,74,86,12,12,118,126,12,12,114,106,112,12", "endOffsets": "314,449,524,611,1349,2099,2218,2345,2567,2791,2906,3013,3126,3356"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\97471c397bccf6da42a783affda4a2d9\\transformed\\cardview-1.0.0\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "4", "endColumns": "12", "endOffsets": "200"}, "to": {"startLines": "52", "startColumns": "4", "startOffsets": "3361", "endLines": "54", "endColumns": "12", "endOffsets": "3506"}}]}]}