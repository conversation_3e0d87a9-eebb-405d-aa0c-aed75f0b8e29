import React, { useState, useEffect, useContext, useRef } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Alert, Animated, TextInput } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { ThemeContext } from './ThemeContext';
import { useNavigation } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import BannerAdComponent from '../components/BannerAdComponent';
import { AD_PLACEMENTS } from '../src/config/adConfig';

const FavoritesScreen = () => {
  const { darkMode, fontSizeMultiplier } = useContext(ThemeContext);
  const navigation = useNavigation();

  const [favorites, setFavorites] = useState([]);
  const [filteredFavorites, setFilteredFavorites] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [loading, setLoading] = useState(true);
  const fadeAnim = useRef(new Animated.Value(0)).current;

  const categories = ['All', 'Physics', 'Chemistry', 'Biology', 'Mathematics'];

  useEffect(() => {
    loadFavorites();
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 800,
      useNativeDriver: true,
    }).start();
  }, []);

  useEffect(() => {
    filterFavorites();
  }, [favorites, searchQuery, selectedCategory]);

  const loadFavorites = async () => {
    try {
      const favoritesData = await AsyncStorage.getItem('user_favorites');
      if (favoritesData) {
        const parsedFavorites = JSON.parse(favoritesData);
        setFavorites(parsedFavorites);
      }
    } catch (error) {
      console.error('Error loading favorites:', error);
    } finally {
      setLoading(false);
    }
  };

  const filterFavorites = () => {
    let filtered = favorites;

    // Filter by category
    if (selectedCategory !== 'All') {
      filtered = filtered.filter(item => item.category === selectedCategory);
    }

    // Filter by search query
    if (searchQuery.trim()) {
      filtered = filtered.filter(item =>
        item.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.answer.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.category.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    setFilteredFavorites(filtered);
  };

  const removeFavorite = async (itemId) => {
    Alert.alert(
      'Remove Favorite',
      'Are you sure you want to remove this question from favorites?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: async () => {
            try {
              const updatedFavorites = favorites.filter(item => item.id !== itemId);
              setFavorites(updatedFavorites);
              await AsyncStorage.setItem('user_favorites', JSON.stringify(updatedFavorites));
            } catch (error) {
              console.error('Error removing favorite:', error);
              Alert.alert('Error', 'Failed to remove favorite');
            }
          }
        }
      ]
    );
  };

  const addToPlaylist = (item) => {
    navigation.navigate('StudyPlaylists', {
      action: 'add_question',
      question: item
    });
  };

  const practiceQuestion = (item) => {
    navigation.navigate('QuizQuestions', {
      questions: [item],
      category: item.category,
      mode: 'practice'
    });
  };

  const getCategoryColor = (category) => {
    const colors = {
      Physics: '#667eea',
      Chemistry: '#f093fb',
      Biology: '#4ECDC4',
      Mathematics: '#FFA726',
      default: '#AB47BC'
    };
    return colors[category] || colors.default;
  };

  const renderFavoriteItem = (item, index) => (
    <Animated.View
      key={item.id}
      style={[
        styles.favoriteCard,
        {
          backgroundColor: darkMode ? '#2A2A2A' : '#FFFFFF',
          opacity: fadeAnim,
          transform: [{
            translateY: fadeAnim.interpolate({
              inputRange: [0, 1],
              outputRange: [30, 0]
            })
          }]
        }
      ]}
    >
      <View style={styles.favoriteHeader}>
        <View style={[
          styles.categoryBadge,
          { backgroundColor: getCategoryColor(item.category) }
        ]}>
          <Text style={[
            styles.categoryText,
            { fontSize: 10 * fontSizeMultiplier }
          ]}>
            {item.category}
          </Text>
        </View>

        <View style={styles.favoriteActions}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => addToPlaylist(item)}
          >
            <Ionicons name="add-circle-outline" size={20} color="#667eea" />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => removeFavorite(item.id)}
          >
            <Ionicons name="heart" size={20} color="#F44336" />
          </TouchableOpacity>
        </View>
      </View>

      <Text style={[
        styles.questionText,
        {
          color: darkMode ? '#FFFFFF' : '#333333',
          fontSize: 14 * fontSizeMultiplier
        }
      ]}>
        {item.question}
      </Text>

      <Text style={[
        styles.answerText,
        {
          color: darkMode ? '#CCCCCC' : '#666666',
          fontSize: 12 * fontSizeMultiplier
        }
      ]}>
        Answer: {item.answer}
      </Text>

      <View style={styles.favoriteFooter}>
        <Text style={[
          styles.dateText,
          {
            color: darkMode ? '#AAAAAA' : '#888888',
            fontSize: 10 * fontSizeMultiplier
          }
        ]}>
          Added: {new Date(item.dateAdded).toLocaleDateString()}
        </Text>

        <TouchableOpacity
          style={styles.practiceButton}
          onPress={() => practiceQuestion(item)}
        >
          <Text style={[
            styles.practiceButtonText,
            { fontSize: 12 * fontSizeMultiplier }
          ]}>
            Practice
          </Text>
          <Ionicons name="play" size={14} color="#FFFFFF" />
        </TouchableOpacity>
      </View>
    </Animated.View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="heart-outline" size={64} color="#CCCCCC" />
      <Text style={[
        styles.emptyStateTitle,
        {
          color: darkMode ? '#FFFFFF' : '#333333',
          fontSize: 18 * fontSizeMultiplier
        }
      ]}>
        No Favorites Yet
      </Text>
      <Text style={[
        styles.emptyStateText,
        {
          color: darkMode ? '#CCCCCC' : '#666666',
          fontSize: 14 * fontSizeMultiplier
        }
      ]}>
        Start adding questions to your favorites by tapping the heart icon during quizzes
      </Text>

      <TouchableOpacity
        style={styles.startQuizButton}
        onPress={() => navigation.navigate('MainApp', { screen: 'Quiz' })}
      >
        <LinearGradient
          colors={['#667eea', '#764ba2']}
          style={styles.startQuizGradient}
        >
          <Text style={[
            styles.startQuizText,
            { fontSize: 14 * fontSizeMultiplier }
          ]}>
            Start Quiz
          </Text>
          <Ionicons name="arrow-forward" size={16} color="#FFFFFF" />
        </LinearGradient>
      </TouchableOpacity>
    </View>
  );

  if (loading) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <Text style={[styles.loadingText, { color: darkMode ? '#FFFFFF' : '#333333' }]}>
          Loading favorites...
        </Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, darkMode ? styles.darkContainer : styles.lightContainer]}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={[
          styles.headerTitle,
          {
            color: darkMode ? '#FFFFFF' : '#333333',
            fontSize: 24 * fontSizeMultiplier
          }
        ]}>
          Favorites
        </Text>
        <Text style={[
          styles.headerSubtitle,
          {
            color: darkMode ? '#CCCCCC' : '#666666',
            fontSize: 16 * fontSizeMultiplier
          }
        ]}>
          {favorites.length} saved question{favorites.length !== 1 ? 's' : ''}
        </Text>
      </View>

      {favorites.length > 0 && (
        <>
          {/* Search Bar */}
          <View style={styles.searchContainer}>
            <View style={[
              styles.searchBar,
              { backgroundColor: darkMode ? '#2A2A2A' : '#F0F0F0' }
            ]}>
              <Ionicons name="search" size={20} color={darkMode ? '#CCCCCC' : '#666666'} />
              <TextInput
                style={[
                  styles.searchInput,
                  {
                    color: darkMode ? '#FFFFFF' : '#333333',
                    fontSize: 14 * fontSizeMultiplier
                  }
                ]}
                placeholder="Search favorites..."
                placeholderTextColor={darkMode ? '#AAAAAA' : '#999999'}
                value={searchQuery}
                onChangeText={setSearchQuery}
              />
              {searchQuery.length > 0 && (
                <TouchableOpacity onPress={() => setSearchQuery('')}>
                  <Ionicons name="close" size={20} color={darkMode ? '#CCCCCC' : '#666666'} />
                </TouchableOpacity>
              )}
            </View>
          </View>

          {/* Category Filter */}
          <View style={styles.categoryContainer}>
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.categoryScrollContent}
            >
              {categories.map((category) => (
                <TouchableOpacity
                  key={category}
                  style={[
                    styles.categoryButton,
                    selectedCategory === category && styles.selectedCategoryButton
                  ]}
                  onPress={() => setSelectedCategory(category)}
                >
                  <Text style={[
                    styles.categoryButtonText,
                    { fontSize: 12 * fontSizeMultiplier },
                    selectedCategory === category && styles.selectedCategoryButtonText
                  ]}>
                    {category}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </>
      )}

      {/* Favorites List */}
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {filteredFavorites.length === 0 ? (
          favorites.length === 0 ? renderEmptyState() : (
            <View style={styles.noResultsState}>
              <Ionicons name="search" size={48} color="#CCCCCC" />
              <Text style={[
                styles.noResultsText,
                {
                  color: darkMode ? '#CCCCCC' : '#666666',
                  fontSize: 16 * fontSizeMultiplier
                }
              ]}>
                No favorites match your search
              </Text>
            </View>
          )
        ) : (
          filteredFavorites.map((item, index) => renderFavoriteItem(item, index))
        )}
      </ScrollView>

      {/* Enhanced Banner Ad */}
      <View style={styles.adContainer}>
        <BannerAdComponent
          placement={AD_PLACEMENTS.FAVORITES_BANNER}
          fallbackToWebView={true}
          enableRefresh={true}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  darkContainer: {
    backgroundColor: '#121212',
  },
  lightContainer: {
    backgroundColor: '#F5F5F5',
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    padding: 20,
    paddingTop: 40,
    alignItems: 'center',
  },
  headerTitle: {
    fontWeight: 'bold',
    marginBottom: 8,
  },
  headerSubtitle: {
    textAlign: 'center',
  },
  loadingText: {
    fontSize: 18,
    textAlign: 'center',
  },
  searchContainer: {
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 25,
  },
  searchInput: {
    flex: 1,
    marginLeft: 12,
    marginRight: 8,
  },
  categoryContainer: {
    marginBottom: 20,
  },
  categoryScrollContent: {
    paddingHorizontal: 20,
  },
  categoryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    borderRadius: 20,
    backgroundColor: '#E0E0E0',
  },
  selectedCategoryButton: {
    backgroundColor: '#667eea',
  },
  categoryButtonText: {
    color: '#666666',
    fontWeight: '500',
  },
  selectedCategoryButtonText: {
    color: '#FFFFFF',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  favoriteCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  favoriteHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  categoryBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  categoryText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  favoriteActions: {
    flexDirection: 'row',
  },
  actionButton: {
    padding: 4,
    marginLeft: 8,
  },
  questionText: {
    fontWeight: '500',
    marginBottom: 8,
    lineHeight: 20,
  },
  answerText: {
    fontStyle: 'italic',
    marginBottom: 12,
    lineHeight: 18,
  },
  favoriteFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.1)',
    paddingTop: 8,
  },
  dateText: {
    fontStyle: 'italic',
  },
  practiceButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#667eea',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
  },
  practiceButtonText: {
    color: '#FFFFFF',
    fontWeight: '500',
    marginRight: 4,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 60,
    paddingHorizontal: 40,
  },
  emptyStateTitle: {
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateText: {
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 24,
  },
  startQuizButton: {
    borderRadius: 25,
    overflow: 'hidden',
  },
  startQuizGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 12,
  },
  startQuizText: {
    color: '#FFFFFF',
    fontWeight: '500',
    marginRight: 8,
  },
  noResultsState: {
    alignItems: 'center',
    paddingVertical: 60,
  },
  noResultsText: {
    marginTop: 16,
    textAlign: 'center',
  },
  adContainer: {
    width: '100%',
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
});

export default FavoritesScreen;
