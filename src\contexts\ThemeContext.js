import React, { createContext, useState } from 'react';

// Create context with default value
export const ThemeContext = createContext({
  fontSizeMultiplier: 1,
  toggleFontSize: () => {}
});

export const ThemeProvider = ({ children }) => {
  const [fontSizeMultiplier, setFontSizeMultiplier] = useState(1);

  const toggleFontSize = () => {
    setFontSizeMultiplier(prev => prev === 1 ? 1.2 : 1);
  };

  const value = {
    fontSizeMultiplier,
    toggleFontSize
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
}; 