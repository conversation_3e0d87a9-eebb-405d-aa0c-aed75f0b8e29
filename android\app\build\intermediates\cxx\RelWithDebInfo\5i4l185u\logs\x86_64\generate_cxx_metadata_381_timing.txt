# C/C++ build system timings
generate_cxx_metadata
  [gap of 38ms]
  create-invalidation-state 53ms
  generate-prefab-packages
    [gap of 64ms]
    exec-prefab 1116ms
    [gap of 122ms]
  generate-prefab-packages completed in 1302ms
  execute-generate-process
    exec-configure 1194ms
    [gap of 216ms]
  execute-generate-process completed in 1413ms
  [gap of 96ms]
generate_cxx_metadata completed in 2910ms

