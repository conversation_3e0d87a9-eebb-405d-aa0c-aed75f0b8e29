1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.gokul719.snack97152fc1f368437dac54171df4ba22bd"
4    android:versionCode="1"
5    android:versionName="1.2.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.CAMERA" />
11-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:2:3-62
11-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:2:20-60
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:3:3-64
12-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:3:20-62
13    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
13-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:4:3-77
13-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:4:20-75
14    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
14-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:5:3-77
14-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:5:20-75
15    <uses-permission android:name="android.permission.RECORD_AUDIO" />
15-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:6:3-68
15-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:6:20-66
16    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
16-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:7:3-75
16-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:7:20-73
17    <uses-permission android:name="android.permission.VIBRATE" />
17-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:8:3-63
17-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:8:20-61
18    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
18-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:9:3-78
18-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:9:20-76
19    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
19-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:10:3-76
19-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:10:20-74
20
21    <queries>
21-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:11:3-17:13
22        <intent>
22-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:12:5-16:14
23            <action android:name="android.intent.action.VIEW" />
23-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:13:7-58
23-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:13:15-56
24
25            <category android:name="android.intent.category.BROWSABLE" />
25-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:14:7-67
25-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:14:17-65
26
27            <data android:scheme="https" />
27-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:15:7-37
27-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:15:13-35
28        </intent>
29        <!-- Query open documents -->
30        <intent>
30-->[:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:9-17:18
31            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
31-->[:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:13-79
31-->[:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:21-76
32        </intent>
33        <intent>
33-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:9-19:18
34
35            <!-- Required for picking images from the camera roll if targeting API 30 -->
36            <action android:name="android.media.action.IMAGE_CAPTURE" />
36-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:13-73
36-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:21-70
37        </intent>
38        <intent>
38-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:9-24:18
39
40            <!-- Required for picking images from the camera if targeting API 30 -->
41            <action android:name="android.media.action.ACTION_VIDEO_CAPTURE" />
41-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:13-80
41-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:21-77
42        </intent>
43        <intent>
43-->[:expo-sharing] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-14:18
44
45            <!-- Required for file sharing if targeting API 30 -->
46            <action android:name="android.intent.action.SEND" />
46-->[:expo-sharing] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-65
46-->[:expo-sharing] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:21-62
47
48            <data android:mimeType="*/*" />
48-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:15:7-37
49        </intent>
50        <intent>
50-->[:expo-speech] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-speech\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-12:18
51
52            <!-- Required for text-to-speech if targeting API 30 -->
53            <action android:name="android.intent.action.TTS_SERVICE" />
53-->[:expo-speech] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-speech\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-72
53-->[:expo-speech] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-speech\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:21-69
54        </intent>
55        <intent>
55-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b33a2f8f4c91a9de31219b5632fe82\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:10:9-16:18
56            <action android:name="android.intent.action.GET_CONTENT" />
56-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b33a2f8f4c91a9de31219b5632fe82\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:11:13-72
56-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b33a2f8f4c91a9de31219b5632fe82\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:11:21-69
57
58            <category android:name="android.intent.category.OPENABLE" />
58-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b33a2f8f4c91a9de31219b5632fe82\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:13:13-73
58-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b33a2f8f4c91a9de31219b5632fe82\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:13:23-70
59
60            <data android:mimeType="*/*" />
60-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:15:7-37
61        </intent> <!-- End of browser content -->
62        <!-- For CustomTabsService -->
63        <intent>
63-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:47:9-49:18
64            <action android:name="android.support.customtabs.action.CustomTabsService" />
64-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:48:13-90
64-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:48:21-87
65        </intent> <!-- End of CustomTabsService -->
66        <!-- For MRAID capabilities -->
67        <intent>
67-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:52:9-56:18
68            <action android:name="android.intent.action.INSERT" />
68-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:53:13-67
68-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:53:21-64
69
70            <data android:mimeType="vnd.android.cursor.dir/event" />
70-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:15:7-37
71        </intent>
72        <intent>
72-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:57:9-61:18
73            <action android:name="android.intent.action.VIEW" />
73-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:13:7-58
73-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:13:15-56
74
75            <data android:scheme="sms" />
75-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:15:7-37
75-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:15:13-35
76        </intent>
77        <intent>
77-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:62:9-66:18
78            <action android:name="android.intent.action.DIAL" />
78-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:63:13-65
78-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:63:21-62
79
80            <data android:path="tel:" />
80-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:15:7-37
81        </intent>
82    </queries>
83
84    <uses-permission android:name="android.permission.WAKE_LOCK" />
84-->[:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:5-68
84-->[:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:22-65
85    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
85-->[:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:5-79
85-->[:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:22-76
86    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
86-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:27:5-82
86-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:27:22-79
87    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
87-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:28:5-88
87-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:28:22-85
88    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" />
88-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:29:5-83
88-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:29:22-80
89    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
89-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:5-77
89-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:22-74
90
91    <permission
91-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a0941004ac45c50b486dd320a2bfc96\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
92        android:name="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
92-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a0941004ac45c50b486dd320a2bfc96\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
93        android:protectionLevel="signature" />
93-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a0941004ac45c50b486dd320a2bfc96\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
94
95    <uses-permission android:name="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
95-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a0941004ac45c50b486dd320a2bfc96\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
95-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a0941004ac45c50b486dd320a2bfc96\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
96
97    <application
97-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:18:3-37:17
98        android:name="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.MainApplication"
98-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:18:16-47
99        android:allowBackup="true"
99-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:18:162-188
100        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
100-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:18:248-316
101        android:extractNativeLibs="false"
102        android:icon="@mipmap/ic_launcher"
102-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:18:81-115
103        android:label="@string/app_name"
103-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:18:48-80
104        android:roundIcon="@mipmap/ic_launcher_round"
104-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:18:116-161
105        android:supportsRtl="true"
105-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:18:221-247
106        android:theme="@style/AppTheme" >
106-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:18:189-220
107        <meta-data
107-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:19:5-159
108            android:name="com.google.android.gms.ads.APPLICATION_ID"
108-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:19:16-72
109            android:value="ca-app-pub-9706687137550019~9208363455" />
109-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:19:73-127
110        <meta-data
110-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:20:5-137
111            android:name="com.google.android.gms.ads.DELAY_APP_MEASUREMENT_INIT"
111-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:20:16-84
112            android:value="true" />
112-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:20:85-105
113        <meta-data
113-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:21:5-83
114            android:name="expo.modules.updates.ENABLED"
114-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:21:16-59
115            android:value="false" />
115-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:21:60-81
116        <meta-data
116-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:22:5-105
117            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
117-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:22:16-80
118            android:value="ALWAYS" />
118-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:22:81-103
119        <meta-data
119-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:23:5-99
120            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
120-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:23:16-79
121            android:value="0" />
121-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:23:80-97
122
123        <activity
123-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:24:5-36:16
124            android:name="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.MainActivity"
124-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:24:15-43
125            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
125-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:24:44-134
126            android:exported="true"
126-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:24:256-279
127            android:launchMode="singleTask"
127-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:24:135-166
128            android:screenOrientation="portrait"
128-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:24:280-316
129            android:theme="@style/Theme.App.SplashScreen"
129-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:24:210-255
130            android:windowSoftInputMode="adjustResize" >
130-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:24:167-209
131            <intent-filter>
131-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:25:7-28:23
132                <action android:name="android.intent.action.MAIN" />
132-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:26:9-60
132-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:26:17-58
133
134                <category android:name="android.intent.category.LAUNCHER" />
134-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:27:9-68
134-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:27:19-66
135            </intent-filter>
136            <intent-filter>
136-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:29:7-35:23
137                <action android:name="android.intent.action.VIEW" />
137-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:13:7-58
137-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:13:15-56
138
139                <category android:name="android.intent.category.DEFAULT" />
139-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:31:9-67
139-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:31:19-65
140                <category android:name="android.intent.category.BROWSABLE" />
140-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:14:7-67
140-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:14:17-65
141
142                <data android:scheme="com.gokul719.snack97152fc1f368437dac54171df4ba22bd" />
142-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:15:7-37
142-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:15:13-35
143                <data android:scheme="exp+snack-97152fc1-f368-437d-ac54-171df4ba22bd" />
143-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:15:7-37
143-->C:\Users\<USER>\quiz-bee-techs\android\app\src\main\AndroidManifest.xml:15:13-35
144            </intent-filter>
145        </activity>
146
147        <meta-data
147-->[:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:9-21:36
148            android:name="com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION"
148-->[:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:13-83
149            android:value="true" />
149-->[:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:13-33
150        <meta-data
150-->[:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:9-24:36
151            android:name="com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING"
151-->[:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:13-79
152            android:value="true" />
152-->[:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:13-33
153        <!--
154           This may generate a warning during your build:
155
156           > property#android.adservices.AD_SERVICES_CONFIG@android:resource
157           > was tagged at AndroidManifest.xml:23 to replace other declarations
158           > but no other declaration present
159
160           You may safely ignore this warning.
161
162           We must include this in case you also use Firebase Analytics in some
163           of its configurations, as it may also include this file, and the two
164           will collide and cause a build error if we don't set this one to take
165           priority via replacement.
166
167           https://github.com/invertase/react-native-google-mobile-ads/issues/657
168        -->
169        <property
169-->[:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:42:9-45:48
170            android:name="android.adservices.AD_SERVICES_CONFIG"
170-->[:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:43:13-65
171            android:resource="@xml/gma_ad_services_config" />
171-->[:react-native-google-mobile-ads] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:44:13-59
172
173        <provider
173-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-16:20
174            android:name="com.reactnativecommunity.webview.RNCWebViewFileProvider"
174-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-83
175            android:authorities="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.fileprovider"
175-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-64
176            android:exported="false"
176-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-37
177            android:grantUriPermissions="true" >
177-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-47
178            <meta-data
178-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-15:63
179                android:name="android.support.FILE_PROVIDER_PATHS"
179-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:17-67
180                android:resource="@xml/file_provider_paths" />
180-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-60
181        </provider>
182        <provider
182-->[:expo-clipboard] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-clipboard\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-15:20
183            android:name="expo.modules.clipboard.ClipboardFileProvider"
183-->[:expo-clipboard] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-clipboard\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-72
184            android:authorities="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.ClipboardFileProvider"
184-->[:expo-clipboard] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-clipboard\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-73
185            android:exported="true" >
185-->[:expo-clipboard] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-clipboard\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-36
186            <meta-data
186-->[:expo-clipboard] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-clipboard\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-14:68
187                android:name="expo.modules.clipboard.CLIPBOARD_FILE_PROVIDER_PATHS"
187-->[:expo-clipboard] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-clipboard\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:17-84
188                android:resource="@xml/clipboard_provider_paths" />
188-->[:expo-clipboard] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-clipboard\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:17-65
189        </provider>
190        <provider
190-->[:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:9-30:20
191            android:name="expo.modules.filesystem.FileSystemFileProvider"
191-->[:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:13-74
192            android:authorities="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.FileSystemFileProvider"
192-->[:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:13-74
193            android:exported="false"
193-->[:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:13-37
194            android:grantUriPermissions="true" >
194-->[:expo-file-system] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:13-47
195            <meta-data
195-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-15:63
196                android:name="android.support.FILE_PROVIDER_PATHS"
196-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:17-67
197                android:resource="@xml/file_system_provider_paths" />
197-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-60
198        </provider>
199
200        <service
200-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:9-40:19
201            android:name="com.google.android.gms.metadata.ModuleDependencies"
201-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:13-78
202            android:enabled="false"
202-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:13-36
203            android:exported="false" >
203-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:31:13-37
204            <intent-filter>
204-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:33:13-35:29
205                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
205-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:34:17-94
205-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:34:25-91
206            </intent-filter>
207
208            <meta-data
208-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:37:13-39:36
209                android:name="photopicker_activity:0:required"
209-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:38:17-63
210                android:value="" />
210-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:39:17-33
211        </service>
212
213        <activity
213-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:42:9-44:59
214            android:name="com.canhub.cropper.CropImageActivity"
214-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:43:13-64
215            android:exported="true"
215-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b33a2f8f4c91a9de31219b5632fe82\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:35:13-36
216            android:theme="@style/Base.Theme.AppCompat" /> <!-- https://developer.android.com/guide/topics/manifest/provider-element.html -->
216-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:44:13-56
217        <provider
217-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:46:9-54:20
218            android:name="expo.modules.imagepicker.fileprovider.ImagePickerFileProvider"
218-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:47:13-89
219            android:authorities="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.ImagePickerFileProvider"
219-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:48:13-75
220            android:exported="false"
220-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:49:13-37
221            android:grantUriPermissions="true" >
221-->[:expo-image-picker] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:50:13-47
222            <meta-data
222-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-15:63
223                android:name="android.support.FILE_PROVIDER_PATHS"
223-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:17-67
224                android:resource="@xml/image_picker_provider_paths" />
224-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-60
225        </provider>
226        <provider
226-->[:expo-sharing] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:9-26:20
227            android:name="expo.modules.sharing.SharingFileProvider"
227-->[:expo-sharing] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:13-68
228            android:authorities="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.SharingFileProvider"
228-->[:expo-sharing] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:13-71
229            android:exported="false"
229-->[:expo-sharing] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:13-37
230            android:grantUriPermissions="true" >
230-->[:expo-sharing] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-sharing\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:13-47
231            <meta-data
231-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-15:63
232                android:name="android.support.FILE_PROVIDER_PATHS"
232-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:17-67
233                android:resource="@xml/sharing_provider_paths" />
233-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-60
234        </provider>
235
236        <meta-data
236-->[:expo-modules-core] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:9-11:89
237            android:name="org.unimodules.core.AppLoader#react-native-headless"
237-->[:expo-modules-core] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-79
238            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
238-->[:expo-modules-core] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-86
239        <meta-data
239-->[:expo-modules-core] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:9-15:45
240            android:name="com.facebook.soloader.enabled"
240-->[:expo-modules-core] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-57
241            android:value="true" />
241-->[:expo-modules-core] C:\Users\<USER>\quiz-bee-techs\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-33
242        <meta-data
242-->[com.github.bumptech.glide:okhttp3-integration:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1dc3cf6a41b4f5c2729b5d5e6d5ceec\transformed\okhttp3-integration-4.12.0\AndroidManifest.xml:11:9-13:43
243            android:name="com.bumptech.glide.integration.okhttp3.OkHttpGlideModule"
243-->[com.github.bumptech.glide:okhttp3-integration:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1dc3cf6a41b4f5c2729b5d5e6d5ceec\transformed\okhttp3-integration-4.12.0\AndroidManifest.xml:12:13-84
244            android:value="GlideModule" />
244-->[com.github.bumptech.glide:okhttp3-integration:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1dc3cf6a41b4f5c2729b5d5e6d5ceec\transformed\okhttp3-integration-4.12.0\AndroidManifest.xml:13:13-40
245
246        <provider
246-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b33a2f8f4c91a9de31219b5632fe82\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:23:9-31:20
247            android:name="com.canhub.cropper.CropFileProvider"
247-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b33a2f8f4c91a9de31219b5632fe82\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:24:13-63
248            android:authorities="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.cropper.fileprovider"
248-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b33a2f8f4c91a9de31219b5632fe82\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:25:13-72
249            android:exported="false"
249-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b33a2f8f4c91a9de31219b5632fe82\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:26:13-37
250            android:grantUriPermissions="true" >
250-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b33a2f8f4c91a9de31219b5632fe82\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:27:13-47
251            <meta-data
251-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-15:63
252                android:name="android.support.FILE_PROVIDER_PATHS"
252-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:17-67
253                android:resource="@xml/library_file_paths" />
253-->[:react-native-webview] C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-60
254        </provider> <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
255        <activity
255-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:73:9-78:43
256            android:name="com.google.android.gms.ads.AdActivity"
256-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:74:13-65
257            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
257-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:75:13-122
258            android:exported="false"
258-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:76:13-37
259            android:theme="@android:style/Theme.Translucent" />
259-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:77:13-61
260
261        <provider
261-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:80:9-85:43
262            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
262-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:81:13-76
263            android:authorities="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.mobileadsinitprovider"
263-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:82:13-73
264            android:exported="false"
264-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:83:13-37
265            android:initOrder="100" />
265-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:84:13-36
266
267        <service
267-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:87:9-91:43
268            android:name="com.google.android.gms.ads.AdService"
268-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:88:13-64
269            android:enabled="true"
269-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:89:13-35
270            android:exported="false" />
270-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:90:13-37
271
272        <activity
272-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:93:9-97:43
273            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
273-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:94:13-82
274            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
274-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:95:13-122
275            android:exported="false" />
275-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:96:13-37
276        <activity
276-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:98:9-105:43
277            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
277-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:99:13-82
278            android:excludeFromRecents="true"
278-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:100:13-46
279            android:exported="false"
279-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:101:13-37
280            android:launchMode="singleTask"
280-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:102:13-44
281            android:taskAffinity=""
281-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:103:13-36
282            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
282-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03277ad713d4560f5b459351d8a4b83d\transformed\play-services-ads-lite-23.6.0\AndroidManifest.xml:104:13-72
283        <activity
283-->[com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d502ef2bffb1f4edf1dd29201baa894c\transformed\play-services-base-18.0.0\AndroidManifest.xml:20:9-22:45
284            android:name="com.google.android.gms.common.api.GoogleApiActivity"
284-->[com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d502ef2bffb1f4edf1dd29201baa894c\transformed\play-services-base-18.0.0\AndroidManifest.xml:20:19-85
285            android:exported="false"
285-->[com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d502ef2bffb1f4edf1dd29201baa894c\transformed\play-services-base-18.0.0\AndroidManifest.xml:22:19-43
286            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
286-->[com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d502ef2bffb1f4edf1dd29201baa894c\transformed\play-services-base-18.0.0\AndroidManifest.xml:21:19-78
287
288        <meta-data
288-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0309cb612008fa0a7f120a51a87e98c5\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
289            android:name="com.google.android.gms.version"
289-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0309cb612008fa0a7f120a51a87e98c5\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
290            android:value="@integer/google_play_services_version" />
290-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0309cb612008fa0a7f120a51a87e98c5\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
291
292        <uses-library
292-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa2d5bf76eb464c8ff4defe2fea1aac0\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
293            android:name="android.ext.adservices"
293-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa2d5bf76eb464c8ff4defe2fea1aac0\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
294            android:required="false" />
294-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa2d5bf76eb464c8ff4defe2fea1aac0\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
295
296        <provider
296-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:31:9-39:20
297            android:name="androidx.startup.InitializationProvider"
297-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:32:13-67
298            android:authorities="com.gokul719.snack97152fc1f368437dac54171df4ba22bd.androidx-startup"
298-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:33:13-68
299            android:exported="false" >
299-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:34:13-37
300            <meta-data
300-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:36:13-38:52
301                android:name="androidx.work.WorkManagerInitializer"
301-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:37:17-68
302                android:value="androidx.startup" />
302-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:38:17-49
303            <meta-data
303-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\867f28603a9adbd922e925087114cbc1\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
304                android:name="androidx.emoji2.text.EmojiCompatInitializer"
304-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\867f28603a9adbd922e925087114cbc1\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
305                android:value="androidx.startup" />
305-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\867f28603a9adbd922e925087114cbc1\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
306            <meta-data
306-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad10a3280ed9ff2393dd52d1764f5de6\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
307                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
307-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad10a3280ed9ff2393dd52d1764f5de6\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
308                android:value="androidx.startup" />
308-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad10a3280ed9ff2393dd52d1764f5de6\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
309            <meta-data
309-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
310                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
310-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
311                android:value="androidx.startup" />
311-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
312        </provider>
313
314        <service
314-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:41:9-46:35
315            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
315-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:42:13-88
316            android:directBootAware="false"
316-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:43:13-44
317            android:enabled="@bool/enable_system_alarm_service_default"
317-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:44:13-72
318            android:exported="false" />
318-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:45:13-37
319        <service
319-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:47:9-53:35
320            android:name="androidx.work.impl.background.systemjob.SystemJobService"
320-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:48:13-84
321            android:directBootAware="false"
321-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:49:13-44
322            android:enabled="@bool/enable_system_job_service_default"
322-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:50:13-70
323            android:exported="true"
323-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:51:13-36
324            android:permission="android.permission.BIND_JOB_SERVICE" />
324-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:52:13-69
325        <service
325-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:54:9-59:35
326            android:name="androidx.work.impl.foreground.SystemForegroundService"
326-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:55:13-81
327            android:directBootAware="false"
327-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:56:13-44
328            android:enabled="@bool/enable_system_foreground_service_default"
328-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:57:13-77
329            android:exported="false" />
329-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:58:13-37
330
331        <receiver
331-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:61:9-66:35
332            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
332-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:62:13-88
333            android:directBootAware="false"
333-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:63:13-44
334            android:enabled="true"
334-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:64:13-35
335            android:exported="false" />
335-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:65:13-37
336        <receiver
336-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:67:9-77:20
337            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
337-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:68:13-106
338            android:directBootAware="false"
338-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:69:13-44
339            android:enabled="false"
339-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:70:13-36
340            android:exported="false" >
340-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:71:13-37
341            <intent-filter>
341-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:73:13-76:29
342                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
342-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:17-87
342-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:25-84
343                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
343-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:17-90
343-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:25-87
344            </intent-filter>
345        </receiver>
346        <receiver
346-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:78:9-88:20
347            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
347-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:79:13-104
348            android:directBootAware="false"
348-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:80:13-44
349            android:enabled="false"
349-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:81:13-36
350            android:exported="false" >
350-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:82:13-37
351            <intent-filter>
351-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:84:13-87:29
352                <action android:name="android.intent.action.BATTERY_OKAY" />
352-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:17-77
352-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:25-74
353                <action android:name="android.intent.action.BATTERY_LOW" />
353-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:17-76
353-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:25-73
354            </intent-filter>
355        </receiver>
356        <receiver
356-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:89:9-99:20
357            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
357-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:90:13-104
358            android:directBootAware="false"
358-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:91:13-44
359            android:enabled="false"
359-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:92:13-36
360            android:exported="false" >
360-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:93:13-37
361            <intent-filter>
361-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:95:13-98:29
362                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
362-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:17-83
362-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:25-80
363                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
363-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:17-82
363-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:25-79
364            </intent-filter>
365        </receiver>
366        <receiver
366-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:100:9-109:20
367            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
367-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:101:13-103
368            android:directBootAware="false"
368-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:102:13-44
369            android:enabled="false"
369-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:103:13-36
370            android:exported="false" >
370-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:104:13-37
371            <intent-filter>
371-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:106:13-108:29
372                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
372-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:17-79
372-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:25-76
373            </intent-filter>
374        </receiver>
375        <receiver
375-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:110:9-121:20
376            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
376-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:111:13-88
377            android:directBootAware="false"
377-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:112:13-44
378            android:enabled="false"
378-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:113:13-36
379            android:exported="false" >
379-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:114:13-37
380            <intent-filter>
380-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:116:13-120:29
381                <action android:name="android.intent.action.BOOT_COMPLETED" />
381-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:17-79
381-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:25-76
382                <action android:name="android.intent.action.TIME_SET" />
382-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:17-73
382-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:25-70
383                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
383-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:17-81
383-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:25-78
384            </intent-filter>
385        </receiver>
386        <receiver
386-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:122:9-131:20
387            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
387-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:123:13-99
388            android:directBootAware="false"
388-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:124:13-44
389            android:enabled="@bool/enable_system_alarm_service_default"
389-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:125:13-72
390            android:exported="false" >
390-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:126:13-37
391            <intent-filter>
391-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:128:13-130:29
392                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
392-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:17-98
392-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:25-95
393            </intent-filter>
394        </receiver>
395        <receiver
395-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:132:9-142:20
396            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
396-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:133:13-78
397            android:directBootAware="false"
397-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:134:13-44
398            android:enabled="true"
398-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:135:13-35
399            android:exported="true"
399-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:136:13-36
400            android:permission="android.permission.DUMP" >
400-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:137:13-57
401            <intent-filter>
401-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:139:13-141:29
402                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
402-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:17-88
402-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33933c9dfd3dea5e401b89560d839cba\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:25-85
403            </intent-filter>
404        </receiver>
405        <receiver
405-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
406            android:name="androidx.profileinstaller.ProfileInstallReceiver"
406-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
407            android:directBootAware="false"
407-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
408            android:enabled="true"
408-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
409            android:exported="true"
409-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
410            android:permission="android.permission.DUMP" >
410-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
411            <intent-filter>
411-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
412                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
412-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
412-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
413            </intent-filter>
414            <intent-filter>
414-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
415                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
415-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
415-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
416            </intent-filter>
417            <intent-filter>
417-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
418                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
418-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
418-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
419            </intent-filter>
420            <intent-filter>
420-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
421                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
421-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
421-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7dcdac4df96419702de83328665f9a7f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
422            </intent-filter>
423        </receiver>
424
425        <service
425-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34a8096090b7377744aaf14d903d92a7\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
426            android:name="androidx.room.MultiInstanceInvalidationService"
426-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34a8096090b7377744aaf14d903d92a7\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
427            android:directBootAware="true"
427-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34a8096090b7377744aaf14d903d92a7\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
428            android:exported="false" />
428-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34a8096090b7377744aaf14d903d92a7\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
429    </application>
430
431</manifest>
