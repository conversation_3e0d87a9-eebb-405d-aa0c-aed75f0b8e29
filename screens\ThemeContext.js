import React, { createContext, useState, useEffect, useCallback } from 'react'; // Import useCallback
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Appearance } from 'react-native'; // Import Appearance

export const ThemeContext = createContext();

export const ThemeProvider = ({ children }) => {
  // Initialize state based on system preference or a default
  const [darkMode, setDarkMode] = useState(Appearance.getColorScheme() === 'dark');
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [soundVolume, setSoundVolume] = useState(0.5); // Add sound volume state (default 50%)
  const [fontSizeMultiplier, setFontSizeMultiplier] = useState(1.0); // Add font size state (1.0 = Medium)
  // Removed isLoading state

  // Load saved preferences from AsyncStorage on mount
  useEffect(() => {
    const loadPreferences = async () => {
      try {
        const [savedMode, savedSound, savedVolume, savedFontSize] = await Promise.all([
          AsyncStorage.getItem('darkMode'),
          AsyncStorage.getItem('soundEnabled'),
          AsyncStorage.getItem('soundVolume'), // Load volume
          AsyncStorage.getItem('fontSizeMultiplier') // Load font size
        ]);

        if (savedMode !== null) {
          setDarkMode(JSON.parse(savedMode));
        }
        if (savedSound !== null) {
          setSoundEnabled(savedSound === 'true');
        }
        if (savedVolume !== null) {
          setSoundVolume(parseFloat(savedVolume)); // Volume stored as string number
        }
        if (savedFontSize !== null) {
          setFontSizeMultiplier(parseFloat(savedFontSize)); // Font size stored as string number
        }
      } catch (error) {
        console.error('Failed to load preferences from storage', error);
      } 
      // Removed finally block - loading state is removed
    };

    loadPreferences();
  }, []);

  // Save theme preference to AsyncStorage whenever it changes
  useEffect(() => {
    // Save immediately when darkMode changes
    AsyncStorage.setItem('darkMode', JSON.stringify(darkMode)).catch(error => {
      console.error('Failed to save theme to storage', error);
    });
  }, [darkMode]); // Removed isLoading dependency

  // Save sound preference to AsyncStorage whenever it changes
  useEffect(() => {
    // Save immediately when soundEnabled changes
    AsyncStorage.setItem('soundEnabled', soundEnabled.toString()).catch(error => {
       console.error('Failed to save sound setting to storage', error);
    });
  }, [soundEnabled]); // Removed isLoading dependency

  // Save sound volume preference to AsyncStorage whenever it changes
  useEffect(() => {
    // Save immediately when soundVolume changes
    AsyncStorage.setItem('soundVolume', soundVolume.toString()).catch(error => {
       console.error('Failed to save sound volume setting to storage', error);
    });
  }, [soundVolume]); // Removed isLoading dependency

  // Save font size preference to AsyncStorage whenever it changes
  useEffect(() => {
    // Save immediately when fontSizeMultiplier changes
    AsyncStorage.setItem('fontSizeMultiplier', fontSizeMultiplier.toString()).catch(error => {
       console.error('Failed to save font size setting to storage', error);
    });
  }, [fontSizeMultiplier]); // Removed isLoading dependency

  // Memoized toggle function for sound
  const toggleSound = useCallback(() => {
    setSoundEnabled(prev => !prev);
  }, []);

  // Function to change font size multiplier (can add validation if needed)
  const changeFontSize = useCallback((multiplier) => {
     setFontSizeMultiplier(multiplier);
  }, []);

  // Function to change sound volume (can add validation if needed)
  const changeSoundVolume = useCallback((volume) => {
     // Ensure volume is within 0.0 to 1.0 range
     const clampedVolume = Math.max(0.0, Math.min(1.0, volume));
     setSoundVolume(clampedVolume);
  }, []);


  // Removed the isLoading check - render children immediately
  // Preferences will apply once loaded asynchronously

  // Include sound volume state and change function in the context value
  return (
    <ThemeContext.Provider value={{ 
      darkMode, setDarkMode, 
      soundEnabled, toggleSound, 
      soundVolume, changeSoundVolume, // Added volume control
      fontSizeMultiplier, changeFontSize 
    }}>
      {children}
    </ThemeContext.Provider>
  );
};
