# C/C++ build system timings
generate_cxx_metadata
  [gap of 26ms]
  create-invalidation-state 158ms
  generate-prefab-packages
    [gap of 77ms]
    exec-prefab 1647ms
    [gap of 58ms]
  generate-prefab-packages completed in 1782ms
  execute-generate-process
    exec-configure 1699ms
    [gap of 163ms]
  execute-generate-process completed in 1864ms
  [gap of 98ms]
  write-metadata-json-to-file 11ms
generate_cxx_metadata completed in 3951ms

