# version control
.git
.gitignore

# build artifacts
*.aab
*.apk
*.ipa
build/
android/app/build/
ios/build/
dist/
out/

# node & dependencies
node_modules/
.pnpm/
.pnp/
.yarn/cache/
.yarn/unplugged/

# config/cache
.env*
.cache/
.expo/
.expo-shared/
.idea/
.vscode/

# logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# additional exclusions for smaller upload
android/.gradle/
android/app/src/main/assets/
*.md
docs/
screenshots/
*.mp4
*.mov
*.avi
__tests__/
*.test.js
*.test.ts
check-ads.js
