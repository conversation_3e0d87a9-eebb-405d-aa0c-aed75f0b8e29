{"logs": [{"outputFile": "com.gokul719.snack97152fc1f368437dac54171df4ba22bd.app-mergeReleaseResources-74:/values-pt-rPT/values-pt-rPT.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\659eba141f87d2afd24e0f28711a233e\\transformed\\play-services-ads-23.6.0\\res\\values-pt-rPT\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "203,245,292,361,427,497,595,658,798,917,1036,1086,1137,1248,1337,1379,1470,1505,1540,1594,1672,1716", "endColumns": "41,46,68,65,69,97,62,139,118,118,49,50,110,88,41,90,34,34,53,77,43,55", "endOffsets": "244,291,360,426,496,594,657,797,916,1035,1085,1136,1247,1336,1378,1469,1504,1539,1593,1671,1715,1771"}, "to": {"startLines": "198,199,200,203,204,205,206,207,208,209,210,211,212,213,220,221,222,223,224,225,226,251", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16499,16545,16596,16847,16917,16991,17093,17160,17304,17427,17550,17604,17659,17774,18374,18420,18515,18554,18593,18651,18733,20769", "endColumns": "45,50,72,69,73,101,66,143,122,122,53,54,114,92,45,94,38,38,57,81,47,59", "endOffsets": "16540,16591,16664,16912,16986,17088,17155,17299,17422,17545,17599,17654,17769,17862,18415,18510,18549,18588,18646,18728,18776,20824"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\46429bd1dac210616d37d633e605f2e8\\transformed\\browser-1.8.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,171,270,382", "endColumns": "115,98,111,102", "endOffsets": "166,265,377,480"}, "to": {"startLines": "83,140,141,142", "startColumns": "4,4,4,4", "startOffsets": "7397,11752,11851,11963", "endColumns": "115,98,111,102", "endOffsets": "7508,11846,11958,12061"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7c7a5afab22fc7e54eb8c70c357abcc1\\transformed\\react-android-0.76.9-release\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,210,281,351,434,504,583,662,750,834,908,997,1081,1157,1238,1320,1395,1473,1547,1637,1709,1795,1871", "endColumns": "68,85,70,69,82,69,78,78,87,83,73,88,83,75,80,81,74,77,73,89,71,85,75,85", "endOffsets": "119,205,276,346,429,499,578,657,745,829,903,992,1076,1152,1233,1315,1390,1468,1542,1632,1704,1790,1866,1952"}, "to": {"startLines": "50,64,143,145,146,150,163,164,215,216,219,227,230,231,232,234,235,237,239,240,242,245,247,248", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3612,4977,12066,12216,12286,12610,13610,13689,17953,18041,18300,18781,19032,19116,19192,19349,19431,19585,19738,19812,20003,20223,20431,20507", "endColumns": "68,85,70,69,82,69,78,78,87,83,73,88,83,75,80,81,74,77,73,89,71,85,75,85", "endOffsets": "3676,5058,12132,12281,12364,12675,13684,13763,18036,18120,18369,18865,19111,19187,19268,19426,19501,19658,19807,19897,20070,20304,20502,20588"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1c6965b61d3737d12e0f0d1e87d21cde\\transformed\\ui-release\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,283,380,479,565,644,741,832,919,1004,1094,1170,1246,1325,1400,1476,1548", "endColumns": "94,82,96,98,85,78,96,90,86,84,89,75,75,78,74,75,71,121", "endOffsets": "195,278,375,474,560,639,736,827,914,999,1089,1165,1241,1320,1395,1471,1543,1665"}, "to": {"startLines": "62,63,84,85,86,147,148,201,202,217,218,229,233,236,238,243,244,246", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4799,4894,7513,7610,7709,12369,12448,16669,16760,18125,18210,18956,19273,19506,19663,20075,20151,20309", "endColumns": "94,82,96,98,85,78,96,90,86,84,89,75,75,78,74,75,71,121", "endOffsets": "4889,4972,7605,7704,7790,12443,12540,16755,16842,18205,18295,19027,19344,19580,19733,20146,20218,20426"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0309cb612008fa0a7f120a51a87e98c5\\transformed\\play-services-basement-18.4.0\\res\\values-pt-rPT\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "140", "endOffsets": "339"}, "to": {"startLines": "73", "startColumns": "4", "startOffsets": "6094", "endColumns": "144", "endOffsets": "6234"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7bbb068f5d1da3b4f3e1ef6a35abc59e\\transformed\\foundation-release\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,88", "endOffsets": "137,226"}, "to": {"startLines": "249,250", "startColumns": "4,4", "startOffsets": "20593,20680", "endColumns": "86,88", "endOffsets": "20675,20764"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\495d9b066df23cebe365f0b1f0fc7029\\transformed\\exoplayer-ui-2.18.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,471,658,739,822,895,989,1079,1153,1220,1317,1414,1480,1549,1616,1687,1798,1909,2019,2086,2172,2245,2319,2406,2495,2559,2626,2679,2737,2785,2846,2911,2979,3044,3113,3177,3238,3304,3369,3435,3488,3548,3622,3696", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,80,82,72,93,89,73,66,96,96,65,68,66,70,110,110,109,66,85,72,73,86,88,63,66,52,57,47,60,64,67,64,68,63,60,65,64,65,52,59,73,73,58", "endOffsets": "280,466,653,734,817,890,984,1074,1148,1215,1312,1409,1475,1544,1611,1682,1793,1904,2014,2081,2167,2240,2314,2401,2490,2554,2621,2674,2732,2780,2841,2906,2974,3039,3108,3172,3233,3299,3364,3430,3483,3543,3617,3691,3750"}, "to": {"startLines": "2,11,15,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,521,7859,7940,8023,8096,8190,8280,8354,8421,8518,8615,8681,8750,8817,8888,8999,9110,9220,9287,9373,9446,9520,9607,9696,9760,10531,10584,10642,10690,10751,10816,10884,10949,11018,11082,11143,11209,11274,11340,11393,11453,11527,11601", "endLines": "10,14,18,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "endColumns": "17,12,12,80,82,72,93,89,73,66,96,96,65,68,66,70,110,110,109,66,85,72,73,86,88,63,66,52,57,47,60,64,67,64,68,63,60,65,64,65,52,59,73,73,58", "endOffsets": "330,516,703,7935,8018,8091,8185,8275,8349,8416,8513,8610,8676,8745,8812,8883,8994,9105,9215,9282,9368,9441,9515,9602,9691,9755,9822,10579,10637,10685,10746,10811,10879,10944,11013,11077,11138,11204,11269,11335,11388,11448,11522,11596,11655"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ab468244bce13502a51b6cbb55b0c3ba\\transformed\\material-1.6.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,228,310,413,533,614,678,770,849,914,1004,1072,1134,1207,1271,1325,1451,1509,1571,1625,1701,1844,1931,2013,2122,2204,2286,2373,2440,2506,2581,2661,2748,2821,2898,2971,3045,3138,3215,3308,3406,3480,3561,3660,3713,3779,3868,3956,4018,4082,4145,4261,4364,4471,4575", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,81,102,119,80,63,91,78,64,89,67,61,72,63,53,125,57,61,53,75,142,86,81,108,81,81,86,66,65,74,79,86,72,76,72,73,92,76,92,97,73,80,98,52,65,88,87,61,63,62,115,102,106,103,85", "endOffsets": "223,305,408,528,609,673,765,844,909,999,1067,1129,1202,1266,1320,1446,1504,1566,1620,1696,1839,1926,2008,2117,2199,2281,2368,2435,2501,2576,2656,2743,2816,2893,2966,3040,3133,3210,3303,3401,3475,3556,3655,3708,3774,3863,3951,4013,4077,4140,4256,4359,4466,4570,4656"}, "to": {"startLines": "19,51,59,60,61,87,139,144,149,151,152,153,154,155,156,157,158,159,160,161,162,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,214", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "708,3681,4495,4598,4718,7795,11660,12137,12545,12680,12770,12838,12900,12973,13037,13091,13217,13275,13337,13391,13467,13768,13855,13937,14046,14128,14210,14297,14364,14430,14505,14585,14672,14745,14822,14895,14969,15062,15139,15232,15330,15404,15485,15584,15637,15703,15792,15880,15942,16006,16069,16185,16288,16395,17867", "endLines": "22,51,59,60,61,87,139,144,149,151,152,153,154,155,156,157,158,159,160,161,162,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,214", "endColumns": "12,81,102,119,80,63,91,78,64,89,67,61,72,63,53,125,57,61,53,75,142,86,81,108,81,81,86,66,65,74,79,86,72,76,72,73,92,76,92,97,73,80,98,52,65,88,87,61,63,62,115,102,106,103,85", "endOffsets": "876,3758,4593,4713,4794,7854,11747,12211,12605,12765,12833,12895,12968,13032,13086,13212,13270,13332,13386,13462,13605,13850,13932,14041,14123,14205,14292,14359,14425,14500,14580,14667,14740,14817,14890,14964,15057,15134,15227,15325,15399,15480,15579,15632,15698,15787,15875,15937,16001,16064,16180,16283,16390,16494,17948"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d502ef2bffb1f4edf1dd29201baa894c\\transformed\\play-services-base-18.0.0\\res\\values-pt-rPT\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,298,457,581,685,849,973,1091,1196,1380,1484,1650,1777,1932,2106,2170,2235", "endColumns": "100,158,123,103,163,123,117,104,183,103,165,126,154,173,63,64,82", "endOffsets": "297,456,580,684,848,972,1090,1195,1379,1483,1649,1776,1931,2105,2169,2234,2317"}, "to": {"startLines": "65,66,67,68,69,70,71,72,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5063,5168,5331,5459,5567,5735,5863,5985,6239,6427,6535,6705,6836,6995,7173,7241,7310", "endColumns": "104,162,127,107,167,127,121,108,187,107,169,130,158,177,67,68,86", "endOffsets": "5163,5326,5454,5562,5730,5858,5980,6089,6422,6530,6700,6831,6990,7168,7236,7305,7392"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7a0941004ac45c50b486dd320a2bfc96\\transformed\\core-1.13.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,666,787", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "147,249,348,448,555,661,782,883"}, "to": {"startLines": "52,53,54,55,56,57,58,241", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3763,3860,3962,4061,4161,4268,4374,19902", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "3855,3957,4056,4156,4263,4369,4490,19998"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b56249db71bc4564ba5f782195824c95\\transformed\\exoplayer-core-2.18.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,194,261,332,414,496,591,680", "endColumns": "74,63,66,70,81,81,94,88,78", "endOffsets": "125,189,256,327,409,491,586,675,754"}, "to": {"startLines": "112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9827,9902,9966,10033,10104,10186,10268,10363,10452", "endColumns": "74,63,66,70,81,81,94,88,78", "endOffsets": "9897,9961,10028,10099,10181,10263,10358,10447,10526"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\42358a9cae14349299c1c7a268311762\\transformed\\appcompat-1.7.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,426,515,616,734,819,899,991,1085,1182,1276,1375,1469,1565,1660,1752,1844,1929,2036,2147,2249,2357,2465,2572,2737,2836", "endColumns": "107,105,106,88,100,117,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,164,98,85", "endOffsets": "208,314,421,510,611,729,814,894,986,1080,1177,1271,1370,1464,1560,1655,1747,1839,1924,2031,2142,2244,2352,2460,2567,2732,2831,2917"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,228", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "881,989,1095,1202,1291,1392,1510,1595,1675,1767,1861,1958,2052,2151,2245,2341,2436,2528,2620,2705,2812,2923,3025,3133,3241,3348,3513,18870", "endColumns": "107,105,106,88,100,117,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,164,98,85", "endOffsets": "984,1090,1197,1286,1387,1505,1590,1670,1762,1856,1953,2047,2146,2240,2336,2431,2523,2615,2700,2807,2918,3020,3128,3236,3343,3508,3607,18951"}}]}]}