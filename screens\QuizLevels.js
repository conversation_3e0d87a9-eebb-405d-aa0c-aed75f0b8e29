import React, { useEffect, useState, useRef, useContext } from 'react'; // Import useContext
import { View, Text, TouchableOpacity, StyleSheet, FlatList, Animated, Alert } from 'react-native';
// Removed unused AsyncStorage import
import { ThemeContext } from './ThemeContext'; // Import ThemeContext
import { Audio } from 'expo-av';
import * as Haptics from 'expo-haptics';

const QuizLevels = ({ route, navigation }) => {
  const { darkMode, soundEnabled, fontSizeMultiplier } = useContext(ThemeContext); // Get fontSizeMultiplier
  const { category } = route.params || { category: 'Unknown' };
  const totalLevels = 10;
  const levels = Array.from({ length: totalLevels }, (_, i) => i + 1);
  const [unlockedLevels] = useState(totalLevels); // Removed unused setUnlockedLevels
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const soundRef = useRef(new Audio.Sound());

  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 500,
      useNativeDriver: true,
    }).start();

    const currentSound = soundRef.current;
    return () => {
      if (currentSound) {
        currentSound.unloadAsync().catch(error => console.error("Error unloading sound:", error));
      }
    };
  }, [fadeAnim]);

  const playSound = async (type) => {
    if (!soundEnabled) return; // Check if sound is enabled
    try {
      const soundFile = type === 'success' 
        ? require('../assets/audios/click.mp3') 
        : require('../assets/audios/error.mp3');
      await soundRef.current.unloadAsync();
      await soundRef.current.loadAsync(soundFile, {}, true);
      await soundRef.current.replayAsync();
    } catch (error) {
      console.error('Error playing sound:', error);
    }
  };

  const retryLevel = (level) => {
    Alert.alert(
      'Retry Level',
      `Do you want to retry Level ${level}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Retry', onPress: () => startLevel(level) }
      ]
    );
  };

  const startLevel = async (level) => {
    await playSound('success');
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    navigation.navigate('QuizQuestions', { category, level });
  };

  return (
    // Apply dynamic background based on darkMode
    <View style={[styles.container, { backgroundColor: darkMode ? '#121212' : '#f0f0f0' }]}> 
      {/* Apply dynamic text color and font size based on context */}
      <Text style={[styles.title, { fontSize: styles.title.fontSize * fontSizeMultiplier, color: darkMode ? '#fff' : '#333' }]}>{category} - Select a Level</Text> 
      <Animated.View style={{ opacity: fadeAnim }}>
        <FlatList
          data={levels}
          keyExtractor={(item) => item.toString()}
          numColumns={2}
          contentContainerStyle={styles.listContainer}
          renderItem={({ item }) => (
            <TouchableOpacity 
              style={[styles.levelButton, { backgroundColor: item <= unlockedLevels ? '#4CAF50' : '#888' }]}
              onPress={() => item <= unlockedLevels ? startLevel(item) : Alert.alert('Locked', 'Complete previous levels to unlock this one.')}
              onLongPress={() => retryLevel(item)}
            >
              {/* Apply font size multiplier */}
              <Text style={[styles.levelText, { fontSize: styles.levelText.fontSize * fontSizeMultiplier }]}>{`Level ${item}`}</Text> 
            </TouchableOpacity>
          )}
        />
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  // Remove fixed background color from container style
  container: { flex: 1, alignItems: 'center', paddingVertical: 20 }, 
  // Remove fixed text color from title style
  title: { fontSize: 26, fontWeight: 'bold', marginBottom: 20, textAlign: 'center' }, 
  listContainer: { paddingBottom: 20, alignItems: 'center' },
  levelButton: { padding: 20, borderRadius: 15, margin: 10, width: 140, height: 100, alignItems: 'center', justifyContent: 'center', elevation: 4 },
  levelText: { fontSize: 20, fontWeight: 'bold', color: '#fff' },
});

export default QuizLevels;
