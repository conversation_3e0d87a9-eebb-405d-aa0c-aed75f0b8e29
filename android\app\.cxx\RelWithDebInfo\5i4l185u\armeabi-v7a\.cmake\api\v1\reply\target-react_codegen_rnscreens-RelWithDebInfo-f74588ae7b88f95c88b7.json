{"artifacts": [{"path": "C:/Users/<USER>/quiz-bee-techs/android/app/build/intermediates/cxx/RelWithDebInfo/5i4l185u/obj/armeabi-v7a/libreact_codegen_rnscreens.so"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "add_compile_options", "target_compile_options", "target_include_directories"], "files": ["C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 25, "parent": 0}, {"command": 1, "file": 0, "line": 42, "parent": 0}, {"command": 2, "file": 0, "line": 13, "parent": 0}, {"command": 3, "file": 0, "line": 70, "parent": 0}, {"command": 4, "file": 0, "line": 32, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC"}, {"backtrace": 3, "fragment": "-fexceptions"}, {"backtrace": 3, "fragment": "-frtti"}, {"backtrace": 3, "fragment": "-std=c++20"}, {"backtrace": 3, "fragment": "-Wall"}, {"backtrace": 3, "fragment": "-Wpedantic"}, {"backtrace": 3, "fragment": "-Wno-gnu-zero-variadic-macro-arguments"}, {"backtrace": 4, "fragment": "-DLOG_TAG=\\\"ReactNative\\\""}, {"backtrace": 0, "fragment": "-DFOLLY_NO_CONFIG=1"}, {"backtrace": 0, "fragment": "-DFOLLY_HAVE_CLOCK_GETTIME=1"}, {"backtrace": 0, "fragment": "-DFOLLY_USE_LIBCPP=1"}, {"backtrace": 0, "fragment": "-DFOLLY_CFG_NO_COROUTINES=1"}, {"backtrace": 0, "fragment": "-DFOLLY_MOBILE=1"}, {"backtrace": 0, "fragment": "-DFOLLY_HAVE_RECVMMSG=1"}, {"backtrace": 0, "fragment": "-DFOLLY_HAVE_PTHREAD=1"}, {"backtrace": 0, "fragment": "-DFOLLY_HAVE_XSI_STRERROR_R=1"}], "defines": [{"define": "react_codegen_rnscreens_EXPORTS"}], "includes": [{"backtrace": 5, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/."}, {"backtrace": 5, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp"}, {"backtrace": 5, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni"}, {"backtrace": 5, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens"}, {"backtrace": 2, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.10.2/transforms/7c7a5afab22fc7e54eb8c70c357abcc1/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include"}, {"backtrace": 2, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.10.2/transforms/7c7a5afab22fc7e54eb8c70c357abcc1/transformed/react-android-0.76.9-release/prefab/modules/jsi/include"}, {"backtrace": 2, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.10.2/transforms/1957b0db02baa50e26af4d5b92e700f6/transformed/fbjni-0.6.0/prefab/modules/fbjni/include"}], "language": "CXX", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13], "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}], "id": "react_codegen_rnscreens::@25bcbd507e98d3a854ad", "link": {"commandFragments": [{"fragment": "-Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments  -Wl,--gc-sections", "role": "flags"}, {"backtrace": 2, "fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7c7a5afab22fc7e54eb8c70c357abcc1\\transformed\\react-android-0.76.9-release\\prefab\\modules\\reactnative\\libs\\android.armeabi-v7a\\libreactnative.so", "role": "libraries"}, {"backtrace": 2, "fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7c7a5afab22fc7e54eb8c70c357abcc1\\transformed\\react-android-0.76.9-release\\prefab\\modules\\jsi\\libs\\android.armeabi-v7a\\libjsi.so", "role": "libraries"}, {"backtrace": 2, "fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1957b0db02baa50e26af4d5b92e700f6\\transformed\\fbjni-0.6.0\\prefab\\modules\\fbjni\\libs\\android.armeabi-v7a\\libfbjni.so", "role": "libraries"}, {"fragment": "-latomic -lm", "role": "libraries"}], "language": "CXX", "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}, "name": "react_codegen_rnscreens", "nameOnDisk": "libreact_codegen_rnscreens.so", "paths": {"build": "rnscreens_autolinked_build", "source": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/rnscreens.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}