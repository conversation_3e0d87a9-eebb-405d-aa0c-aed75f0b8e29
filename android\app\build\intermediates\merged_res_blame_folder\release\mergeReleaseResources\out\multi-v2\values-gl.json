{"logs": [{"outputFile": "com.gokul719.snack97152fc1f368437dac54171df4ba22bd.app-mergeReleaseResources-74:/values-gl/values-gl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7a0941004ac45c50b486dd320a2bfc96\\transformed\\core-1.13.1\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,783", "endColumns": "98,101,99,97,106,105,115,100", "endOffsets": "149,251,351,449,556,662,778,879"}, "to": {"startLines": "51,52,53,54,55,56,57,221", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3742,3841,3943,4043,4141,4248,4354,18558", "endColumns": "98,101,99,97,106,105,115,100", "endOffsets": "3836,3938,4038,4136,4243,4349,4465,18654"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0309cb612008fa0a7f120a51a87e98c5\\transformed\\play-services-basement-18.4.0\\res\\values-gl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "129", "endOffsets": "324"}, "to": {"startLines": "71", "startColumns": "4", "startOffsets": "5992", "endColumns": "133", "endOffsets": "6121"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b56249db71bc4564ba5f782195824c95\\transformed\\exoplayer-core-2.18.1\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,192,257,336,413,489,588,684", "endColumns": "73,62,64,78,76,75,98,95,68", "endOffsets": "124,187,252,331,408,484,583,679,748"}, "to": {"startLines": "110,111,112,113,114,115,116,117,118", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9817,9891,9954,10019,10098,10175,10251,10350,10446", "endColumns": "73,62,64,78,76,75,98,95,68", "endOffsets": "9886,9949,10014,10093,10170,10246,10345,10441,10510"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ab468244bce13502a51b6cbb55b0c3ba\\transformed\\material-1.6.1\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,230,315,420,553,633,711,807,886,949,1044,1113,1176,1250,1314,1370,1491,1549,1611,1667,1744,1883,1971,2051,2161,2241,2321,2411,2478,2544,2623,2704,2792,2871,2948,3030,3119,3203,3295,3388,3489,3563,3655,3757,3809,3875,3967,4055,4117,4181,4244,4355,4457,4563,4666", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,84,104,132,79,77,95,78,62,94,68,62,73,63,55,120,57,61,55,76,138,87,79,109,79,79,89,66,65,78,80,87,78,76,81,88,83,91,92,100,73,91,101,51,65,91,87,61,63,62,110,101,105,102,84", "endOffsets": "225,310,415,548,628,706,802,881,944,1039,1108,1171,1245,1309,1365,1486,1544,1606,1662,1739,1878,1966,2046,2156,2236,2316,2406,2473,2539,2618,2699,2787,2866,2943,3025,3114,3198,3290,3383,3484,3558,3650,3752,3804,3870,3962,4050,4112,4176,4239,4350,4452,4558,4661,4746"}, "to": {"startLines": "19,50,58,59,60,85,137,141,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,206", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "732,3657,4470,4575,4708,7654,11668,12084,12335,12398,12493,12562,12625,12699,12763,12819,12940,12998,13060,13116,13193,13332,13420,13500,13610,13690,13770,13860,13927,13993,14072,14153,14241,14320,14397,14479,14568,14652,14744,14837,14938,15012,15104,15206,15258,15324,15416,15504,15566,15630,15693,15804,15906,16012,17481", "endLines": "22,50,58,59,60,85,137,141,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,206", "endColumns": "12,84,104,132,79,77,95,78,62,94,68,62,73,63,55,120,57,61,55,76,138,87,79,109,79,79,89,66,65,78,80,87,78,76,81,88,83,91,92,100,73,91,101,51,65,91,87,61,63,62,110,101,105,102,84", "endOffsets": "902,3737,4570,4703,4783,7727,11759,12158,12393,12488,12557,12620,12694,12758,12814,12935,12993,13055,13111,13188,13327,13415,13495,13605,13685,13765,13855,13922,13988,14067,14148,14236,14315,14392,14474,14563,14647,14739,14832,14933,15007,15099,15201,15253,15319,15411,15499,15561,15625,15688,15799,15901,16007,16110,17561"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d502ef2bffb1f4edf1dd29201baa894c\\transformed\\play-services-base-18.0.0\\res\\values-gl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,454,579,683,838,966,1081,1186,1353,1458,1623,1754,1915,2063,2126,2191", "endColumns": "101,158,124,103,154,127,114,104,166,104,164,130,160,147,62,64,80", "endOffsets": "294,453,578,682,837,965,1080,1185,1352,1457,1622,1753,1914,2062,2125,2190,2271"}, "to": {"startLines": "63,64,65,66,67,68,69,70,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4967,5073,5236,5365,5473,5632,5764,5883,6126,6297,6406,6575,6710,6875,7027,7094,7163", "endColumns": "105,162,128,107,158,131,118,108,170,108,168,134,164,151,66,68,84", "endOffsets": "5068,5231,5360,5468,5627,5759,5878,5987,6292,6401,6570,6705,6870,7022,7089,7158,7243"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\659eba141f87d2afd24e0f28711a233e\\transformed\\play-services-ads-23.6.0\\res\\values-gl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,241,288,350,416,486,599,662,789,902,1022,1072,1123,1240,1329,1369,1464,1499,1535,1589,1676,1718", "endColumns": "41,46,61,65,69,112,62,126,112,119,49,50,116,88,39,94,34,35,53,86,41,54", "endOffsets": "240,287,349,415,485,598,661,788,901,1021,1071,1122,1239,1328,1368,1463,1498,1534,1588,1675,1717,1772"}, "to": {"startLines": "190,191,192,195,196,197,198,199,200,201,202,203,204,205,209,210,211,212,213,214,215,227", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16115,16161,16212,16458,16528,16602,16719,16786,16917,17034,17158,17212,17267,17388,17741,17785,17884,17923,17963,18021,18112,19135", "endColumns": "45,50,65,69,73,116,66,130,116,123,53,54,120,92,43,98,38,39,57,90,45,58", "endOffsets": "16156,16207,16273,16523,16597,16714,16781,16912,17029,17153,17207,17262,17383,17476,17780,17879,17918,17958,18016,18107,18153,19189"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\46429bd1dac210616d37d633e605f2e8\\transformed\\browser-1.8.0\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,376", "endColumns": "106,101,111,105", "endOffsets": "157,259,371,477"}, "to": {"startLines": "81,138,139,140", "startColumns": "4,4,4,4", "startOffsets": "7248,11764,11866,11978", "endColumns": "106,101,111,105", "endOffsets": "7350,11861,11973,12079"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\495d9b066df23cebe365f0b1f0fc7029\\transformed\\exoplayer-ui-2.18.1\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,291,499,682,768,856,935,1033,1128,1205,1272,1372,1472,1538,1607,1674,1745,1876,1995,2121,2192,2278,2354,2431,2534,2639,2703,2767,2820,2878,2926,2987,3052,3122,3188,3260,3330,3398,3464,3529,3595,3648,3710,3786,3862", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,87,78,97,94,76,66,99,99,65,68,66,70,130,118,125,70,85,75,76,102,104,63,63,52,57,47,60,64,69,65,71,69,67,65,64,65,52,61,75,75,57", "endOffsets": "286,494,677,763,851,930,1028,1123,1200,1267,1367,1467,1533,1602,1669,1740,1871,1990,2116,2187,2273,2349,2426,2529,2634,2698,2762,2815,2873,2921,2982,3047,3117,3183,3255,3325,3393,3459,3524,3590,3643,3705,3781,3857,3915"}, "to": {"startLines": "2,11,15,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,341,549,7732,7818,7906,7985,8083,8178,8255,8322,8422,8522,8588,8657,8724,8795,8926,9045,9171,9242,9328,9404,9481,9584,9689,9753,10515,10568,10626,10674,10735,10800,10870,10936,11008,11078,11146,11212,11277,11343,11396,11458,11534,11610", "endLines": "10,14,18,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136", "endColumns": "17,12,12,85,87,78,97,94,76,66,99,99,65,68,66,70,130,118,125,70,85,75,76,102,104,63,63,52,57,47,60,64,69,65,71,69,67,65,64,65,52,61,75,75,57", "endOffsets": "336,544,727,7813,7901,7980,8078,8173,8250,8317,8417,8517,8583,8652,8719,8790,8921,9040,9166,9237,9323,9399,9476,9579,9684,9748,9812,10563,10621,10669,10730,10795,10865,10931,11003,11073,11141,11207,11272,11338,11391,11453,11529,11605,11663"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1c6965b61d3737d12e0f0d1e87d21cde\\transformed\\ui-release\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,284,393,494,583,662,755,847,935,1020,1110,1187,1271,1351,1427,1509,1581", "endColumns": "95,82,108,100,88,78,92,91,87,84,89,76,83,79,75,81,71,121", "endOffsets": "196,279,388,489,578,657,750,842,930,1015,1105,1182,1266,1346,1422,1504,1576,1698"}, "to": {"startLines": "61,62,82,83,84,142,143,193,194,207,208,217,218,219,220,222,223,224", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4788,4884,7355,7464,7565,12163,12242,16278,16370,17566,17651,18241,18318,18402,18482,18659,18741,18813", "endColumns": "95,82,108,100,88,78,92,91,87,84,89,76,83,79,75,81,71,121", "endOffsets": "4879,4962,7459,7560,7649,12237,12330,16365,16453,17646,17736,18313,18397,18477,18553,18736,18808,18930"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7bbb068f5d1da3b4f3e1ef6a35abc59e\\transformed\\foundation-release\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,154", "endColumns": "98,100", "endOffsets": "149,250"}, "to": {"startLines": "225,226", "startColumns": "4,4", "startOffsets": "18935,19034", "endColumns": "98,100", "endOffsets": "19029,19130"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\42358a9cae14349299c1c7a268311762\\transformed\\appcompat-1.7.0\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,313,421,506,607,735,821,902,994,1088,1185,1279,1379,1473,1569,1664,1756,1848,1929,2037,2144,2251,2360,2465,2579,2756,2855", "endColumns": "103,103,107,84,100,127,85,80,91,93,96,93,99,93,95,94,91,91,80,107,106,106,108,104,113,176,98,82", "endOffsets": "204,308,416,501,602,730,816,897,989,1083,1180,1274,1374,1468,1564,1659,1751,1843,1924,2032,2139,2246,2355,2460,2574,2751,2850,2933"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,216", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "907,1011,1115,1223,1308,1409,1537,1623,1704,1796,1890,1987,2081,2181,2275,2371,2466,2558,2650,2731,2839,2946,3053,3162,3267,3381,3558,18158", "endColumns": "103,103,107,84,100,127,85,80,91,93,96,93,99,93,95,94,91,91,80,107,106,106,108,104,113,176,98,82", "endOffsets": "1006,1110,1218,1303,1404,1532,1618,1699,1791,1885,1982,2076,2176,2270,2366,2461,2553,2645,2726,2834,2941,3048,3157,3262,3376,3553,3652,18236"}}]}]}