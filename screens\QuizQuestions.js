import React, { useEffect, useState, useMemo, useCallback, useContext, useRef } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Vibration, ActivityIndicator, Alert } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage'; // Keep for level unlocking
import { useTranslation } from 'react-i18next';
import i18n from './localization'; // Import i18n instance
import { ThemeContext } from './ThemeContext';
import { Audio } from 'expo-av';
import { MotiView } from 'moti';
import { getAuth } from 'firebase/auth'; // Import Firebase Auth
// Import Firestore functions
import { getFirestore, doc, getDoc, setDoc, updateDoc, serverTimestamp } from 'firebase/firestore';
import quizDataRaw from '../data/questions.json'; // Import the JSON data
import InterstitialAdComponent from '../components/InterstitialAdComponent';
import { AD_PLACEMENTS } from '../src/config/adConfig';
import AdManager from '../src/services/AdManager';

// Fetches questions based on category and level
const getQuizQuestions = (category, level) => {
  if (!category || !level) {
    console.warn('Category or level not provided to getQuizQuestions');
    return []; // Return empty if category/level missing
  }
  // Filter questions by matching category and level
  const filteredQuestions = quizDataRaw.filter(
    (q) => q.category === category && q.level === level
  );
  // Return only the first 10 questions found for that category/level
  return filteredQuestions.slice(0, 10);
};

// Modified playSound to accept volume
const playSound = async (type, volume) => {
  // NOTE: soundEnabled check should happen *before* calling this function

  try {
    const soundFile =
      type === 'correct'
        ? require('../assets/audios/correct.mp3')
        : require('../assets/audios/wrong.mp3');

    const { sound } = await Audio.Sound.createAsync(soundFile);
    // Use the passed volume, ensuring it's between 0.0 and 1.0
    await sound.setVolumeAsync(Math.max(0.0, Math.min(1.0, volume)));
    await sound.playAsync();

    setTimeout(() => sound.unloadAsync(), 2500);
  } catch (error) {
    console.error('Error playing sound:', error);
  }
};

const unlockNextLevel = async (category, level) => {
  try {
    const nextLevel = level + 1;
    const storedProgress = await AsyncStorage.getItem(`progress_${category}`);
    const currentProgress = storedProgress ? parseInt(storedProgress, 10) : 1;
    if (nextLevel > currentProgress) {
      await AsyncStorage.setItem(`progress_${category}`, nextLevel.toString());
    }
  } catch (error) {
    console.error('Error unlocking next level:', error);
  }
};

const QuizQuestions = ({ route, navigation }) => {
  const { t } = useTranslation(); // Initialize translation hook
  // Get soundVolume from context
  const { darkMode, soundEnabled, soundVolume, fontSizeMultiplier } = useContext(ThemeContext);
  // Get category and level from route params, provide defaults if necessary
  const { category = 'Science', level = 1 } = route.params || {};
  // Load and filter questions based on category and level
  const filteredQuestions = useMemo(() => getQuizQuestions(category, level), [category, level]);
  const [questions] = useState(filteredQuestions); // Use the filtered questions
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedAnswer, setSelectedAnswer] = useState(null);
  const [showExplanation, setShowExplanation] = useState(false); // Re-added explanation state
  const [score, setScore] = useState(0);
  const [timeLeft, setTimeLeft] = useState(20);
  // Initialize loading to false since data is loaded synchronously
  const [loading, setLoading] = useState(false);

  // Interstitial ad ref
  const interstitialRef = useRef(null);

  // Determine current language suffix for data fields
  const langSuffix = i18n.language === 'ta' ? '_ta' : '';
  const currentQuestionData = questions[currentQuestionIndex];

  // Removed the useEffect hook that artificially delayed setting loading to false

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft((prev) => (prev > 0 ? prev - 1 : 0));
    }, 1000); // Changed interval to 1000ms (1 second)
    return () => clearInterval(timer);
  }, [currentQuestionIndex]);

  useEffect(() => {
    if (timeLeft === 0 && currentQuestionIndex < questions.length) { // Prevent calling nextQuestion if already finished
      nextQuestion();
    }
  }, [timeLeft, nextQuestion, currentQuestionIndex, questions.length]);

  useEffect(() => {
    if (currentQuestionIndex >= questions.length && questions.length > 0) { // Ensure questions were loaded
      unlockNextLevel(category, level);
      // Navigate back or to a results screen
      navigation.navigate('QuizLevels', { category });
    }
  }, [currentQuestionIndex, questions.length, category, level, navigation]); // Removed nextQuestion from dependencies as it's defined below

  // Function to save score to Firestore
  const saveScore = async (finalScore) => {
    const auth = getAuth();
    const user = auth.currentUser;

    if (!user) {
      console.error('User not logged in, cannot save score.');
      Alert.alert(t('error_title'), t('user_not_logged_in_error')); // Inform user
      return; // Exit if no user
    }

    const db = getFirestore();
    const userId = user.uid;
    const userName = user.displayName || user.email || 'Anonymous'; // Get user name or fallback
    const scoreRef = doc(db, 'userScores', userId); // Document ID is the user's UID

    console.log(`Attempting to save score ${finalScore} for user ${userId} (${userName})`);

    try {
      const docSnap = await getDoc(scoreRef);

      if (docSnap.exists()) {
        // Document exists, check if new score is higher
        const currentHighest = docSnap.data().highestScore || 0;
        console.log(`Existing highest score: ${currentHighest}`);
        if (finalScore > currentHighest) {
          console.log(`New score ${finalScore} is higher. Updating Firestore...`);
          await updateDoc(scoreRef, {
            highestScore: finalScore,
            userName: userName, // Update username in case it changed
            lastUpdated: serverTimestamp(), // Use server timestamp
            // Optionally store last category/level played
            lastCategory: category,
            lastLevel: level,
          });
          console.log('Firestore score updated successfully.');
        } else {
          console.log(`New score ${finalScore} is not higher than ${currentHighest}. No update needed.`);
          // Optionally update lastUpdated even if score isn't higher
          await updateDoc(scoreRef, {
             lastUpdated: serverTimestamp(),
             lastCategory: category,
             lastLevel: level,
          }).catch(err => console.warn("Minor error updating lastUpdated:", err)); // Log minor errors
        }
      } else {
        // Document doesn't exist, create it
        console.log(`No existing score found for user ${userId}. Creating new Firestore document...`);
        await setDoc(scoreRef, {
          userId: userId, // Explicitly store userId in the document too
          userName: userName,
          highestScore: finalScore,
          firstAchieved: serverTimestamp(), // Timestamp for first score
          lastUpdated: serverTimestamp(), // Timestamp for last update
          // Optionally store first category/level played
          firstCategory: category,
          firstLevel: level,
        });
        console.log('Firestore score document created successfully.');
      }
    } catch (error) {
      console.error('Failed to save score to Firestore:', error);
      Alert.alert(t('error_title'), t('score_save_error')); // Use translated alert
    }
  };

  const handleEndOfQuiz = useCallback(async () => {
    console.log(`Quiz ended. Final Score: ${score}`);
    await unlockNextLevel(category, level);
    await saveScore(score); // Save the score before navigating

    // Check if we should show interstitial ad
    if (AdManager.shouldShowInterstitial(AD_PLACEMENTS.QUIZ_INTERSTITIAL)) {
      // Show interstitial ad before navigation
      if (interstitialRef.current?.isLoaded()) {
        interstitialRef.current.showAd();
        return; // Navigation will happen in onAdDismissed callback
      }
    }

    // Navigate directly if no ad to show
    navigation.replace('QuizLevels', { category });
  }, [category, level, score, navigation, saveScore]);



  // Check if quiz ended
  useEffect(() => {
    // Check if quiz ended (moved from nextQuestion to handle timer ending on last question)
    if (currentQuestionIndex >= questions.length && questions.length > 0) {
      handleEndOfQuiz();
    }
  }, [currentQuestionIndex, questions.length, handleEndOfQuiz]);

  const nextQuestion = useCallback(() => {
     if (currentQuestionIndex < questions.length - 1) {
        setCurrentQuestionIndex((prev) => prev + 1);
        setSelectedAnswer(null);
        setShowExplanation(false); // Hide explanation for next question
        setTimeLeft(20); // Reset timer
     } else {
        // End of quiz
        handleEndOfQuiz();
     }
  }, [currentQuestionIndex, questions.length, handleEndOfQuiz]); // Use handleEndOfQuiz here

  const handleAnswer = useCallback(
    (answer) => {
      if (!currentQuestionData) return;
      setSelectedAnswer(answer);
      setShowExplanation(true); // Show explanation after answering

      // Get the correct answer field based on language
      const correctAnswerField = `correctAnswer${langSuffix}`;
      const isCorrect = answer === currentQuestionData[correctAnswerField];

      // Check soundEnabled before calling playSound and pass the volume
      if (soundEnabled) {
         playSound(isCorrect ? 'correct' : 'wrong', soundVolume);
      }
      if (isCorrect) {
        setScore((prev) => prev + 1);
      }
      // Optionally add vibration
      Vibration.vibrate(isCorrect ? 100 : [100, 100]);
    },
    // Added soundVolume to dependencies
    [currentQuestionData, soundEnabled, langSuffix, soundVolume]
  );

  // Show loading indicator while loading is true
  if (loading) {
    return (
      <View style={[styles.container, { backgroundColor: darkMode ? '#121212' : '#f0f0f0' }]}>
        <ActivityIndicator size="large" color={darkMode ? '#fff' : '#000'} />
        <Text style={{ color: darkMode ? '#fff' : '#000', marginTop: 10 }}>{t('loading_questions')}</Text>
      </View>
    );
  }

  // Handle case where filtering resulted in no questions AFTER loading
  if (!loading && (!questions || questions.length === 0)) {
    return (
      <View style={[styles.container, { backgroundColor: darkMode ? '#121212' : '#f0f0f0' }]}>
        {/* Show specific message when no questions are found */}
        <Text style={{ color: darkMode ? '#fff' : '#000', marginTop: 10, textAlign: 'center' }}>
          {t('no_questions_found')}
        </Text>
      </View>
    );
  }

  // Handle potential issue where currentQuestionData might be undefined (e.g., index out of bounds)
  // This check might be redundant if the previous check handles empty questions array, but good for safety.
  if (!currentQuestionData) {
     // This case should ideally not be reached if the above checks are correct
     console.error("Error: currentQuestionData is undefined despite questions array having items.");
     // Navigate back or show an error message
     navigation.goBack();
     return null; // Return null to render nothing while navigating back
  }

  // If we reach here, loading is false, questions array is not empty, and currentQuestionData is valid.

  return (
    <MotiView
      from={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 500 }}
      // Apply dynamic background color inline
      style={[styles.container, { backgroundColor: darkMode ? '#121212' : '#f0f0f0' }]}
    >
      {/* Apply dynamic text color and font size inline */}
      <Text style={[styles.title, { fontSize: styles.title.fontSize * fontSizeMultiplier, color: darkMode ? '#fff' : '#333' }]}>
        {category} - {t('level')} {level}
      </Text>
      {/* Apply dynamic text color and font size inline */}
      <Text style={[styles.progress, { fontSize: styles.progress.fontSize * fontSizeMultiplier, color: darkMode ? '#ccc' : '#555' }]}>
        {t('question_progress', { current: currentQuestionIndex + 1, total: questions.length })}
      </Text>
      <Text style={[styles.timer, { fontSize: styles.timer.fontSize * fontSizeMultiplier }]}>{t('time_left', { seconds: timeLeft })}</Text>
      {/* Apply dynamic text color and font size inline */}
      {/* Select question text based on language */}
      <Text style={[styles.question, { fontSize: styles.question.fontSize * fontSizeMultiplier, color: darkMode ? '#eee' : '#222' }]}>
        {String(currentQuestionData?.[`question${langSuffix}`] || currentQuestionData?.question || 'Loading question...')}
      </Text>
      {/* Select options based on language */}
      {(currentQuestionData?.[`options${langSuffix}`] || currentQuestionData?.options)?.map((option, index) => {
        const correctAnswerField = `correctAnswer${langSuffix}`;
        const isCorrectOption = option === currentQuestionData?.[correctAnswerField];
        const isSelectedOption = selectedAnswer === option;

        return (
          <TouchableOpacity
            key={index}
            style={[
              styles.optionButton,
              isSelectedOption
              ? isCorrectOption ? styles.correctOption : styles.wrongOption
              : null,
            // Highlight correct answer after selection if the selected one was wrong
            selectedAnswer !== null && !isSelectedOption && isCorrectOption
              ? styles.correctOption
              : null,
            ]}
            onPress={() => handleAnswer(option)}
            disabled={selectedAnswer !== null}
          >
            <Text style={[styles.optionText, { fontSize: styles.optionText.fontSize * fontSizeMultiplier }]}>{String(option || 'Option')}</Text>
          </TouchableOpacity>
        );
      })}
      {/* Show Explanation Section if an answer is selected */}
      {showExplanation && currentQuestionData && ( // Also check currentQuestionData here
        <>
          <Text style={[styles.explanation, { fontSize: styles.explanation.fontSize * fontSizeMultiplier, color: darkMode ? '#111' : '#fff' }]}>
            {String(currentQuestionData?.[`explanation${langSuffix}`] || currentQuestionData?.explanation || 'No explanation available')}
          </Text>
          <TouchableOpacity onPress={nextQuestion} style={styles.nextButton}>
            <Text style={[styles.optionText, { fontSize: styles.optionText.fontSize * fontSizeMultiplier }]}>
              {currentQuestionIndex === questions.length - 1 ? t('finish_quiz') : t('next_question')}
           </Text>
         </TouchableOpacity>
        </> // <-- Add missing closing fragment tag
       )}

      {/* Interstitial Ad Component */}
      <InterstitialAdComponent
        ref={interstitialRef}
        placement={AD_PLACEMENTS.QUIZ_INTERSTITIAL}
        onAdDismissed={() => {
          // Navigate after ad is dismissed
          navigation.replace('QuizLevels', { category });
        }}
        onAdFailedToShow={() => {
          // Navigate if ad fails to show
          navigation.replace('QuizLevels', { category });
        }}
      />
    </MotiView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    // backgroundColor: '#f0f0f0', // Removed as it's applied inline
    padding: 20,
  },
  title: {
    fontSize: 26,
    fontWeight: 'bold',
    marginBottom: 10,
    // color: '#333', // Removed
  },
  progress: {
    fontSize: 18, // Base size
    fontWeight: 'bold',
    marginBottom: 5,
    // color: '#555', // Removed
  },
  timer: {
    fontSize: 18, // Base size
    color: 'red',
    marginBottom: 10,
  },
  question: {
    fontSize: 22, // Base size
    fontWeight: 'bold',
    textAlign: 'center',
    marginVertical: 15,
    // color: '#222', // Removed
  },
  optionButton: {
    padding: 15,
    backgroundColor: '#3498db', // Keep button base color consistent
    marginVertical: 5,
    borderRadius: 10,
    width: '80%',
    alignItems: 'center',
  },
  correctOption: {
    backgroundColor: '#2ecc71', // Keep feedback colors consistent
  },
  wrongOption: {
    backgroundColor: '#e74c3c', // Keep feedback colors consistent
  },
  optionText: {
    color: '#fff',
    fontSize: 18, // Base size
  },
  explanation: {
    fontSize: 18, // Base size
    fontStyle: 'italic',
    // color: '#ffffff', // Color applied dynamically
    marginTop: 15,
    textAlign: 'center',
    paddingHorizontal: 10,
    backgroundColor: '#f39c12', // Keep explanation background consistent
    padding: 10,
    borderRadius: 10,
    width: '80%', // Match button width
  },
  nextButton: {
    marginTop: 15, // Adjusted margin
    backgroundColor: '#1abc9c', // Keep next button color consistent
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
    width: '80%',
  },
});

export default QuizQuestions;
