import React, { useContext, useRef, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Animated } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { ThemeContext } from './ThemeContext';
import { useNavigation } from '@react-navigation/native';
import BannerAdComponent from '../components/BannerAdComponent';
import { AD_PLACEMENTS } from '../src/config/adConfig';

const StudyToolsScreen = () => {
  const { darkMode, fontSizeMultiplier } = useContext(ThemeContext);
  const navigation = useNavigation();
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;

  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 4,
        useNativeDriver: true,
      }),
    ]).start();
  }, [fadeAnim, scaleAnim]);

  const studyTools = [
    {
      id: 'flashcards',
      title: 'Flashcards',
      description: 'Quick revision with interactive flashcards',
      icon: 'card-outline',
      colors: ['#FF6B6B', '#FF8E8E'],
      screen: 'Flashcards'
    },
    {
      id: 'formulas',
      title: 'Formula Sheets',
      description: 'Quick reference for important formulas',
      icon: 'calculator-outline',
      colors: ['#4ECDC4', '#44A08D'],
      screen: 'FormulaSheets'
    },
    {
      id: 'practice-tests',
      title: 'Practice Tests',
      description: 'Timed practice tests for exam preparation',
      icon: 'timer-outline',
      colors: ['#45B7D1', '#96C93D'],
      screen: 'PracticeTests'
    },
    {
      id: 'mock-exams',
      title: 'Mock Exams',
      description: 'Full-length mock examinations',
      icon: 'school-outline',
      colors: ['#F093FB', '#F5576C'],
      screen: 'MockExams'
    },
    {
      id: 'adaptive-learning',
      title: 'AI Study Plan',
      description: 'Personalized study recommendations',
      icon: 'bulb-outline',
      colors: ['#FFA726', '#FF7043'],
      screen: 'AdaptiveLearning'
    },
    {
      id: 'spaced-repetition',
      title: 'Spaced Repetition',
      description: 'Smart revision scheduling system',
      icon: 'refresh-outline',
      colors: ['#AB47BC', '#8E24AA'],
      screen: 'SpacedRepetition'
    }
  ];

  const handleToolPress = (tool) => {
    navigation.navigate(tool.screen, { category: 'NEET' });
  };

  const renderToolCard = (tool, index) => {
    return (
      <Animated.View
        key={tool.id}
        style={[
          styles.toolCard,
          { 
            opacity: fadeAnim,
            transform: [{ scale: scaleAnim }]
          }
        ]}
      >
        <TouchableOpacity
          onPress={() => handleToolPress(tool)}
          style={styles.toolButton}
          activeOpacity={0.8}
        >
          <LinearGradient
            colors={tool.colors}
            style={styles.toolGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            <View style={styles.toolIconContainer}>
              <Ionicons 
                name={tool.icon} 
                size={32} 
                color="#FFFFFF" 
              />
            </View>
            <View style={styles.toolTextContainer}>
              <Text style={[
                styles.toolTitle,
                { fontSize: 18 * fontSizeMultiplier }
              ]}>
                {tool.title}
              </Text>
              <Text style={[
                styles.toolDescription,
                { fontSize: 14 * fontSizeMultiplier }
              ]}>
                {tool.description}
              </Text>
            </View>
            <View style={styles.toolArrow}>
              <Ionicons 
                name="chevron-forward" 
                size={24} 
                color="#FFFFFF" 
              />
            </View>
          </LinearGradient>
        </TouchableOpacity>
      </Animated.View>
    );
  };

  return (
    <View style={[styles.container, darkMode ? styles.darkContainer : styles.lightContainer]}>
      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.header}>
          <Text style={[
            styles.headerTitle,
            { 
              color: darkMode ? '#FFFFFF' : '#333333',
              fontSize: 28 * fontSizeMultiplier
            }
          ]}>
            Study Tools
          </Text>
          <Text style={[
            styles.headerSubtitle,
            { 
              color: darkMode ? '#CCCCCC' : '#666666',
              fontSize: 16 * fontSizeMultiplier
            }
          ]}>
            Enhance your learning with powerful study tools
          </Text>
        </View>

        <View style={styles.toolsContainer}>
          {studyTools.map((tool, index) => renderToolCard(tool, index))}
        </View>
      </ScrollView>

      {/* Banner Ad */}
      <View style={styles.adContainer}>
        <BannerAdComponent
          placement={AD_PLACEMENTS.SETTINGS_BANNER}
          fallbackToWebView={true}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  darkContainer: {
    backgroundColor: '#121212',
  },
  lightContainer: {
    backgroundColor: '#F5F5F5',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 20,
  },
  header: {
    padding: 20,
    paddingTop: 40,
    alignItems: 'center',
  },
  headerTitle: {
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  headerSubtitle: {
    textAlign: 'center',
    lineHeight: 22,
  },
  toolsContainer: {
    paddingHorizontal: 16,
  },
  toolCard: {
    marginBottom: 16,
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  toolButton: {
    width: '100%',
  },
  toolGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    minHeight: 80,
  },
  toolIconContainer: {
    marginRight: 16,
  },
  toolTextContainer: {
    flex: 1,
  },
  toolTitle: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    marginBottom: 4,
  },
  toolDescription: {
    color: '#FFFFFF',
    opacity: 0.9,
    lineHeight: 20,
  },
  toolArrow: {
    marginLeft: 12,
  },
  adContainer: {
    width: '100%',
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
});

export default StudyToolsScreen;
