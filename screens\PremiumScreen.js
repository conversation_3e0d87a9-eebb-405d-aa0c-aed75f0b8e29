import React, { useState, useEffect, useContext, useRef } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Alert, Animated } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { ThemeContext } from './ThemeContext';
import { useNavigation } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import BannerAdComponent from '../components/BannerAdComponent';
import { AD_PLACEMENTS } from '../src/config/adConfig';

const PremiumScreen = () => {
  const { darkMode, fontSizeMultiplier } = useContext(ThemeContext);
  const navigation = useNavigation();

  const [isPremium, setIsPremium] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState('monthly');
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;

  useEffect(() => {
    checkPremiumStatus();
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 4,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const checkPremiumStatus = async () => {
    try {
      const premiumStatus = await AsyncStorage.getItem('premium_status');
      setIsPremium(premiumStatus === 'true');
    } catch (error) {
      console.error('Error checking premium status:', error);
    }
  };

  const premiumFeatures = [
    {
      icon: 'remove-circle-outline',
      title: 'Ad-Free Experience',
      description: 'Study without interruptions - no more ads!',
      color: '#FF6B6B'
    },
    {
      icon: 'chatbubbles-outline',
      title: 'Unlimited AI Chat',
      description: 'Ask Nijeta unlimited questions and get instant help',
      color: '#4ECDC4'
    },
    {
      icon: 'analytics-outline',
      title: 'Advanced Analytics',
      description: 'Detailed performance insights and progress tracking',
      color: '#45B7D1'
    },
    {
      icon: 'headset-outline',
      title: 'Priority Support',
      description: '24/7 premium support with faster response times',
      color: '#96C93D'
    },
    {
      icon: 'star-outline',
      title: 'Exclusive Content',
      description: 'Access to premium study materials and mock tests',
      color: '#F093FB'
    },
    {
      icon: 'flash-outline',
      title: 'Early Access',
      description: 'Be the first to try new features and updates',
      color: '#FFA726'
    },
    {
      icon: 'cloud-download-outline',
      title: 'Offline Mode',
      description: 'Download content for offline studying',
      color: '#AB47BC'
    },
    {
      icon: 'people-outline',
      title: 'Study Groups',
      description: 'Join exclusive premium study groups and forums',
      color: '#FF7043'
    }
  ];

  const pricingPlans = [
    {
      id: 'monthly',
      title: 'Monthly',
      price: '₹299',
      period: '/month',
      savings: null,
      popular: false
    },
    {
      id: 'yearly',
      title: 'Yearly',
      price: '₹1999',
      period: '/year',
      savings: 'Save 44%',
      popular: true
    },
    {
      id: 'lifetime',
      title: 'Lifetime',
      price: '₹4999',
      period: 'one-time',
      savings: 'Best Value',
      popular: false
    }
  ];

  const handlePurchase = async (planId) => {
    try {
      // Simulate purchase process
      Alert.alert(
        'Purchase Confirmation',
        `Are you sure you want to purchase the ${pricingPlans.find(p => p.id === planId)?.title} plan?`,
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Purchase',
            onPress: async () => {
              // Simulate successful purchase
              await AsyncStorage.setItem('premium_status', 'true');
              await AsyncStorage.setItem('premium_plan', planId);
              await AsyncStorage.setItem('premium_purchase_date', new Date().toISOString());

              setIsPremium(true);

              Alert.alert(
                'Welcome to Premium! 🎉',
                'Thank you for upgrading! You now have access to all premium features.',
                [{ text: 'Awesome!', onPress: () => navigation.goBack() }]
              );
            }
          }
        ]
      );
    } catch (error) {
      console.error('Error processing purchase:', error);
      Alert.alert('Error', 'Failed to process purchase. Please try again.');
    }
  };

  const restorePurchases = async () => {
    try {
      // Simulate restore process
      Alert.alert('Restore Purchases', 'No previous purchases found to restore.');
    } catch (error) {
      console.error('Error restoring purchases:', error);
      Alert.alert('Error', 'Failed to restore purchases. Please try again.');
    }
  };

  const renderFeatureCard = (feature, index) => (
    <Animated.View
      key={index}
      style={[
        styles.featureCard,
        {
          backgroundColor: darkMode ? '#2A2A2A' : '#FFFFFF',
          opacity: fadeAnim,
          transform: [{ scale: scaleAnim }]
        }
      ]}
    >
      <View style={[styles.featureIcon, { backgroundColor: feature.color }]}>
        <Ionicons name={feature.icon} size={24} color="#FFFFFF" />
      </View>

      <View style={styles.featureContent}>
        <Text style={[
          styles.featureTitle,
          {
            color: darkMode ? '#FFFFFF' : '#333333',
            fontSize: 16 * fontSizeMultiplier
          }
        ]}>
          {feature.title}
        </Text>
        <Text style={[
          styles.featureDescription,
          {
            color: darkMode ? '#CCCCCC' : '#666666',
            fontSize: 14 * fontSizeMultiplier
          }
        ]}>
          {feature.description}
        </Text>
      </View>

      <Ionicons
        name="checkmark-circle"
        size={24}
        color={isPremium ? '#4CAF50' : '#CCCCCC'}
      />
    </Animated.View>
  );

  const renderPricingCard = (plan) => (
    <Animated.View
      key={plan.id}
      style={[
        styles.pricingCard,
        {
          backgroundColor: darkMode ? '#2A2A2A' : '#FFFFFF',
          opacity: fadeAnim,
          transform: [{ scale: scaleAnim }]
        },
        selectedPlan === plan.id && styles.selectedPricingCard,
        plan.popular && styles.popularPricingCard
      ]}
    >
      <TouchableOpacity
        onPress={() => setSelectedPlan(plan.id)}
        style={styles.pricingCardContent}
      >
        {plan.popular && (
          <View style={styles.popularBadge}>
            <Text style={[styles.popularBadgeText, { fontSize: 12 * fontSizeMultiplier }]}>
              MOST POPULAR
            </Text>
          </View>
        )}

        <Text style={[
          styles.planTitle,
          {
            color: darkMode ? '#FFFFFF' : '#333333',
            fontSize: 18 * fontSizeMultiplier
          }
        ]}>
          {plan.title}
        </Text>

        <View style={styles.priceContainer}>
          <Text style={[
            styles.price,
            {
              color: plan.popular ? '#FFFFFF' : (darkMode ? '#FFFFFF' : '#333333'),
              fontSize: 24 * fontSizeMultiplier
            }
          ]}>
            {plan.price}
          </Text>
          <Text style={[
            styles.period,
            {
              color: plan.popular ? '#FFFFFF' : (darkMode ? '#CCCCCC' : '#666666'),
              fontSize: 14 * fontSizeMultiplier
            }
          ]}>
            {plan.period}
          </Text>
        </View>

        {plan.savings && (
          <View style={[
            styles.savingsBadge,
            { backgroundColor: plan.popular ? 'rgba(255, 255, 255, 0.2)' : '#4CAF50' }
          ]}>
            <Text style={[
              styles.savingsText,
              { fontSize: 12 * fontSizeMultiplier }
            ]}>
              {plan.savings}
            </Text>
          </View>
        )}

        <View style={styles.radioButton}>
          <View style={[
            styles.radioOuter,
            { borderColor: selectedPlan === plan.id ? '#667eea' : '#CCCCCC' }
          ]}>
            {selectedPlan === plan.id && <View style={styles.radioInner} />}
          </View>
        </View>
      </TouchableOpacity>
    </Animated.View>
  );

  if (isPremium) {
    return (
      <View style={[styles.container, darkMode ? styles.darkContainer : styles.lightContainer]}>
        <ScrollView contentContainerStyle={styles.premiumUserContent}>
          <Animated.View style={[styles.premiumBadge, { opacity: fadeAnim }]}>
            <LinearGradient
              colors={['#FFD700', '#FFA500']}
              style={styles.premiumBadgeGradient}
            >
              <Ionicons name="star" size={32} color="#FFFFFF" />
              <Text style={[styles.premiumBadgeText, { fontSize: 20 * fontSizeMultiplier }]}>
                Premium Member
              </Text>
            </LinearGradient>
          </Animated.View>

          <Text style={[
            styles.premiumWelcome,
            {
              color: darkMode ? '#FFFFFF' : '#333333',
              fontSize: 18 * fontSizeMultiplier
            }
          ]}>
            Welcome to Premium! 🎉
          </Text>

          <Text style={[
            styles.premiumDescription,
            {
              color: darkMode ? '#CCCCCC' : '#666666',
              fontSize: 16 * fontSizeMultiplier
            }
          ]}>
            You have access to all premium features. Enjoy your enhanced learning experience!
          </Text>

          <View style={styles.premiumFeaturesList}>
            {premiumFeatures.map((feature, index) => renderFeatureCard(feature, index))}
          </View>

          <TouchableOpacity
            style={styles.manageSubscriptionButton}
            onPress={() => Alert.alert('Manage Subscription', 'Subscription management coming soon!')}
          >
            <Text style={[styles.manageSubscriptionText, { fontSize: 16 * fontSizeMultiplier }]}>
              Manage Subscription
            </Text>
          </TouchableOpacity>
        </ScrollView>
      </View>
    );
  }

  return (
    <View style={[styles.container, darkMode ? styles.darkContainer : styles.lightContainer]}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* Header */}
        <Animated.View style={[styles.header, { opacity: fadeAnim }]}>
          <LinearGradient
            colors={['#667eea', '#764ba2']}
            style={styles.headerGradient}
          >
            <Ionicons name="star" size={48} color="#FFFFFF" />
            <Text style={[styles.headerTitle, { fontSize: 28 * fontSizeMultiplier }]}>
              Upgrade to Premium
            </Text>
            <Text style={[styles.headerSubtitle, { fontSize: 16 * fontSizeMultiplier }]}>
              Unlock all features and supercharge your learning
            </Text>
          </LinearGradient>
        </Animated.View>

        {/* Features */}
        <View style={styles.featuresSection}>
          <Text style={[
            styles.sectionTitle,
            {
              color: darkMode ? '#FFFFFF' : '#333333',
              fontSize: 22 * fontSizeMultiplier
            }
          ]}>
            Premium Features
          </Text>

          <View style={styles.featuresList}>
            {premiumFeatures.map((feature, index) => renderFeatureCard(feature, index))}
          </View>
        </View>

        {/* Pricing */}
        <View style={styles.pricingSection}>
          <Text style={[
            styles.sectionTitle,
            {
              color: darkMode ? '#FFFFFF' : '#333333',
              fontSize: 22 * fontSizeMultiplier
            }
          ]}>
            Choose Your Plan
          </Text>

          <View style={styles.pricingCards}>
            {pricingPlans.map(plan => renderPricingCard(plan))}
          </View>

          <TouchableOpacity
            style={styles.purchaseButton}
            onPress={() => handlePurchase(selectedPlan)}
          >
            <LinearGradient
              colors={['#f093fb', '#f5576c']}
              style={styles.purchaseButtonGradient}
            >
              <Text style={[styles.purchaseButtonText, { fontSize: 18 * fontSizeMultiplier }]}>
                Start Premium
              </Text>
              <Ionicons name="arrow-forward" size={20} color="#FFFFFF" />
            </LinearGradient>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.restoreButton}
            onPress={restorePurchases}
          >
            <Text style={[
              styles.restoreButtonText,
              {
                color: darkMode ? '#CCCCCC' : '#666666',
                fontSize: 14 * fontSizeMultiplier
              }
            ]}>
              Restore Purchases
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>

      {/* Enhanced Banner Ad - Only show for non-premium users */}
      {!isPremium && (
        <View style={styles.adContainer}>
          <BannerAdComponent
            placement={AD_PLACEMENTS.PREMIUM_BANNER}
            fallbackToWebView={true}
            enableRefresh={true}
          />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  darkContainer: {
    backgroundColor: '#121212',
  },
  lightContainer: {
    backgroundColor: '#F5F5F5',
  },
  scrollContent: {
    paddingBottom: 20,
  },
  header: {
    marginBottom: 30,
  },
  headerGradient: {
    paddingVertical: 40,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  headerTitle: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  headerSubtitle: {
    color: '#FFFFFF',
    opacity: 0.9,
    textAlign: 'center',
    lineHeight: 22,
  },
  featuresSection: {
    paddingHorizontal: 20,
    marginBottom: 30,
  },
  sectionTitle: {
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  featuresList: {
    gap: 12,
  },
  featureCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  featureIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  featureContent: {
    flex: 1,
  },
  featureTitle: {
    fontWeight: 'bold',
    marginBottom: 4,
  },
  featureDescription: {
    lineHeight: 20,
  },
  pricingSection: {
    paddingHorizontal: 20,
  },
  pricingCards: {
    gap: 16,
    marginBottom: 30,
  },
  pricingCard: {
    borderRadius: 16,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedPricingCard: {
    borderColor: '#667eea',
  },
  popularPricingCard: {
    borderColor: '#f093fb',
  },
  pricingCardContent: {
    padding: 20,
    alignItems: 'center',
    position: 'relative',
  },
  popularBadge: {
    position: 'absolute',
    top: -10,
    backgroundColor: '#f093fb',
    paddingHorizontal: 16,
    paddingVertical: 4,
    borderRadius: 12,
  },
  popularBadgeText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  planTitle: {
    fontWeight: 'bold',
    marginBottom: 12,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
    marginBottom: 12,
  },
  price: {
    fontWeight: 'bold',
  },
  period: {
    marginLeft: 4,
  },
  savingsBadge: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
    marginBottom: 16,
  },
  savingsText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  radioButton: {
    position: 'absolute',
    top: 20,
    right: 20,
  },
  radioOuter: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#667eea',
  },
  purchaseButton: {
    borderRadius: 25,
    overflow: 'hidden',
    marginBottom: 16,
  },
  purchaseButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 32,
  },
  purchaseButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    marginRight: 8,
  },
  restoreButton: {
    alignItems: 'center',
    paddingVertical: 12,
  },
  restoreButtonText: {
    textDecorationLine: 'underline',
  },
  premiumUserContent: {
    padding: 20,
    alignItems: 'center',
  },
  premiumBadge: {
    marginBottom: 30,
    borderRadius: 20,
    overflow: 'hidden',
  },
  premiumBadgeGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
  },
  premiumBadgeText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    marginLeft: 12,
  },
  premiumWelcome: {
    fontWeight: 'bold',
    marginBottom: 12,
    textAlign: 'center',
  },
  premiumDescription: {
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 30,
  },
  premiumFeaturesList: {
    width: '100%',
    gap: 12,
    marginBottom: 30,
  },
  manageSubscriptionButton: {
    backgroundColor: '#667eea',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 25,
  },
  manageSubscriptionText: {
    color: '#FFFFFF',
    fontWeight: '500',
  },
  adContainer: {
    width: '100%',
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
});

export default PremiumScreen;
