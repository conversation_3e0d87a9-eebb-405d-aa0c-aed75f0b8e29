import React, { useContext, useState, useEffect, lazy, Suspense } from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { createDrawerNavigator, DrawerContentScrollView, DrawerItemList } from '@react-navigation/drawer';
import { NavigationContainer } from '@react-navigation/native'; // Removed useNavigation (not used here)
import { View, Text, StyleSheet, ActivityIndicator } from 'react-native'; // Removed TouchableOpacity, Alert
import { Ionicons } from '@expo/vector-icons';
import { useFonts, Poppins_400Regular, Poppins_500Medium, Poppins_600SemiBold, Poppins_700Bold } from '@expo-google-fonts/poppins'; // Import useFonts and Poppins
import * as SplashScreen from 'expo-splash-screen'; // Import SplashScreen
// Removed AsyncStorage import as theme loading/saving is handled in ThemeContext.js
import { auth } from './firebaseConfig'; // Import Firebase auth
import { onAuthStateChanged } from 'firebase/auth'; // Removed signOut, Import onAuthStateChanged

// Import the consolidated ThemeProvider and ThemeContext
import { ThemeProvider, ThemeContext } from './screens/ThemeContext';

// Import AdMob initialization
import { initializeAdMob } from './services/AdMobService';
import AdManager from './src/services/AdManager';

import HomeScreen from './screens/HomeScreen';
import QuizScreen from './screens/QuizScreen';
import QuizLevels from './screens/QuizLevels';
import QuizQuestions from './screens/QuizQuestions';
import SettingsScreen from './screens/SettingsScreen'; // Import the actual SettingsScreen
import LoginScreen from './screens/LoginScreen';
import RegisterScreen from './screens/RegisterScreen';
import ForgotPasswordScreen from './screens/ForgotPasswordScreen';
import ChangePasswordScreen from './screens/ChangePasswordScreen';
import AiChatScreen from './screens/AiChatScreen'; // Import AiChatScreen
// Import Study Tools screens
import StudyToolsScreen from './screens/StudyToolsScreen';
import FlashcardsScreen from './screens/FlashcardsScreen';
import FormulaSheetsScreen from './screens/FormulaSheetsScreen';
import PracticeTestsScreen from './screens/PracticeTestsScreen';
import MockExamsScreen from './screens/MockExamsScreen';
import AdaptiveLearningScreen from './screens/AdaptiveLearningScreen';
import SpacedRepetitionScreen from './screens/SpacedRepetitionScreen';
// Import Content Organization screens
import FavoritesScreen from './screens/FavoritesScreen';
import StudyPlaylistsScreen from './screens/StudyPlaylistsScreen';
import NotesScreen from './screens/NotesScreen';
import PremiumScreen from './screens/PremiumScreen';
// Import notification service
import notificationService from './services/NotificationService';
// Lazy load heavier/less common screens
const LazyChapterScreen = lazy(() => import('./screens/ChapterScreen'));
const LazyVideoChapterScreen = lazy(() => import('./screens/VideoChapterScreen'));
const LazyBookUnitsScreen = lazy(() => import('./screens/BookUnitsScreen'));
const LazyPdfViewerScreen = lazy(() => import('./screens/PdfViewerScreen')); // Import PdfViewerScreen
// Keep essential/frequent screens imported directly for potentially faster access after initial load
// import ChapterScreen from './screens/ChapterScreen';
// import VideoChapterScreen from './screens/VideoChapterScreen';
// import BookUnitsScreen from './screens/BookUnitsScreen';


const Stack = createStackNavigator();
const Drawer = createDrawerNavigator();
// Removed local ThemeContext definition

// Removed local ThemeProvider definition

const CustomDrawerContent = (props) => {
  // Use the imported ThemeContext
  const { darkMode } = useContext(ThemeContext);
  // Removed unused navigation variable
  // Removed unused navigation variable
  // Removed unused handleLogout function

  return (
    // Use darkMode from the context
    <DrawerContentScrollView {...props} style={[styles.drawerContainer, { backgroundColor: darkMode ? '#121212' : '#fff' }]}>
      <View style={styles.drawerHeader}>
        {/* Use darkMode from the context */}
        <Text style={[styles.userName, { color: darkMode ? '#fff' : '#000' }]}>BeeTech</Text>
        <Text style={[styles.userEmail, { color: darkMode ? '#aaa' : '#555' }]}>Welcome to the App</Text>
      </View>
      <DrawerItemList {...props} />
    </DrawerContentScrollView>
  );
};

// Removed SettingsScreenUpdated component definition

const QuizStackNavigator = () => (
  <Stack.Navigator>
    <Stack.Screen name="QuizScreen" component={QuizScreen} options={{ title: 'Select Category' }} />
    <Stack.Screen name="QuizLevels" component={QuizLevels} options={{ title: 'Select Level' }} />
    <Stack.Screen name="QuizQuestions" component={QuizQuestions} options={{ title: 'Quiz Questions' }} />
  </Stack.Navigator>
);

const MainDrawerNavigator = () => {
  const { darkMode } = useContext(ThemeContext);

  return (
    <Drawer.Navigator
      drawerContent={(props) => <CustomDrawerContent {...props} />}
      screenOptions={{
        drawerStyle: { backgroundColor: darkMode ? '#121212' : '#fff' },
        drawerLabelStyle: { color: darkMode ? '#fff' : '#000' },
        drawerActiveTintColor: darkMode ? '#81b0ff' : '#000',
        drawerInactiveTintColor: darkMode ? '#aaa' : '#000',
      }}
    >
      <Drawer.Screen name="Home" component={HomeScreen} options={{ drawerIcon: ({ size, color }) => <Ionicons name="home" size={size} color={color} /> }} />
      <Drawer.Screen name="Quiz" component={QuizStackNavigator} options={{ drawerIcon: ({ size, color }) => <Ionicons name="game-controller-outline" size={size} color={color} /> }} />
      <Drawer.Screen name="Settings" component={SettingsScreen} options={{ drawerIcon: ({ size, color }) => <Ionicons name="settings" size={size} color={color} /> }} />
    </Drawer.Navigator>
  );
};

function RootNavigator() {
  const [authInitializing, setAuthInitializing] = useState(true); // Renamed for clarity
  const [user, setUser] = useState(null);

  // Load Fonts with error handling
  let [fontsLoaded, fontError] = useFonts({
    Poppins_400Regular,
    Poppins_500Medium,
    Poppins_600SemiBold,
    Poppins_700Bold,
  });

  // If font loading fails, continue with system fonts
  const shouldUseFonts = fontsLoaded && !fontError;

  // Initialize AdMob and AdManager
  useEffect(() => {
    console.log("Initializing AdMob and AdManager...");

    // Initialize AdMob first
    initializeAdMob()
      .then(() => {
        console.log("AdMob initialized successfully");
        // Then initialize AdManager
        return AdManager.initialize();
      })
      .then(() => {
        console.log("AdManager initialized successfully");
      })
      .catch((error) => {
        console.error("Ad initialization failed:", error);
      });
  }, []);

  // Initialize notification service
  // useEffect(() => {
  //   const initializeNotifications = async () => {
  //     try {
  //       await notificationService.initialize();
  //       console.log('Notification service initialized successfully');
  //     } catch (error) {
  //       console.error('Failed to initialize notification service:', error);
  //     }
  //   };

  //   initializeNotifications();

  //   // Cleanup on unmount
  //   return () => {
  //     notificationService.cleanup();
  //   };
  // }, []);

  // Handle user state changes (Firebase Auth)
  useEffect(() => {
    console.log("Setting up Firebase auth state listener...");
    const subscriber = onAuthStateChanged(auth, (user) => {
      console.log("onAuthStateChanged fired. User:", user ? user.uid : null);
      setUser(user);
      if (authInitializing) {
        console.log("Setting authInitializing to false.");
        setAuthInitializing(false);
      }
    });
    return () => {
      console.log("Unsubscribing Firebase auth state listener.");
      subscriber(); // unsubscribe on unmount
    };
  }, []); // Empty dependency array: run only on mount

  // Prevent splash screen from hiding until fonts are loaded and auth is checked
  useEffect(() => {
    async function prepare() {
      await SplashScreen.preventAutoHideAsync();
    }
    prepare();
  }, []);

  // Hide splash screen once fonts are loaded/error and auth is initialized
  useEffect(() => {
    if ((fontsLoaded || fontError) && !authInitializing) {
      async function hideSplash() {
        await SplashScreen.hideAsync();
      }
      hideSplash();
    }
  }, [fontsLoaded, fontError, authInitializing]);

  // Show loading indicator if fonts aren't loaded/error or auth is still initializing
  if ((!fontsLoaded && !fontError) || authInitializing) {
    return null; // Return null while splash screen is visible
  }

  // Log font loading error if it occurs but continue with app
  if (fontError) {
    console.warn("Font Loading Error (continuing with system fonts):", fontError);
    // Continue with system fonts - don't block the app
  }

 // Simple loading component for Suspense fallback
 const LoadingIndicator = () => (
   <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
     <ActivityIndicator size="large" />
   </View>
 );

 return (
   <NavigationContainer>
     {/* Wrap the entire navigator in Suspense to handle lazy loading */}
     <Suspense fallback={<LoadingIndicator />}>
       <Stack.Navigator>
         {user ? (
           // User is signed in, show main app with drawer
          <>
            <Stack.Screen
              name="MainApp"
              component={MainDrawerNavigator}
              options={{ headerShown: false }}
            />
            {/* Keep other screens accessible from MainApp */}
            <Stack.Screen
              name="ChangePassword"
              component={ChangePasswordScreen}
              options={{ headerShown: false }} // Screen has its own header/back button
            />
            <Stack.Screen
              name="AiChat"
              component={AiChatScreen}
              options={{ title: 'Ask AI Doubt' }} // Set a title for the header
            />
            {/* Study Tools Screens */}
            <Stack.Screen
              name="StudyTools"
              component={StudyToolsScreen}
              options={{ title: 'Study Tools' }}
            />
            <Stack.Screen
              name="Flashcards"
              component={FlashcardsScreen}
              options={{ title: 'Flashcards' }}
            />
            <Stack.Screen
              name="FormulaSheets"
              component={FormulaSheetsScreen}
              options={{ title: 'Formula Sheets' }}
            />
            <Stack.Screen
              name="PracticeTests"
              component={PracticeTestsScreen}
              options={{ title: 'Practice Tests' }}
            />
            <Stack.Screen
              name="MockExams"
              component={MockExamsScreen}
              options={{ title: 'Mock Exams' }}
            />
            <Stack.Screen
              name="AdaptiveLearning"
              component={AdaptiveLearningScreen}
              options={{ title: 'AI Study Plan' }}
            />
            <Stack.Screen
              name="SpacedRepetition"
              component={SpacedRepetitionScreen}
              options={{ title: 'Spaced Repetition' }}
            />
            {/* Content Organization Screens */}
            <Stack.Screen
              name="Favorites"
              component={FavoritesScreen}
              options={{ title: 'Favorites' }}
            />
            <Stack.Screen
              name="StudyPlaylists"
              component={StudyPlaylistsScreen}
              options={{ title: 'Study Playlists' }}
            />
            <Stack.Screen
              name="Notes"
              component={NotesScreen}
              options={{ title: 'My Notes' }}
            />
            <Stack.Screen
              name="Premium"
              component={PremiumScreen}
              options={{ title: 'Premium' }}
            />
           {/* Add LazyChapterScreen to the stack */}
           <Stack.Screen
             name="ChapterScreen"
             component={LazyChapterScreen} // Use lazy loaded component
             options={({ route }) => ({ title: `${route.params.category} Chapters` })} // Dynamic title based on category
           />
           {/* Add LazyVideoChapterScreen to the stack */}
           <Stack.Screen
             name="VideoChapterScreen"
              component={LazyVideoChapterScreen} // Use lazy loaded component
              options={({ route }) => ({ title: `${route.params.category} Video Chapters` })} // Dynamic title
            />
            {/* Add LazyBookUnitsScreen to the stack */}
            <Stack.Screen
              name="BookUnitsScreen"
              component={LazyBookUnitsScreen} // Use lazy loaded component
              options={({ route }) => ({ title: route.params.bookTitle || 'Book Units' })} // Dynamic title based on book
            />
            {/* Add PdfViewerScreen to the stack */}
            <Stack.Screen
              name="PdfViewer"
              component={LazyPdfViewerScreen}
              options={{ title: 'PDF Viewer' }} // You can customize the title
            />
          </>
         ) : (
          // No user is signed in, show auth screens
          <>
            <Stack.Screen
              name="Login"
              component={LoginScreen}
              options={{ headerShown: false }}
            />
            <Stack.Screen
              name="Register"
              component={RegisterScreen}
              options={{ headerShown: false }}
            />
            <Stack.Screen
              name="ForgotPassword"
              component={ForgotPasswordScreen}
              options={{ headerShown: false }}
            />
          </>
        )}
         {/* Screens accessible regardless of auth state (if any needed) */}
        {/* Example: A public info screen */}
        {/* <Stack.Screen name="PublicInfo" component={PublicInfoScreen} /> */}
       </Stack.Navigator>
     </Suspense>
   </NavigationContainer>
 );
}


export default function App() {
  // The useFonts hook needs to be called within the component rendering the navigator
  // So we wrap RootNavigator which now contains the hook call
  return (
    <ThemeProvider>
      <RootNavigator />
    </ThemeProvider>
  );
}


const styles = StyleSheet.create({
  drawerContainer: { flex: 1 },
  drawerHeader: { padding: 20, alignItems: 'center', borderBottomWidth: 1, borderBottomColor: '#ccc' },
  userName: { fontSize: 18, fontWeight: 'bold', marginBottom: 5 },
  userEmail: { fontSize: 14 },
  // Removed themeToggleContainer and themeToggleText styles
  // Removed container and settingRow styles (they belonged to SettingsScreenUpdated)
});
