import React from 'react';
import { View, StyleSheet, Platform, Dimensions } from 'react-native';
import { WebView } from 'react-native-webview';

// Get screen width for responsive ad sizing
const windowWidth = Dimensions.get('window').width;

const SimpleAdBanner = ({ height = 50, testMode = false }) => {
  // Sample HTML with a banner ad (using a placeholder in test mode)
  const adHtml = testMode 
    ? `
      <html>
        <head>
          <meta name="viewport" content="width=device-width, initial-scale=1">
          <style>
            body {
              margin: 0;
              padding: 0;
              display: flex;
              justify-content: center;
              align-items: center;
              background-color: #f0f0f0;
              height: 100%;
              overflow: hidden;
            }
            .ad-container {
              width: 100%;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
              background-color: #e0e0e0;
              color: #555;
              font-family: Arial, sans-serif;
              border: 1px solid #ccc;
              font-size: 12px;
              text-align: center;
            }
          </style>
        </head>
        <body>
          <div class="ad-container">
            <div>TEST MODE - Ad Banner Placeholder<br/>(${windowWidth}x${height})</div>
          </div>
        </body>
      </html>
    `
    : `
      <html>
        <head>
          <meta name="viewport" content="width=device-width, initial-scale=1">
          <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-9706687137550019" crossorigin="anonymous"></script>
          <style>
            body {
              margin: 0;
              padding: 0;
              overflow: hidden;
            }
            .ad-container {
              width: 100%;
              height: 100%;
            }
          </style>
        </head>
        <body>
          <div class="ad-container">
            <ins class="adsbygoogle"
              style="display:block"
              data-ad-client="ca-pub-9706687137550019"
              data-ad-slot="4124160377"
              data-ad-format="auto"
              data-full-width-responsive="true"></ins>
            <script>
              (adsbygoogle = window.adsbygoogle || []).push({});
            </script>
          </div>
        </body>
      </html>
    `;

  return (
    <View style={[styles.container, { height }]}>
      <WebView
        source={{ html: adHtml }}
        style={styles.webView}
        scrollEnabled={false}
        javaScriptEnabled={true}
        domStorageEnabled={true}
        originWhitelist={['*']}
        automaticallyAdjustContentInsets={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    overflow: 'hidden',
    backgroundColor: '#f0f0f0',
  },
  webView: {
    flex: 1,
  },
});

export default SimpleAdBanner; 