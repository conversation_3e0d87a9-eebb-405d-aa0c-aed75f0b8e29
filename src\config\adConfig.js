// Enhanced Ad Configuration
// This file contains all ad-related configuration for the app with improved revenue optimization

import { Platform, Dimensions } from 'react-native';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// Enhanced Ad placement configuration with more strategic placements
export const AD_PLACEMENTS = {
  // Banner placements
  HOME_BANNER: 'home_banner',
  QUIZ_BANNER: 'quiz_banner',
  CHAPTER_BANNER: 'chapter_banner',
  SETTINGS_BANNER: 'settings_banner',
  AI_CHAT_BANNER: 'ai_chat_banner',
  STUDY_TOOLS_BANNER: 'study_tools_banner',
  FAVORITES_BANNER: 'favorites_banner',
  NOTES_BANNER: 'notes_banner',
  PREMIUM_BANNER: 'premium_banner',
  MOCK_EXAMS_BANNER: 'mock_exams_banner',

  // Interstitial placements
  QUIZ_INTERSTITIAL: 'quiz_interstitial',
  CHAPTER_COMPLETE_INTERSTITIAL: 'chapter_complete_interstitial',
  STUDY_SESSION_INTERSTITIAL: 'study_session_interstitial',
  APP_LAUNCH_INTERSTITIAL: 'app_launch_interstitial',

  // Native ad placements
  QUIZ_LIST_NATIVE: 'quiz_list_native',
  STUDY_TOOLS_NATIVE: 'study_tools_native',

  // Rewarded ad placements
  HINT_REWARDED: 'hint_rewarded',
  PREMIUM_TRIAL_REWARDED: 'premium_trial_rewarded',
  EXTRA_LIVES_REWARDED: 'extra_lives_rewarded',
};

// Enhanced Ad frequency configuration for better user experience and revenue
export const AD_FREQUENCY = {
  INTERSTITIAL_AFTER_QUIZ: 2, // Reduced to 2 for more revenue (was 3)
  INTERSTITIAL_MIN_INTERVAL: 90000, // 1.5 minutes between interstitials
  BANNER_REFRESH_INTERVAL: 45000, // Increased to 45 seconds for better viewability
  APP_LAUNCH_INTERSTITIAL_DELAY: 30000, // Show after 30 seconds of app usage
  CHAPTER_COMPLETE_INTERSTITIAL_CHANCE: 0.7, // 70% chance to show
  STUDY_SESSION_INTERSTITIAL_INTERVAL: 300000, // Every 5 minutes during study
};

// Enhanced Ad sizes configuration with adaptive sizing
export const AD_SIZES = {
  SMALL_BANNER: { width: 320, height: 50 },
  MEDIUM_BANNER: { width: 320, height: 100 },
  LARGE_BANNER: { width: 320, height: 250 },
  ADAPTIVE_BANNER: 'adaptive', // Let AdMob decide the best size
  SMART_BANNER: 'smartBanner', // Smart banner that adapts to screen
  FULL_BANNER: { width: 468, height: 60 },
  LEADERBOARD: { width: 728, height: 90 },
  MEDIUM_RECTANGLE: { width: 300, height: 250 },
  // Dynamic sizing based on screen width
  RESPONSIVE_BANNER: {
    width: Math.min(screenWidth - 20, 320),
    height: screenWidth > 400 ? 100 : 50
  },
};

// Test mode configuration
export const AD_TEST_MODE = __DEV__; // Use test ads in development

// Enhanced Ad unit IDs with multiple ad types for better revenue
export const PRODUCTION_AD_UNITS = {
  banner: Platform.select({
    ios: 'ca-app-pub-9706687137550019/4124160377',
    android: 'ca-app-pub-9706687137550019/4124160377',
  }),
  interstitial: Platform.select({
    ios: 'ca-app-pub-9706687137550019/7998992050',
    android: 'ca-app-pub-9706687137550019/7998992050',
  }),
  // Note: You'll need to create these additional ad units in AdMob console
  rewarded: Platform.select({
    ios: 'ca-app-pub-9706687137550019/4124160377', // Placeholder - create rewarded ad unit
    android: 'ca-app-pub-9706687137550019/4124160377', // Placeholder - create rewarded ad unit
  }),
  native: Platform.select({
    ios: 'ca-app-pub-9706687137550019/4124160377', // Placeholder - create native ad unit
    android: 'ca-app-pub-9706687137550019/4124160377', // Placeholder - create native ad unit
  }),
};

// Enhanced Test ad unit IDs (provided by Google)
export const TEST_AD_UNITS = {
  banner: Platform.select({
    ios: 'ca-app-pub-3940256099942544/2934735716',
    android: 'ca-app-pub-3940256099942544/6300978111',
  }),
  interstitial: Platform.select({
    ios: 'ca-app-pub-3940256099942544/4411468910',
    android: 'ca-app-pub-3940256099942544/1033173712',
  }),
  rewarded: Platform.select({
    ios: 'ca-app-pub-3940256099942544/1712485313',
    android: 'ca-app-pub-3940256099942544/5224354917',
  }),
  native: Platform.select({
    ios: 'ca-app-pub-3940256099942544/3986624511',
    android: 'ca-app-pub-3940256099942544/**********',
  }),
};

// Get the appropriate ad unit ID based on test mode
export const getAdUnitId = (adType) => {
  return AD_TEST_MODE ? TEST_AD_UNITS[adType] : PRODUCTION_AD_UNITS[adType];
};

// Enhanced Ad request configuration for better targeting and revenue
export const AD_REQUEST_CONFIG = {
  requestNonPersonalizedAdsOnly: false, // Set to true for GDPR compliance if needed
  keywords: [
    'education', 'quiz', 'learning', 'study', 'neet', 'exam', 'medical',
    'entrance', 'test', 'preparation', 'biology', 'chemistry', 'physics',
    'student', 'college', 'university', 'career', 'competitive', 'coaching'
  ],
  contentUrl: 'https://quiz-bee-techs.com', // Your app's content URL
  // Enhanced targeting
  maxAdContentRating: 'G', // General audiences
  tagForChildDirectedTreatment: false,
  tagForUnderAgeOfConsent: false,
};

// Enhanced Ad error handling configuration
export const AD_ERROR_CONFIG = {
  maxRetries: 5, // Increased retries for better fill rate
  retryDelay: 3000, // Reduced delay for faster recovery
  exponentialBackoff: true, // Use exponential backoff for retries
  fallbackToWebView: true, // Fall back to WebView ads if native ads fail
  maxConsecutiveErrors: 10, // Disable placement after too many errors
  errorCooldownPeriod: 600000, // 10 minutes cooldown after max errors
};

// Revenue optimization configuration
export const REVENUE_CONFIG = {
  // Ad refresh settings
  enableBannerRefresh: true,
  bannerRefreshInterval: AD_FREQUENCY.BANNER_REFRESH_INTERVAL,
  maxBannerRefreshes: 10, // Limit refreshes per session

  // Interstitial optimization
  enableSmartInterstitials: true,
  interstitialCapping: 5, // Max 5 interstitials per session

  // User engagement tracking
  trackUserEngagement: true,
  highEngagementThreshold: 300000, // 5 minutes of active usage

  // A/B testing for ad placements
  enableAdPlacementTesting: true,
  testVariants: ['default', 'aggressive', 'conservative'],
};

// Ad placement priority for revenue optimization
export const AD_PLACEMENT_PRIORITY = {
  [AD_PLACEMENTS.HOME_BANNER]: 'high',
  [AD_PLACEMENTS.QUIZ_BANNER]: 'high',
  [AD_PLACEMENTS.QUIZ_INTERSTITIAL]: 'critical',
  [AD_PLACEMENTS.CHAPTER_COMPLETE_INTERSTITIAL]: 'high',
  [AD_PLACEMENTS.AI_CHAT_BANNER]: 'medium',
  [AD_PLACEMENTS.STUDY_TOOLS_BANNER]: 'medium',
  [AD_PLACEMENTS.SETTINGS_BANNER]: 'low',
  [AD_PLACEMENTS.HINT_REWARDED]: 'critical',
  [AD_PLACEMENTS.PREMIUM_TRIAL_REWARDED]: 'critical',
};
