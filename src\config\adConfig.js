// Ad Configuration
// This file contains all ad-related configuration for the app

import { Platform } from 'react-native';

// Ad placement configuration
export const AD_PLACEMENTS = {
  HOME_BANNER: 'home_banner',
  QUIZ_BANNER: 'quiz_banner',
  QUIZ_INTERSTITIAL: 'quiz_interstitial',
  CHAPTER_BANNER: 'chapter_banner',
  SETTINGS_BANNER: 'settings_banner',
  AI_CHAT_BANNER: 'ai_chat_banner',
};

// Ad frequency configuration
export const AD_FREQUENCY = {
  INTERSTITIAL_AFTER_QUIZ: 3, // Show interstitial after every 3 quizzes
  BANNER_REFRESH_INTERVAL: 30000, // 30 seconds
};

// Ad sizes configuration
export const AD_SIZES = {
  SMALL_BANNER: { width: 320, height: 50 },
  MEDIUM_BANNER: { width: 320, height: 100 },
  LARGE_BANNER: { width: 320, height: 250 },
  ADAPTIVE_BANNER: 'adaptive', // Let AdMob decide the best size
};

// Test mode configuration
export const AD_TEST_MODE = __DEV__; // Use test ads in development

// Ad unit IDs (your real production IDs)
export const PRODUCTION_AD_UNITS = {
  banner: Platform.select({
    ios: 'ca-app-pub-9706687137550019/4124160377',
    android: 'ca-app-pub-9706687137550019/4124160377',
  }),
  interstitial: Platform.select({
    ios: 'ca-app-pub-9706687137550019/7998992050',
    android: 'ca-app-pub-9706687137550019/7998992050',
  }),
};

// Test ad unit IDs (provided by Google)
export const TEST_AD_UNITS = {
  banner: Platform.select({
    ios: 'ca-app-pub-3940256099942544/2934735716',
    android: 'ca-app-pub-3940256099942544/6300978111',
  }),
  interstitial: Platform.select({
    ios: 'ca-app-pub-3940256099942544/4411468910',
    android: 'ca-app-pub-3940256099942544/1033173712',
  }),
};

// Get the appropriate ad unit ID based on test mode
export const getAdUnitId = (adType) => {
  return AD_TEST_MODE ? TEST_AD_UNITS[adType] : PRODUCTION_AD_UNITS[adType];
};

// Ad request configuration
export const AD_REQUEST_CONFIG = {
  requestNonPersonalizedAdsOnly: false, // Set to true for GDPR compliance if needed
  keywords: ['education', 'quiz', 'learning', 'study', 'neet', 'exam'],
  contentUrl: 'https://quiz-bee-techs.com', // Your app's content URL
};

// Ad error handling configuration
export const AD_ERROR_CONFIG = {
  maxRetries: 3,
  retryDelay: 5000, // 5 seconds
  fallbackToWebView: true, // Fall back to WebView ads if native ads fail
};
