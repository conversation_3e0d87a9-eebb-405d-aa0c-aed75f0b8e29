{"logs": [{"outputFile": "com.gokul719.snack97152fc1f368437dac54171df4ba22bd.app-mergeReleaseResources-74:/values-fr-rCA/values-fr-rCA.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\42358a9cae14349299c1c7a268311762\\transformed\\appcompat-1.7.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,323,433,520,626,756,841,921,1012,1105,1203,1298,1398,1491,1584,1679,1770,1861,1947,2057,2168,2271,2382,2490,2597,2756,2855", "endColumns": "110,106,109,86,105,129,84,79,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,318,428,515,621,751,836,916,1007,1100,1198,1293,1393,1486,1579,1674,1765,1856,1942,2052,2163,2266,2377,2485,2592,2751,2850,2937"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,227", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "919,1030,1137,1247,1334,1440,1570,1655,1735,1826,1919,2017,2112,2212,2305,2398,2493,2584,2675,2761,2871,2982,3085,3196,3304,3411,3570,19202", "endColumns": "110,106,109,86,105,129,84,79,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "1025,1132,1242,1329,1435,1565,1650,1730,1821,1914,2012,2107,2207,2300,2393,2488,2579,2670,2756,2866,2977,3080,3191,3299,3406,3565,3664,19284"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\495d9b066df23cebe365f0b1f0fc7029\\transformed\\exoplayer-ui-2.18.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,499,686,775,866,945,1043,1140,1219,1285,1382,1479,1544,1607,1671,1743,1864,1990,2115,2190,2278,2351,2431,2530,2631,2697,2761,2814,2872,2920,2981,3048,3125,3192,3264,3322,3381,3447,3512,3578,3630,3695,3774,3853", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,88,90,78,97,96,78,65,96,96,64,62,63,71,120,125,124,74,87,72,79,98,100,65,63,52,57,47,60,66,76,66,71,57,58,65,64,65,51,64,78,78,53", "endOffsets": "280,494,681,770,861,940,1038,1135,1214,1280,1377,1474,1539,1602,1666,1738,1859,1985,2110,2185,2273,2346,2426,2525,2626,2692,2756,2809,2867,2915,2976,3043,3120,3187,3259,3317,3376,3442,3507,3573,3625,3690,3769,3848,3902"}, "to": {"startLines": "2,11,15,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,549,7994,8083,8174,8253,8351,8448,8527,8593,8690,8787,8852,8915,8979,9051,9172,9298,9423,9498,9586,9659,9739,9838,9939,10005,10799,10852,10910,10958,11019,11086,11163,11230,11302,11360,11419,11485,11550,11616,11668,11733,11812,11891", "endLines": "10,14,18,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "endColumns": "17,12,12,88,90,78,97,96,78,65,96,96,64,62,63,71,120,125,124,74,87,72,79,98,100,65,63,52,57,47,60,66,76,66,71,57,58,65,64,65,51,64,78,78,53", "endOffsets": "330,544,731,8078,8169,8248,8346,8443,8522,8588,8685,8782,8847,8910,8974,9046,9167,9293,9418,9493,9581,9654,9734,9833,9934,10000,10064,10847,10905,10953,11014,11081,11158,11225,11297,11355,11414,11480,11545,11611,11663,11728,11807,11886,11940"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1c6965b61d3737d12e0f0d1e87d21cde\\transformed\\ui-release\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,292,390,496,583,663,757,849,936,1017,1102,1178,1253,1331,1405,1484,1553", "endColumns": "98,87,97,105,86,79,93,91,86,80,84,75,74,77,73,78,68,121", "endOffsets": "199,287,385,491,578,658,752,844,931,1012,1097,1173,1248,1326,1400,1479,1548,1670"}, "to": {"startLines": "62,63,84,85,86,146,147,200,201,216,217,228,232,235,237,242,243,245", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4851,4950,7637,7735,7841,12601,12681,16903,16995,18450,18531,19289,19631,19869,20024,20432,20511,20663", "endColumns": "98,87,97,105,86,79,93,91,86,80,84,75,74,77,73,78,68,121", "endOffsets": "4945,5033,7730,7836,7923,12676,12770,16990,17077,18526,18611,19360,19701,19942,20093,20506,20575,20780"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ab468244bce13502a51b6cbb55b0c3ba\\transformed\\material-1.6.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,238,320,420,542,627,693,790,870,932,1024,1098,1159,1238,1302,1356,1472,1531,1593,1647,1729,1858,1950,2034,2148,2227,2308,2401,2468,2534,2613,2694,2785,2857,2935,3010,3082,3179,3256,3354,3452,3530,3611,3711,3768,3834,3917,4004,4066,4130,4193,4295,4402,4499,4608", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,81,99,121,84,65,96,79,61,91,73,60,78,63,53,115,58,61,53,81,128,91,83,113,78,80,92,66,65,78,80,90,71,77,74,71,96,76,97,97,77,80,99,56,65,82,86,61,63,62,101,106,96,108,88", "endOffsets": "233,315,415,537,622,688,785,865,927,1019,1093,1154,1233,1297,1351,1467,1526,1588,1642,1724,1853,1945,2029,2143,2222,2303,2396,2463,2529,2608,2689,2780,2852,2930,3005,3077,3174,3251,3349,3447,3525,3606,3706,3763,3829,3912,3999,4061,4125,4188,4290,4397,4494,4603,4692"}, "to": {"startLines": "19,51,59,60,61,87,139,144,148,150,151,152,153,154,155,156,157,158,159,160,161,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,213", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "736,3739,4544,4644,4766,7928,11945,12438,12775,12904,12996,13070,13131,13210,13274,13328,13444,13503,13565,13619,13701,13990,14082,14166,14280,14359,14440,14533,14600,14666,14745,14826,14917,14989,15067,15142,15214,15311,15388,15486,15584,15662,15743,15843,15900,15966,16049,16136,16198,16262,16325,16427,16534,16631,18179", "endLines": "22,51,59,60,61,87,139,144,148,150,151,152,153,154,155,156,157,158,159,160,161,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,213", "endColumns": "12,81,99,121,84,65,96,79,61,91,73,60,78,63,53,115,58,61,53,81,128,91,83,113,78,80,92,66,65,78,80,90,71,77,74,71,96,76,97,97,77,80,99,56,65,82,86,61,63,62,101,106,96,108,88", "endOffsets": "914,3816,4639,4761,4846,7989,12037,12513,12832,12991,13065,13126,13205,13269,13323,13439,13498,13560,13614,13696,13825,14077,14161,14275,14354,14435,14528,14595,14661,14740,14821,14912,14984,15062,15137,15209,15306,15383,15481,15579,15657,15738,15838,15895,15961,16044,16131,16193,16257,16320,16422,16529,16626,16735,18263"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7c7a5afab22fc7e54eb8c70c357abcc1\\transformed\\react-android-0.76.9-release\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,205,275,358,425,504,585,675,767,838,926,1021,1112,1192,1272,1355,1432,1505,1593,1665,1748,1821", "endColumns": "69,79,69,82,66,78,80,89,91,70,87,94,90,79,79,82,76,72,87,71,82,72,79", "endOffsets": "120,200,270,353,420,499,580,670,762,833,921,1016,1107,1187,1267,1350,1427,1500,1588,1660,1743,1816,1896"}, "to": {"startLines": "50,64,143,145,149,162,163,214,215,218,226,229,230,231,233,234,236,238,239,241,244,246,247", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3669,5038,12368,12518,12837,13830,13909,18268,18358,18616,19114,19365,19460,19551,19706,19786,19947,20098,20171,20360,20580,20785,20858", "endColumns": "69,79,69,82,66,78,80,89,91,70,87,94,90,79,79,82,76,72,87,71,82,72,79", "endOffsets": "3734,5113,12433,12596,12899,13904,13985,18353,18445,18682,19197,19455,19546,19626,19781,19864,20019,20166,20254,20427,20658,20853,20933"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7bbb068f5d1da3b4f3e1ef6a35abc59e\\transformed\\foundation-release\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,94", "endOffsets": "138,233"}, "to": {"startLines": "248,249", "startColumns": "4,4", "startOffsets": "20938,21026", "endColumns": "87,94", "endOffsets": "21021,21116"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0309cb612008fa0a7f120a51a87e98c5\\transformed\\play-services-basement-18.4.0\\res\\values-fr-rCA\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "156", "endOffsets": "355"}, "to": {"startLines": "73", "startColumns": "4", "startOffsets": "6181", "endColumns": "160", "endOffsets": "6337"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\46429bd1dac210616d37d633e605f2e8\\transformed\\browser-1.8.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,383", "endColumns": "106,101,118,104", "endOffsets": "157,259,378,483"}, "to": {"startLines": "83,140,141,142", "startColumns": "4,4,4,4", "startOffsets": "7530,12042,12144,12263", "endColumns": "106,101,118,104", "endOffsets": "7632,12139,12258,12363"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\659eba141f87d2afd24e0f28711a233e\\transformed\\play-services-ads-23.6.0\\res\\values-fr-rCA\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "203,245,291,354,421,495,603,666,825,937,1076,1126,1183,1315,1407,1455,1549,1585,1620,1681,1766,1806", "endColumns": "41,45,62,66,73,107,62,158,111,138,49,56,131,91,47,93,35,34,60,84,39,55", "endOffsets": "244,290,353,420,494,602,665,824,936,1075,1125,1182,1314,1406,1454,1548,1584,1619,1680,1765,1805,1861"}, "to": {"startLines": "197,198,199,202,203,204,205,206,207,208,209,210,211,212,219,220,221,222,223,224,225,250", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16740,16786,16836,17082,17153,17231,17343,17410,17573,17689,17832,17886,17947,18083,18687,18739,18837,18877,18916,18981,19070,21121", "endColumns": "45,49,66,70,77,111,66,162,115,142,53,60,135,95,51,97,39,38,64,88,43,59", "endOffsets": "16781,16831,16898,17148,17226,17338,17405,17568,17684,17827,17881,17942,18078,18174,18734,18832,18872,18911,18976,19065,19109,21176"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7a0941004ac45c50b486dd320a2bfc96\\transformed\\core-1.13.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,778", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "148,250,349,451,555,659,773,874"}, "to": {"startLines": "52,53,54,55,56,57,58,240", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3821,3919,4021,4120,4222,4326,4430,20259", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "3914,4016,4115,4217,4321,4425,4539,20355"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b56249db71bc4564ba5f782195824c95\\transformed\\exoplayer-core-2.18.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,194,272,344,427,503,600,693", "endColumns": "73,64,77,71,82,75,96,92,91", "endOffsets": "124,189,267,339,422,498,595,688,780"}, "to": {"startLines": "112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "10069,10143,10208,10286,10358,10441,10517,10614,10707", "endColumns": "73,64,77,71,82,75,96,92,91", "endOffsets": "10138,10203,10281,10353,10436,10512,10609,10702,10794"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d502ef2bffb1f4edf1dd29201baa894c\\transformed\\play-services-base-18.0.0\\res\\values-fr-rCA\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,299,475,601,706,873,1002,1119,1228,1419,1527,1708,1840,1996,2171,2240,2300", "endColumns": "101,175,125,104,166,128,116,108,190,107,180,131,155,174,68,59,79", "endOffsets": "298,474,600,705,872,1001,1118,1227,1418,1526,1707,1839,1995,2170,2239,2299,2379"}, "to": {"startLines": "65,66,67,68,69,70,71,72,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5118,5224,5404,5534,5643,5814,5947,6068,6342,6537,6649,6834,6970,7130,7309,7382,7446", "endColumns": "105,179,129,108,170,132,120,112,194,111,184,135,159,178,72,63,83", "endOffsets": "5219,5399,5529,5638,5809,5942,6063,6176,6532,6644,6829,6965,7125,7304,7377,7441,7525"}}]}]}