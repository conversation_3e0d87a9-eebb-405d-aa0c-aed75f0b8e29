import React, { useEffect, useState, useCallback, useContext, useRef } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Alert, ScrollView, Vibration } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { ThemeContext } from './ThemeContext';
import { Audio } from 'expo-av';
import AsyncStorage from '@react-native-async-storage/async-storage';
import quizDataRaw from '../data/questions.json';
import BannerAdComponent from '../components/BannerAdComponent';
import InterstitialAdComponent from '../components/InterstitialAdComponent';
import { AD_PLACEMENTS } from '../src/config/adConfig';

const TimedQuiz = ({ route, navigation }) => {
  const { darkMode, soundEnabled, soundVolume, fontSizeMultiplier } = useContext(ThemeContext);
  const {
    category = 'NEET',
    duration = 3600, // Default 1 hour in seconds
    questionCount = 50,
    testType = 'practice', // 'practice' or 'mock_exam'
    examType = null,
    onTestComplete = null,
    strictMode = false
  } = route.params || {};

  const [questions, setQuestions] = useState([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedAnswers, setSelectedAnswers] = useState({});
  const [timeLeft, setTimeLeft] = useState(duration);
  const [isTestStarted, setIsTestStarted] = useState(false);
  const [isTestCompleted, setIsTestCompleted] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [score, setScore] = useState(0);
  const [sound, setSound] = useState(null);
  const timerRef = useRef(null);
  const interstitialRef = useRef(null);

  // Load questions on component mount
  useEffect(() => {
    loadQuestions();
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
      if (sound) {
        sound.unloadAsync();
      }
    };
  }, []);

  // Timer effect
  useEffect(() => {
    if (isTestStarted && !isTestCompleted && timeLeft > 0) {
      timerRef.current = setInterval(() => {
        setTimeLeft(prev => {
          if (prev <= 1) {
            handleTestComplete();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } else {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [isTestStarted, isTestCompleted, timeLeft]);

  const loadQuestions = () => {
    try {
      let filteredQuestions = [];
      
      if (category === 'NEET' || category === 'Mixed') {
        // For NEET/Mixed, get questions from all subjects
        const subjects = ['Physics', 'Chemistry', 'Biology'];
        subjects.forEach(subject => {
          const subjectQuestions = quizDataRaw.filter(q => q.category === subject);
          const shuffled = subjectQuestions.sort(() => 0.5 - Math.random());
          filteredQuestions.push(...shuffled.slice(0, Math.ceil(questionCount / subjects.length)));
        });
      } else {
        // For specific subjects
        const subjectQuestions = quizDataRaw.filter(q => q.category === category);
        filteredQuestions = subjectQuestions.sort(() => 0.5 - Math.random());
      }

      // Shuffle and limit to required count
      const finalQuestions = filteredQuestions
        .sort(() => 0.5 - Math.random())
        .slice(0, questionCount);

      setQuestions(finalQuestions);
    } catch (error) {
      console.error('Error loading questions:', error);
      Alert.alert('Error', 'Failed to load questions. Please try again.');
      navigation.goBack();
    }
  };

  const playSound = async (type) => {
    if (!soundEnabled) return;

    try {
      const soundFile = type === 'correct' 
        ? require('../assets/audios/correct.mp3')
        : require('../assets/audios/wrong.mp3');

      const { sound: newSound } = await Audio.Sound.createAsync(soundFile);
      await newSound.setVolumeAsync(soundVolume);
      await newSound.playAsync();
      setSound(newSound);

      setTimeout(() => newSound.unloadAsync(), 2500);
    } catch (error) {
      console.error('Error playing sound:', error);
    }
  };

  const handleAnswerSelect = (questionIndex, answer) => {
    if (isTestCompleted) return;

    setSelectedAnswers(prev => ({
      ...prev,
      [questionIndex]: answer
    }));

    // Auto-advance for practice tests (not for strict mock exams)
    if (!strictMode && testType === 'practice') {
      setTimeout(() => {
        if (questionIndex < questions.length - 1) {
          setCurrentQuestionIndex(questionIndex + 1);
        }
      }, 1000);
    }
  };

  const handleTestComplete = useCallback(() => {
    if (isTestCompleted) return;

    setIsTestCompleted(true);
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }

    // Calculate score
    let correctAnswers = 0;
    questions.forEach((question, index) => {
      const userAnswer = selectedAnswers[index];
      if (userAnswer === question.correctAnswer) {
        correctAnswers++;
      }
    });

    setScore(correctAnswers);
    
    // Save test result
    const testResult = {
      id: Date.now().toString(),
      type: testType,
      category: category,
      examType: examType?.name || category,
      score: correctAnswers,
      totalQuestions: questions.length,
      percentage: Math.round((correctAnswers / questions.length) * 100),
      timeSpent: duration - timeLeft,
      duration: duration,
      date: new Date().toISOString(),
      answers: selectedAnswers
    };

    // Call completion callback if provided
    if (onTestComplete) {
      onTestComplete(testResult);
    }

    // Save to local storage
    saveTestResult(testResult);

    setShowResults(true);
  }, [isTestCompleted, questions, selectedAnswers, testType, category, examType, duration, timeLeft, onTestComplete]);

  const saveTestResult = async (result) => {
    try {
      const key = `${testType}_results_${category}`;
      const existingResults = await AsyncStorage.getItem(key);
      const results = existingResults ? JSON.parse(existingResults) : [];
      
      results.unshift(result);
      // Keep only last 20 results
      const limitedResults = results.slice(0, 20);
      
      await AsyncStorage.setItem(key, JSON.stringify(limitedResults));
    } catch (error) {
      console.error('Error saving test result:', error);
    }
  };

  const formatTime = (seconds) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  const handleStartTest = () => {
    if (questions.length === 0) {
      Alert.alert('Error', 'No questions loaded. Please try again.');
      return;
    }
    setIsTestStarted(true);
  };

  const handleFinishTest = () => {
    Alert.alert(
      'Finish Test',
      'Are you sure you want to finish the test? You cannot resume once finished.',
      [
        { text: 'Continue Test', style: 'cancel' },
        { text: 'Finish', onPress: handleTestComplete }
      ]
    );
  };

  const handleExitTest = () => {
    Alert.alert(
      'Exit Test',
      'Are you sure you want to exit? Your progress will be lost.',
      [
        { text: 'Continue Test', style: 'cancel' },
        { text: 'Exit', style: 'destructive', onPress: () => navigation.goBack() }
      ]
    );
  };

  const navigateToQuestion = (index) => {
    setCurrentQuestionIndex(index);
  };

  const handleBackToMenu = () => {
    // Show interstitial ad before going back
    if (interstitialRef.current?.isLoaded()) {
      interstitialRef.current.showAd();
    } else {
      navigation.goBack();
    }
  };

  if (questions.length === 0) {
    return (
      <View style={[styles.container, { backgroundColor: darkMode ? '#121212' : '#F5F5F5' }]}>
        <Text style={[styles.loadingText, { color: darkMode ? '#FFFFFF' : '#333333' }]}>
          Loading questions...
        </Text>
      </View>
    );
  }

  // Pre-test screen
  if (!isTestStarted) {
    return (
      <View style={[styles.container, { backgroundColor: darkMode ? '#121212' : '#F5F5F5' }]}>
        <ScrollView contentContainerStyle={styles.preTestContainer}>
          <LinearGradient
            colors={examType?.colors || ['#667eea', '#764ba2']}
            style={styles.headerGradient}
          >
            <Ionicons name="school-outline" size={48} color="#FFFFFF" />
            <Text style={[styles.testTitle, { fontSize: 24 * fontSizeMultiplier }]}>
              {examType?.name || `${category} ${testType === 'mock_exam' ? 'Mock Exam' : 'Practice Test'}`}
            </Text>
          </LinearGradient>

          <View style={[styles.infoCard, { backgroundColor: darkMode ? '#2A2A2A' : '#FFFFFF' }]}>
            <View style={styles.infoRow}>
              <Ionicons name="time-outline" size={24} color={darkMode ? '#FFFFFF' : '#333333'} />
              <Text style={[styles.infoText, { color: darkMode ? '#FFFFFF' : '#333333', fontSize: 16 * fontSizeMultiplier }]}>
                Duration: {formatTime(duration)}
              </Text>
            </View>
            <View style={styles.infoRow}>
              <Ionicons name="help-circle-outline" size={24} color={darkMode ? '#FFFFFF' : '#333333'} />
              <Text style={[styles.infoText, { color: darkMode ? '#FFFFFF' : '#333333', fontSize: 16 * fontSizeMultiplier }]}>
                Questions: {questions.length}
              </Text>
            </View>
            {examType?.subjects && (
              <View style={styles.infoRow}>
                <Ionicons name="book-outline" size={24} color={darkMode ? '#FFFFFF' : '#333333'} />
                <Text style={[styles.infoText, { color: darkMode ? '#FFFFFF' : '#333333', fontSize: 16 * fontSizeMultiplier }]}>
                  Subjects: {examType.subjects.join(', ')}
                </Text>
              </View>
            )}
          </View>

          <TouchableOpacity onPress={handleStartTest} style={styles.startButton}>
            <LinearGradient
              colors={['#4CAF50', '#45A049']}
              style={styles.startButtonGradient}
            >
              <Text style={[styles.startButtonText, { fontSize: 18 * fontSizeMultiplier }]}>
                Start Test
              </Text>
              <Ionicons name="play" size={24} color="#FFFFFF" />
            </LinearGradient>
          </TouchableOpacity>

          <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
            <Text style={[styles.backButtonText, { color: darkMode ? '#CCCCCC' : '#666666', fontSize: 16 * fontSizeMultiplier }]}>
              Back to Menu
            </Text>
          </TouchableOpacity>
        </ScrollView>
      </View>
    );
  }

  // Results screen
  if (showResults) {
    const percentage = Math.round((score / questions.length) * 100);
    const passed = percentage >= 60;

    return (
      <View style={[styles.container, { backgroundColor: darkMode ? '#121212' : '#F5F5F5' }]}>
        <ScrollView contentContainerStyle={styles.resultsContainer}>
          <LinearGradient
            colors={passed ? ['#4CAF50', '#45A049'] : ['#F44336', '#D32F2F']}
            style={styles.resultsHeader}
          >
            <Ionicons 
              name={passed ? "checkmark-circle" : "close-circle"} 
              size={64} 
              color="#FFFFFF" 
            />
            <Text style={[styles.resultsTitle, { fontSize: 24 * fontSizeMultiplier }]}>
              {passed ? 'Test Completed!' : 'Test Completed'}
            </Text>
            <Text style={[styles.resultsSubtitle, { fontSize: 18 * fontSizeMultiplier }]}>
              {passed ? 'Great job!' : 'Keep practicing!'}
            </Text>
          </LinearGradient>

          <View style={[styles.scoreCard, { backgroundColor: darkMode ? '#2A2A2A' : '#FFFFFF' }]}>
            <Text style={[styles.scoreText, { color: darkMode ? '#FFFFFF' : '#333333', fontSize: 32 * fontSizeMultiplier }]}>
              {score}/{questions.length}
            </Text>
            <Text style={[styles.percentageText, { color: darkMode ? '#CCCCCC' : '#666666', fontSize: 18 * fontSizeMultiplier }]}>
              {percentage}%
            </Text>
            <Text style={[styles.timeSpentText, { color: darkMode ? '#CCCCCC' : '#666666', fontSize: 16 * fontSizeMultiplier }]}>
              Time: {formatTime(duration - timeLeft)}
            </Text>
          </View>

          <TouchableOpacity onPress={handleBackToMenu} style={styles.finishButton}>
            <LinearGradient
              colors={['#667eea', '#764ba2']}
              style={styles.finishButtonGradient}
            >
              <Text style={[styles.finishButtonText, { fontSize: 18 * fontSizeMultiplier }]}>
                Back to Menu
              </Text>
            </LinearGradient>
          </TouchableOpacity>
        </ScrollView>

        {/* Interstitial Ad Component */}
        <InterstitialAdComponent
          ref={interstitialRef}
          placement={AD_PLACEMENTS.QUIZ_INTERSTITIAL}
          onAdDismissed={() => navigation.goBack()}
          autoShow={false}
        />
      </View>
    );
  }

  // Main test screen
  const currentQuestion = questions[currentQuestionIndex];
  const userAnswer = selectedAnswers[currentQuestionIndex];

  return (
    <View style={[styles.container, { backgroundColor: darkMode ? '#121212' : '#F5F5F5' }]}>
      {/* Header with timer and navigation */}
      <View style={[styles.header, { backgroundColor: darkMode ? '#1E1E1E' : '#FFFFFF' }]}>
        <TouchableOpacity onPress={handleExitTest} style={styles.exitButton}>
          <Ionicons name="close" size={24} color={darkMode ? '#FFFFFF' : '#333333'} />
        </TouchableOpacity>
        
        <View style={styles.timerContainer}>
          <Ionicons name="time-outline" size={20} color={timeLeft < 300 ? '#F44336' : (darkMode ? '#FFFFFF' : '#333333')} />
          <Text style={[
            styles.timerText, 
            { 
              color: timeLeft < 300 ? '#F44336' : (darkMode ? '#FFFFFF' : '#333333'),
              fontSize: 16 * fontSizeMultiplier 
            }
          ]}>
            {formatTime(timeLeft)}
          </Text>
        </View>

        <TouchableOpacity onPress={handleFinishTest} style={styles.finishTestButton}>
          <Text style={[styles.finishTestText, { fontSize: 14 * fontSizeMultiplier }]}>
            Finish
          </Text>
        </TouchableOpacity>
      </View>

      {/* Question navigation */}
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        style={styles.questionNavigation}
        contentContainerStyle={styles.questionNavigationContent}
      >
        {questions.map((_, index) => (
          <TouchableOpacity
            key={index}
            onPress={() => navigateToQuestion(index)}
            style={[
              styles.questionNavButton,
              {
                backgroundColor: selectedAnswers[index] 
                  ? '#4CAF50' 
                  : index === currentQuestionIndex 
                    ? '#2196F3' 
                    : (darkMode ? '#333333' : '#E0E0E0')
              }
            ]}
          >
            <Text style={[
              styles.questionNavText,
              { 
                color: selectedAnswers[index] || index === currentQuestionIndex ? '#FFFFFF' : (darkMode ? '#FFFFFF' : '#333333'),
                fontSize: 12 * fontSizeMultiplier
              }
            ]}>
              {index + 1}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Question content */}
      <ScrollView style={styles.questionContent}>
        <View style={styles.questionContainer}>
          <Text style={[
            styles.questionNumber,
            { 
              color: darkMode ? '#CCCCCC' : '#666666',
              fontSize: 16 * fontSizeMultiplier
            }
          ]}>
            Question {currentQuestionIndex + 1} of {questions.length}
          </Text>
          
          <Text style={[
            styles.questionText,
            { 
              color: darkMode ? '#FFFFFF' : '#333333',
              fontSize: 18 * fontSizeMultiplier
            }
          ]}>
            {currentQuestion?.question || 'Loading question...'}
          </Text>

          {currentQuestion?.options?.map((option, index) => (
            <TouchableOpacity
              key={index}
              onPress={() => handleAnswerSelect(currentQuestionIndex, option)}
              style={[
                styles.optionButton,
                {
                  backgroundColor: userAnswer === option 
                    ? '#2196F3' 
                    : (darkMode ? '#2A2A2A' : '#FFFFFF'),
                  borderColor: userAnswer === option 
                    ? '#2196F3' 
                    : (darkMode ? '#444444' : '#E0E0E0')
                }
              ]}
            >
              <Text style={[
                styles.optionText,
                { 
                  color: userAnswer === option ? '#FFFFFF' : (darkMode ? '#FFFFFF' : '#333333'),
                  fontSize: 16 * fontSizeMultiplier
                }
              ]}>
                {String.fromCharCode(65 + index)}. {option}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>

      {/* Navigation buttons */}
      <View style={[styles.navigationButtons, { backgroundColor: darkMode ? '#1E1E1E' : '#FFFFFF' }]}>
        <TouchableOpacity
          onPress={() => navigateToQuestion(Math.max(0, currentQuestionIndex - 1))}
          disabled={currentQuestionIndex === 0}
          style={[
            styles.navButton,
            { 
              backgroundColor: currentQuestionIndex === 0 ? '#CCCCCC' : '#2196F3',
              opacity: currentQuestionIndex === 0 ? 0.5 : 1
            }
          ]}
        >
          <Text style={[styles.navButtonText, { fontSize: 16 * fontSizeMultiplier }]}>
            Previous
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => navigateToQuestion(Math.min(questions.length - 1, currentQuestionIndex + 1))}
          disabled={currentQuestionIndex === questions.length - 1}
          style={[
            styles.navButton,
            { 
              backgroundColor: currentQuestionIndex === questions.length - 1 ? '#CCCCCC' : '#2196F3',
              opacity: currentQuestionIndex === questions.length - 1 ? 0.5 : 1
            }
          ]}
        >
          <Text style={[styles.navButtonText, { fontSize: 16 * fontSizeMultiplier }]}>
            Next
          </Text>
        </TouchableOpacity>
      </View>

      {/* Banner Ad */}
      <BannerAdComponent
        placement={AD_PLACEMENTS.QUIZ_BANNER}
        fallbackToWebView={true}
        enableRefresh={false} // Disable refresh during test
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingText: {
    fontSize: 18,
    textAlign: 'center',
    marginTop: 50,
  },
  preTestContainer: {
    padding: 20,
    alignItems: 'center',
  },
  headerGradient: {
    padding: 30,
    borderRadius: 16,
    alignItems: 'center',
    marginBottom: 20,
    width: '100%',
  },
  testTitle: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    textAlign: 'center',
    marginTop: 12,
  },
  infoCard: {
    padding: 20,
    borderRadius: 12,
    width: '100%',
    marginBottom: 30,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  infoText: {
    marginLeft: 12,
    fontWeight: '500',
  },
  startButton: {
    width: '100%',
    borderRadius: 25,
    overflow: 'hidden',
    marginBottom: 16,
  },
  startButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 32,
  },
  startButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    marginRight: 8,
  },
  backButton: {
    paddingVertical: 12,
  },
  backButtonText: {
    textAlign: 'center',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  exitButton: {
    padding: 8,
  },
  timerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timerText: {
    marginLeft: 4,
    fontWeight: 'bold',
  },
  finishTestButton: {
    backgroundColor: '#FF9800',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  finishTestText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  questionNavigation: {
    maxHeight: 60,
    backgroundColor: 'transparent',
  },
  questionNavigationContent: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  questionNavButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  questionNavText: {
    fontWeight: 'bold',
  },
  questionContent: {
    flex: 1,
  },
  questionContainer: {
    padding: 20,
  },
  questionNumber: {
    marginBottom: 8,
  },
  questionText: {
    fontWeight: 'bold',
    lineHeight: 24,
    marginBottom: 20,
  },
  optionButton: {
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 12,
  },
  optionText: {
    lineHeight: 20,
  },
  navigationButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 12,
  },
  navButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 6,
    minWidth: 80,
    alignItems: 'center',
  },
  navButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  resultsContainer: {
    padding: 20,
    alignItems: 'center',
  },
  resultsHeader: {
    padding: 30,
    borderRadius: 16,
    alignItems: 'center',
    marginBottom: 20,
    width: '100%',
  },
  resultsTitle: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    marginTop: 12,
  },
  resultsSubtitle: {
    color: '#FFFFFF',
    opacity: 0.9,
    marginTop: 4,
  },
  scoreCard: {
    padding: 30,
    borderRadius: 12,
    alignItems: 'center',
    width: '100%',
    marginBottom: 30,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  scoreText: {
    fontWeight: 'bold',
    marginBottom: 8,
  },
  percentageText: {
    fontWeight: '600',
    marginBottom: 4,
  },
  timeSpentText: {
    fontWeight: '500',
  },
  finishButton: {
    width: '100%',
    borderRadius: 25,
    overflow: 'hidden',
  },
  finishButtonGradient: {
    paddingVertical: 16,
    paddingHorizontal: 32,
    alignItems: 'center',
  },
  finishButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
});

export default TimedQuiz;
