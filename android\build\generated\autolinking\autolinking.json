{"root": "C:\\Users\\<USER>\\quiz-bee-techs", "reactNativePath": "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\react-native", "dependencies": {"@react-native-async-storage/async-storage": {"root": "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\@react-native-async-storage\\async-storage", "name": "@react-native-async-storage/async-storage", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\@react-native-async-storage\\async-storage\\android", "packageImportPath": "import com.reactnativecommunity.asyncstorage.AsyncStoragePackage;", "packageInstance": "new AsyncStoragePackage()", "buildTypes": [], "libraryName": "rnasyncstorage", "componentDescriptors": [], "cmakeListsPath": "C:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "@react-native-clipboard/clipboard": {"root": "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\@react-native-clipboard\\clipboard", "name": "@react-native-clipboard/clipboard", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\@react-native-clipboard\\clipboard\\android", "packageImportPath": "import com.reactnativecommunity.clipboard.ClipboardPackage;", "packageInstance": "new ClipboardPackage()", "buildTypes": [], "libraryName": "rnclipboard", "componentDescriptors": [], "cmakeListsPath": "C:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "@react-native-community/slider": {"root": "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\@react-native-community\\slider", "name": "@react-native-community/slider", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\@react-native-community\\slider\\android", "packageImportPath": "import com.reactnativecommunity.slider.ReactSliderPackage;", "packageInstance": "new ReactSliderPackage()", "buildTypes": [], "libraryName": "RNCSlider", "componentDescriptors": ["RNCSliderComponentDescriptor"], "cmakeListsPath": "C:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "@react-native-picker/picker": {"root": "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\@react-native-picker\\picker", "name": "@react-native-picker/picker", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\@react-native-picker\\picker\\android", "packageImportPath": "import com.reactnativecommunity.picker.RNCPickerPackage;", "packageInstance": "new RNCPickerPackage()", "buildTypes": [], "libraryName": "rnpicker", "componentDescriptors": ["RNCAndroidDialogPickerComponentDescriptor", "RNCAndroidDropdownPickerComponentDescriptor"], "cmakeListsPath": "C:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "@react-native-voice/voice": {"root": "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\@react-native-voice\\voice", "name": "@react-native-voice/voice", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\@react-native-voice\\voice\\android", "packageImportPath": "import com.wenkesj.voice.VoicePackage;", "packageInstance": "new VoicePackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "C:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-voice/voice/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "expo": {"root": "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\expo", "name": "expo", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\expo\\android", "packageImportPath": "import expo.modules.ExpoModulesPackage;", "packageInstance": "new ExpoModulesPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "C:/Users/<USER>/quiz-bee-techs/node_modules/expo/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-fast-image": {"root": "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\react-native-fast-image", "name": "react-native-fast-image", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\react-native-fast-image\\android", "packageImportPath": "import com.dylanvann.fastimage.FastImageViewPackage;", "packageInstance": "new FastImageViewPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-fast-image/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-gesture-handler": {"root": "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\react-native-gesture-handler", "name": "react-native-gesture-handler", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\react-native-gesture-handler\\android", "packageImportPath": "import com.swmansion.gesturehandler.RNGestureHandlerPackage;", "packageInstance": "new RNGestureHandlerPackage()", "buildTypes": [], "libraryName": "rngesturehandler_codegen", "componentDescriptors": ["RNGestureHandlerButtonComponentDescriptor", "RNGestureHandlerRootViewComponentDescriptor"], "cmakeListsPath": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-google-mobile-ads": {"root": "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\react-native-google-mobile-ads", "name": "react-native-google-mobile-ads", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\react-native-google-mobile-ads\\android", "packageImportPath": "import io.invertase.googlemobileads.ReactNativeGoogleMobileAdsPackage;", "packageInstance": "new ReactNativeGoogleMobileAdsPackage()", "buildTypes": [], "libraryName": "RNGoogleMobileAdsSpec", "componentDescriptors": ["RNGoogleMobileAdsBannerViewComponentDescriptor", "RNGoogleMobileAdsMediaViewComponentDescriptor", "RNGoogleMobileAdsNativeViewComponentDescriptor"], "cmakeListsPath": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-haptic-feedback": {"root": "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\react-native-haptic-feedback", "name": "react-native-haptic-feedback", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\react-native-haptic-feedback\\android", "packageImportPath": "import com.mkuczera.RNReactNativeHapticFeedbackPackage;", "packageInstance": "new RNReactNativeHapticFeedbackPackage()", "buildTypes": [], "libraryName": "RNHapticFeedbackSpec", "componentDescriptors": [], "cmakeListsPath": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-haptic-feedback/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-pdf": {"root": "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\react-native-pdf", "name": "react-native-pdf", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\react-native-pdf\\android", "packageImportPath": "import org.wonday.pdf.RNPDFPackage;", "packageInstance": "new RNPDFPackage()", "buildTypes": [], "libraryName": "rnpdf", "componentDescriptors": ["RNPDFPdfViewComponentDescriptor"], "cmakeListsPath": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-reanimated": {"root": "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\react-native-reanimated", "name": "react-native-reanimated", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\react-native-reanimated\\android", "packageImportPath": "import com.swmansion.reanimated.ReanimatedPackage;", "packageInstance": "new ReanimatedPackage()", "buildTypes": [], "libraryName": "rnreanimated", "componentDescriptors": [], "cmakeListsPath": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-safe-area-context": {"root": "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\react-native-safe-area-context", "name": "react-native-safe-area-context", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\react-native-safe-area-context\\android", "packageImportPath": "import com.th3rdwave.safeareacontext.SafeAreaContextPackage;", "packageInstance": "new SafeAreaContextPackage()", "buildTypes": [], "libraryName": "safeareacontext", "componentDescriptors": ["RNCSafeAreaProviderComponentDescriptor", "RNCSafeAreaViewComponentDescriptor"], "cmakeListsPath": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-screens": {"root": "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\react-native-screens", "name": "react-native-screens", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\react-native-screens\\android", "packageImportPath": "import com.swmansion.rnscreens.RNScreensPackage;", "packageInstance": "new RNScreensPackage()", "buildTypes": [], "libraryName": "rnscreens", "componentDescriptors": ["RNSFullWindowOverlayComponentDescriptor", "RNSScreenContainerComponentDescriptor", "RNSScreenNavigationContainerComponentDescriptor", "RNSScreenStackHeaderConfigComponentDescriptor", "RNSScreenStackHeaderSubviewComponentDescriptor", "RNSScreenStackComponentDescriptor", "RNSSearchBarComponentDescriptor", "RNSScreenComponentDescriptor", "RNSScreenFooterComponentDescriptor", "RNSScreenContentWrapperComponentDescriptor", "RNSModalScreenComponentDescriptor"], "cmakeListsPath": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-vector-icons": {"root": "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\react-native-vector-icons", "name": "react-native-vector-icons", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\react-native-vector-icons\\android", "packageImportPath": "import com.oblador.vectoricons.VectorIconsPackage;", "packageInstance": "new VectorIconsPackage()", "buildTypes": [], "libraryName": "RNVectorIconsSpec", "componentDescriptors": [], "cmakeListsPath": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-webview": {"root": "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\react-native-webview", "name": "react-native-webview", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\react-native-webview\\android", "packageImportPath": "import com.reactnativecommunity.webview.RNCWebViewPackage;", "packageInstance": "new RNCWebViewPackage()", "buildTypes": [], "libraryName": "RNCWebViewSpec", "componentDescriptors": ["RNCWebViewComponentDescriptor"], "cmakeListsPath": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-webview/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}}, "project": {"android": {"packageName": "com.gokul719.snack97152fc1f368437dac54171df4ba22bd", "sourceDir": "C:\\Users\\<USER>\\quiz-bee-techs\\android"}}}