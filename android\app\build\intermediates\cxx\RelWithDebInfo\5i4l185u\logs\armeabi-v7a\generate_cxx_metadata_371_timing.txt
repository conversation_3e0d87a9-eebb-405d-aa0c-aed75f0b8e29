# C/C++ build system timings
generate_cxx_metadata
  [gap of 57ms]
  create-invalidation-state 75ms
  generate-prefab-packages
    [gap of 84ms]
    exec-prefab 1185ms
    [gap of 76ms]
  generate-prefab-packages completed in 1345ms
  execute-generate-process
    exec-configure 1090ms
    [gap of 155ms]
  execute-generate-process completed in 1249ms
  [gap of 90ms]
generate_cxx_metadata completed in 2823ms

