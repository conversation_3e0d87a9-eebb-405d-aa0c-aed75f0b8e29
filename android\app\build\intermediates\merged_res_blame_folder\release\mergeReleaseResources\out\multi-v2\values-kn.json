{"logs": [{"outputFile": "com.gokul719.snack97152fc1f368437dac54171df4ba22bd.app-mergeReleaseResources-74:/values-kn/values-kn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ab468244bce13502a51b6cbb55b0c3ba\\transformed\\material-1.6.1\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,224,306,413,526,611,674,768,834,896,999,1070,1129,1205,1270,1324,1437,1495,1556,1610,1689,1805,1888,1979,2091,2170,2249,2337,2404,2470,2550,2640,2724,2801,2878,2955,3024,3123,3200,3293,3388,3462,3543,3639,3690,3758,3844,3932,3995,4060,4123,4228,4331,4426,4531", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,81,106,112,84,62,93,65,61,102,70,58,75,64,53,112,57,60,53,78,115,82,90,111,78,78,87,66,65,79,89,83,76,76,76,68,98,76,92,94,73,80,95,50,67,85,87,62,64,62,104,102,94,104,81", "endOffsets": "219,301,408,521,606,669,763,829,891,994,1065,1124,1200,1265,1319,1432,1490,1551,1605,1684,1800,1883,1974,2086,2165,2244,2332,2399,2465,2545,2635,2719,2796,2873,2950,3019,3118,3195,3288,3383,3457,3538,3634,3685,3753,3839,3927,3990,4055,4118,4223,4326,4421,4526,4608"}, "to": {"startLines": "19,51,59,60,61,87,139,144,149,151,152,153,154,155,156,157,158,159,160,161,162,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,215", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "797,3789,4616,4723,4836,7816,11779,12262,12655,12785,12888,12959,13018,13094,13159,13213,13326,13384,13445,13499,13578,13911,13994,14085,14197,14276,14355,14443,14510,14576,14656,14746,14830,14907,14984,15061,15130,15229,15306,15399,15494,15568,15649,15745,15796,15864,15950,16038,16101,16166,16229,16334,16437,16532,18041", "endLines": "22,51,59,60,61,87,139,144,149,151,152,153,154,155,156,157,158,159,160,161,162,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,215", "endColumns": "12,81,106,112,84,62,93,65,61,102,70,58,75,64,53,112,57,60,53,78,115,82,90,111,78,78,87,66,65,79,89,83,76,76,76,68,98,76,92,94,73,80,95,50,67,85,87,62,64,62,104,102,94,104,81", "endOffsets": "961,3866,4718,4831,4916,7874,11868,12323,12712,12883,12954,13013,13089,13154,13208,13321,13379,13440,13494,13573,13689,13989,14080,14192,14271,14350,14438,14505,14571,14651,14741,14825,14902,14979,15056,15125,15224,15301,15394,15489,15563,15644,15740,15791,15859,15945,16033,16096,16161,16224,16329,16432,16527,16632,18118"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\495d9b066df23cebe365f0b1f0fc7029\\transformed\\exoplayer-ui-2.18.1\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,316,543,747,842,939,1021,1118,1216,1293,1357,1460,1561,1626,1689,1749,1820,1942,2067,2187,2255,2342,2418,2494,2587,2683,2750,2814,2867,2925,2975,3036,3096,3158,3222,3284,3343,3408,3474,3538,3605,3659,3718,3791,3864", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,94,96,81,96,97,76,63,102,100,64,62,59,70,121,124,119,67,86,75,75,92,95,66,63,52,57,49,60,59,61,63,61,58,64,65,63,66,53,58,72,72,53", "endOffsets": "311,538,742,837,934,1016,1113,1211,1288,1352,1455,1556,1621,1684,1744,1815,1937,2062,2182,2250,2337,2413,2489,2582,2678,2745,2809,2862,2920,2970,3031,3091,3153,3217,3279,3338,3403,3469,3533,3600,3654,3713,3786,3859,3913"}, "to": {"startLines": "2,11,15,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,366,593,7879,7974,8071,8153,8250,8348,8425,8489,8592,8693,8758,8821,8881,8952,9074,9199,9319,9387,9474,9550,9626,9719,9815,9882,10675,10728,10786,10836,10897,10957,11019,11083,11145,11204,11269,11335,11399,11466,11520,11579,11652,11725", "endLines": "10,14,18,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "endColumns": "17,12,12,94,96,81,96,97,76,63,102,100,64,62,59,70,121,124,119,67,86,75,75,92,95,66,63,52,57,49,60,59,61,63,61,58,64,65,63,66,53,58,72,72,53", "endOffsets": "361,588,792,7969,8066,8148,8245,8343,8420,8484,8587,8688,8753,8816,8876,8947,9069,9194,9314,9382,9469,9545,9621,9714,9810,9877,9941,10723,10781,10831,10892,10952,11014,11078,11140,11199,11264,11330,11394,11461,11515,11574,11647,11720,11774"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\46429bd1dac210616d37d633e605f2e8\\transformed\\browser-1.8.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,169,269,385", "endColumns": "113,99,115,100", "endOffsets": "164,264,380,481"}, "to": {"startLines": "83,140,141,142", "startColumns": "4,4,4,4", "startOffsets": "7417,11873,11973,12089", "endColumns": "113,99,115,100", "endOffsets": "7526,11968,12084,12185"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d502ef2bffb1f4edf1dd29201baa894c\\transformed\\play-services-base-18.0.0\\res\\values-kn\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,466,595,706,845,970,1074,1169,1315,1424,1585,1716,1857,2010,2075,2134", "endColumns": "106,165,128,110,138,124,103,94,145,108,160,130,140,152,64,58,80", "endOffsets": "299,465,594,705,844,969,1073,1168,1314,1423,1584,1715,1856,2009,2074,2133,2214"}, "to": {"startLines": "65,66,67,68,69,70,71,72,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5181,5292,5462,5595,5710,5853,5982,6090,6335,6485,6598,6763,6898,7043,7200,7269,7332", "endColumns": "110,169,132,114,142,128,107,98,149,112,164,134,144,156,68,62,84", "endOffsets": "5287,5457,5590,5705,5848,5977,6085,6184,6480,6593,6758,6893,7038,7195,7264,7327,7412"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0309cb612008fa0a7f120a51a87e98c5\\transformed\\play-services-basement-18.4.0\\res\\values-kn\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "73", "startColumns": "4", "startOffsets": "6189", "endColumns": "145", "endOffsets": "6330"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b56249db71bc4564ba5f782195824c95\\transformed\\exoplayer-core-2.18.1\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,193,270,341,422,504,605,698", "endColumns": "73,63,76,70,80,81,100,92,85", "endOffsets": "124,188,265,336,417,499,600,693,779"}, "to": {"startLines": "112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9946,10020,10084,10161,10232,10313,10395,10496,10589", "endColumns": "73,63,76,70,80,81,100,92,85", "endOffsets": "10015,10079,10156,10227,10308,10390,10491,10584,10670"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\659eba141f87d2afd24e0f28711a233e\\transformed\\play-services-ads-23.6.0\\res\\values-kn\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,245,294,351,417,486,594,658,790,913,1050,1101,1152,1274,1371,1414,1506,1542,1579,1630,1708,1752", "endColumns": "45,48,56,65,68,107,63,131,122,136,50,50,121,96,42,91,35,36,50,77,43,55", "endOffsets": "244,293,350,416,485,593,657,789,912,1049,1100,1151,1273,1370,1413,1505,1541,1578,1629,1707,1751,1807"}, "to": {"startLines": "199,200,201,204,205,206,207,208,209,210,211,212,213,214,221,222,223,224,225,226,227,252", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16637,16687,16740,16977,17047,17120,17232,17300,17436,17563,17704,17759,17814,17940,18528,18575,18671,18711,18752,18807,18889,20892", "endColumns": "49,52,60,69,72,111,67,135,126,140,54,54,125,100,46,95,39,40,54,81,47,59", "endOffsets": "16682,16735,16796,17042,17115,17227,17295,17431,17558,17699,17754,17809,17935,18036,18570,18666,18706,18747,18802,18884,18932,20947"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\42358a9cae14349299c1c7a268311762\\transformed\\appcompat-1.7.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,331,444,529,636,762,840,916,1007,1100,1195,1289,1389,1482,1577,1671,1762,1853,1935,2051,2161,2260,2373,2478,2592,2756,2856", "endColumns": "113,111,112,84,106,125,77,75,90,92,94,93,99,92,94,93,90,90,81,115,109,98,112,104,113,163,99,82", "endOffsets": "214,326,439,524,631,757,835,911,1002,1095,1190,1284,1384,1477,1572,1666,1757,1848,1930,2046,2156,2255,2368,2473,2587,2751,2851,2934"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,229", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "966,1080,1192,1305,1390,1497,1623,1701,1777,1868,1961,2056,2150,2250,2343,2438,2532,2623,2714,2796,2912,3022,3121,3234,3339,3453,3617,19018", "endColumns": "113,111,112,84,106,125,77,75,90,92,94,93,99,92,94,93,90,90,81,115,109,98,112,104,113,163,99,82", "endOffsets": "1075,1187,1300,1385,1492,1618,1696,1772,1863,1956,2051,2145,2245,2338,2433,2527,2618,2709,2791,2907,3017,3116,3229,3334,3448,3612,3712,19096"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7a0941004ac45c50b486dd320a2bfc96\\transformed\\core-1.13.1\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,357,463,564,672,800", "endColumns": "97,102,100,105,100,107,127,100", "endOffsets": "148,251,352,458,559,667,795,896"}, "to": {"startLines": "52,53,54,55,56,57,58,242", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3871,3969,4072,4173,4279,4380,4488,20050", "endColumns": "97,102,100,105,100,107,127,100", "endOffsets": "3964,4067,4168,4274,4375,4483,4611,20146"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1c6965b61d3737d12e0f0d1e87d21cde\\transformed\\ui-release\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,286,382,482,571,655,748,839,924,1006,1092,1171,1246,1324,1401,1478,1547", "endColumns": "96,83,95,99,88,83,92,90,84,81,85,78,74,77,76,76,68,117", "endOffsets": "197,281,377,477,566,650,743,834,919,1001,1087,1166,1241,1319,1396,1473,1542,1660"}, "to": {"startLines": "62,63,84,85,86,147,148,202,203,218,219,230,234,237,239,244,245,247", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4921,5018,7531,7627,7727,12478,12562,16801,16892,18289,18371,19101,19420,19659,19808,20223,20300,20447", "endColumns": "96,83,95,99,88,83,92,90,84,81,85,78,74,77,76,76,68,117", "endOffsets": "5013,5097,7622,7722,7811,12557,12650,16887,16972,18366,18452,19175,19490,19732,19880,20295,20364,20560"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7c7a5afab22fc7e54eb8c70c357abcc1\\transformed\\react-android-0.76.9-release\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,206,278,347,428,496,563,638,713,798,879,950,1031,1111,1189,1271,1358,1435,1506,1576,1671,1743,1821,1890", "endColumns": "71,78,71,68,80,67,66,74,74,84,80,70,80,79,77,81,86,76,70,69,94,71,77,68,74", "endOffsets": "122,201,273,342,423,491,558,633,708,793,874,945,1026,1106,1184,1266,1353,1430,1501,1571,1666,1738,1816,1885,1960"}, "to": {"startLines": "50,64,143,145,146,150,163,164,165,216,217,220,228,231,232,233,235,236,238,240,241,243,246,248,249", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3717,5102,12190,12328,12397,12717,13694,13761,13836,18123,18208,18457,18937,19180,19260,19338,19495,19582,19737,19885,19955,20151,20369,20565,20634", "endColumns": "71,78,71,68,80,67,66,74,74,84,80,70,80,79,77,81,86,76,70,69,94,71,77,68,74", "endOffsets": "3784,5176,12257,12392,12473,12780,13756,13831,13906,18203,18284,18523,19013,19255,19333,19415,19577,19654,19803,19950,20045,20218,20442,20629,20704"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7bbb068f5d1da3b4f3e1ef6a35abc59e\\transformed\\foundation-release\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,94", "endOffsets": "138,233"}, "to": {"startLines": "250,251", "startColumns": "4,4", "startOffsets": "20709,20797", "endColumns": "87,94", "endOffsets": "20792,20887"}}]}]}