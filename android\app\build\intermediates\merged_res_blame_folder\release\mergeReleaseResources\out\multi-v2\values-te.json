{"logs": [{"outputFile": "com.gokul719.snack97152fc1f368437dac54171df4ba22bd.app-mergeReleaseResources-74:/values-te/values-te.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ab468244bce13502a51b6cbb55b0c3ba\\transformed\\material-1.6.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,231,317,421,537,626,692,786,853,915,1008,1076,1139,1213,1278,1332,1453,1510,1572,1626,1705,1833,1921,2013,2128,2208,2290,2378,2445,2511,2586,2664,2754,2827,2903,2984,3053,3158,3235,3326,3419,3493,3570,3662,3717,3783,3867,3953,4016,4081,4145,4255,4367,4466,4585", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,85,103,115,88,65,93,66,61,92,67,62,73,64,53,120,56,61,53,78,127,87,91,114,79,81,87,66,65,74,77,89,72,75,80,68,104,76,90,92,73,76,91,54,65,83,85,62,64,63,109,111,98,118,82", "endOffsets": "226,312,416,532,621,687,781,848,910,1003,1071,1134,1208,1273,1327,1448,1505,1567,1621,1700,1828,1916,2008,2123,2203,2285,2373,2440,2506,2581,2659,2749,2822,2898,2979,3048,3153,3230,3321,3414,3488,3565,3657,3712,3778,3862,3948,4011,4076,4140,4250,4362,4461,4580,4663"}, "to": {"startLines": "19,51,59,60,61,87,139,144,149,151,152,153,154,155,156,157,158,159,160,161,162,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,215", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "745,3747,4583,4687,4803,7745,11762,12254,12658,12788,12881,12949,13012,13086,13151,13205,13326,13383,13445,13499,13578,13924,14012,14104,14219,14299,14381,14469,14536,14602,14677,14755,14845,14918,14994,15075,15144,15249,15326,15417,15510,15584,15661,15753,15808,15874,15958,16044,16107,16172,16236,16346,16458,16557,18119", "endLines": "22,51,59,60,61,87,139,144,149,151,152,153,154,155,156,157,158,159,160,161,162,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,215", "endColumns": "12,85,103,115,88,65,93,66,61,92,67,62,73,64,53,120,56,61,53,78,127,87,91,114,79,81,87,66,65,74,77,89,72,75,80,68,104,76,90,92,73,76,91,54,65,83,85,62,64,63,109,111,98,118,82", "endOffsets": "916,3828,4682,4798,4887,7806,11851,12316,12715,12876,12944,13007,13081,13146,13200,13321,13378,13440,13494,13573,13701,14007,14099,14214,14294,14376,14464,14531,14597,14672,14750,14840,14913,14989,15070,15139,15244,15321,15412,15505,15579,15656,15748,15803,15869,15953,16039,16102,16167,16231,16341,16453,16552,16671,18197"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1c6965b61d3737d12e0f0d1e87d21cde\\transformed\\ui-release\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,203,292,389,489,578,667,763,851,935,1019,1109,1186,1268,1348,1427,1504,1573", "endColumns": "97,88,96,99,88,88,95,87,83,83,89,76,81,79,78,76,68,116", "endOffsets": "198,287,384,484,573,662,758,846,930,1014,1104,1181,1263,1343,1422,1499,1568,1685"}, "to": {"startLines": "62,63,84,85,86,147,148,202,203,218,219,230,234,237,239,244,245,247", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4892,4990,7459,7556,7656,12473,12562,16834,16922,18369,18453,19235,19561,19809,19965,20383,20460,20608", "endColumns": "97,88,96,99,88,88,95,87,83,83,89,76,81,79,78,76,68,116", "endOffsets": "4985,5074,7551,7651,7740,12557,12653,16917,17001,18448,18538,19307,19638,19884,20039,20455,20524,20720"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\495d9b066df23cebe365f0b1f0fc7029\\transformed\\exoplayer-ui-2.18.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,502,695,787,879,969,1069,1171,1248,1313,1405,1497,1568,1638,1699,1769,1910,2046,2185,2260,2344,2419,2490,2584,2678,2742,2821,2874,2932,2980,3041,3108,3170,3235,3302,3361,3423,3489,3553,3620,3674,3734,3808,3882", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,91,91,89,99,101,76,64,91,91,70,69,60,69,140,135,138,74,83,74,70,93,93,63,78,52,57,47,60,66,61,64,66,58,61,65,63,66,53,59,73,73,53", "endOffsets": "281,497,690,782,874,964,1064,1166,1243,1308,1400,1492,1563,1633,1694,1764,1905,2041,2180,2255,2339,2414,2485,2579,2673,2737,2816,2869,2927,2975,3036,3103,3165,3230,3297,3356,3418,3484,3548,3615,3669,3729,3803,3877,3931"}, "to": {"startLines": "2,11,15,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,336,552,7811,7903,7995,8085,8185,8287,8364,8429,8521,8613,8684,8754,8815,8885,9026,9162,9301,9376,9460,9535,9606,9700,9794,9858,10647,10700,10758,10806,10867,10934,10996,11061,11128,11187,11249,11315,11379,11446,11500,11560,11634,11708", "endLines": "10,14,18,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "endColumns": "17,12,12,91,91,89,99,101,76,64,91,91,70,69,60,69,140,135,138,74,83,74,70,93,93,63,78,52,57,47,60,66,61,64,66,58,61,65,63,66,53,59,73,73,53", "endOffsets": "331,547,740,7898,7990,8080,8180,8282,8359,8424,8516,8608,8679,8749,8810,8880,9021,9157,9296,9371,9455,9530,9601,9695,9789,9853,9932,10695,10753,10801,10862,10929,10991,11056,11123,11182,11244,11310,11374,11441,11495,11555,11629,11703,11757"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\42358a9cae14349299c1c7a268311762\\transformed\\appcompat-1.7.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,334,447,537,642,761,839,915,1006,1099,1194,1288,1388,1481,1576,1671,1762,1853,1942,2056,2160,2259,2374,2479,2594,2756,2859", "endColumns": "116,111,112,89,104,118,77,75,90,92,94,93,99,92,94,94,90,90,88,113,103,98,114,104,114,161,102,82", "endOffsets": "217,329,442,532,637,756,834,910,1001,1094,1189,1283,1383,1476,1571,1666,1757,1848,1937,2051,2155,2254,2369,2474,2589,2751,2854,2937"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,229", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "921,1038,1150,1263,1353,1458,1577,1655,1731,1822,1915,2010,2104,2204,2297,2392,2487,2578,2669,2758,2872,2976,3075,3190,3295,3410,3572,19152", "endColumns": "116,111,112,89,104,118,77,75,90,92,94,93,99,92,94,94,90,90,88,113,103,98,114,104,114,161,102,82", "endOffsets": "1033,1145,1258,1348,1453,1572,1650,1726,1817,1910,2005,2099,2199,2292,2387,2482,2573,2664,2753,2867,2971,3070,3185,3290,3405,3567,3670,19230"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7a0941004ac45c50b486dd320a2bfc96\\transformed\\core-1.13.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,265,367,468,574,681,805", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "152,260,362,463,569,676,800,901"}, "to": {"startLines": "52,53,54,55,56,57,58,242", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3833,3935,4043,4145,4246,4352,4459,20210", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "3930,4038,4140,4241,4347,4454,4578,20306"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\46429bd1dac210616d37d633e605f2e8\\transformed\\browser-1.8.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,166,274,385", "endColumns": "110,107,110,106", "endOffsets": "161,269,380,487"}, "to": {"startLines": "83,140,141,142", "startColumns": "4,4,4,4", "startOffsets": "7348,11856,11964,12075", "endColumns": "110,107,110,106", "endOffsets": "7454,11959,12070,12177"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7c7a5afab22fc7e54eb8c70c357abcc1\\transformed\\react-android-0.76.9-release\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,206,278,348,430,498,565,640,716,801,883,954,1035,1115,1198,1284,1372,1450,1526,1601,1692,1764,1843,1912", "endColumns": "71,78,71,69,81,67,66,74,75,84,81,70,80,79,82,85,87,77,75,74,90,71,78,68,74", "endOffsets": "122,201,273,343,425,493,560,635,711,796,878,949,1030,1110,1193,1279,1367,1445,1521,1596,1687,1759,1838,1907,1982"}, "to": {"startLines": "50,64,143,145,146,150,163,164,165,216,217,220,228,231,232,233,235,236,238,240,241,243,246,248,249", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3675,5079,12182,12321,12391,12720,13706,13773,13848,18202,18287,18543,19071,19312,19392,19475,19643,19731,19889,20044,20119,20311,20529,20725,20794", "endColumns": "71,78,71,69,81,67,66,74,75,84,81,70,80,79,82,85,87,77,75,74,90,71,78,68,74", "endOffsets": "3742,5153,12249,12386,12468,12783,13768,13843,13919,18282,18364,18609,19147,19387,19470,19556,19726,19804,19960,20114,20205,20378,20603,20789,20864"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7bbb068f5d1da3b4f3e1ef6a35abc59e\\transformed\\foundation-release\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,94", "endOffsets": "138,233"}, "to": {"startLines": "250,251", "startColumns": "4,4", "startOffsets": "20869,20957", "endColumns": "87,94", "endOffsets": "20952,21047"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0309cb612008fa0a7f120a51a87e98c5\\transformed\\play-services-basement-18.4.0\\res\\values-te\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "133", "endOffsets": "328"}, "to": {"startLines": "73", "startColumns": "4", "startOffsets": "6134", "endColumns": "137", "endOffsets": "6267"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d502ef2bffb1f4edf1dd29201baa894c\\transformed\\play-services-base-18.0.0\\res\\values-te\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,299,449,575,686,819,940,1041,1137,1282,1390,1539,1667,1814,1973,2033,2099", "endColumns": "105,149,125,110,132,120,100,95,144,107,148,127,146,158,59,65,77", "endOffsets": "298,448,574,685,818,939,1040,1136,1281,1389,1538,1666,1813,1972,2032,2098,2176"}, "to": {"startLines": "65,66,67,68,69,70,71,72,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5158,5268,5422,5552,5667,5804,5929,6034,6272,6421,6533,6686,6818,6969,7132,7196,7266", "endColumns": "109,153,129,114,136,124,104,99,148,111,152,131,150,162,63,69,81", "endOffsets": "5263,5417,5547,5662,5799,5924,6029,6129,6416,6528,6681,6813,6964,7127,7191,7261,7343"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\659eba141f87d2afd24e0f28711a233e\\transformed\\play-services-ads-23.6.0\\res\\values-te\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,241,291,345,412,483,590,654,807,948,1086,1137,1202,1313,1414,1464,1560,1600,1641,1705,1794,1843", "endColumns": "41,49,53,66,70,106,63,152,140,137,50,64,110,100,49,95,39,40,63,88,48,55", "endOffsets": "240,290,344,411,482,589,653,806,947,1085,1136,1201,1312,1413,1463,1559,1599,1640,1704,1793,1842,1898"}, "to": {"startLines": "199,200,201,204,205,206,207,208,209,210,211,212,213,214,221,222,223,224,225,226,227,252", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16676,16722,16776,17006,17077,17152,17263,17331,17488,17633,17775,17830,17899,18014,18614,18668,18768,18812,18857,18925,19018,21052", "endColumns": "45,53,57,70,74,110,67,156,144,141,54,68,114,104,53,99,43,44,67,92,52,59", "endOffsets": "16717,16771,16829,17072,17147,17258,17326,17483,17628,17770,17825,17894,18009,18114,18663,18763,18807,18852,18920,19013,19066,21107"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b56249db71bc4564ba5f782195824c95\\transformed\\exoplayer-core-2.18.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,195,268,336,416,493,594,687", "endColumns": "71,67,72,67,79,76,100,92,77", "endOffsets": "122,190,263,331,411,488,589,682,760"}, "to": {"startLines": "112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9937,10009,10077,10150,10218,10298,10375,10476,10569", "endColumns": "71,67,72,67,79,76,100,92,77", "endOffsets": "10004,10072,10145,10213,10293,10370,10471,10564,10642"}}]}]}