import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Configure notification behavior
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
  }),
});

class NotificationService {
  constructor() {
    this.expoPushToken = null;
    this.notificationListener = null;
    this.responseListener = null;
  }

  // Initialize notification service
  async initialize() {
    try {
      // Request permissions
      await this.requestPermissions();
      
      // Get push token
      await this.registerForPushNotifications();
      
      // Set up listeners
      this.setupNotificationListeners();
      
      // Schedule default notifications
      await this.scheduleDefaultNotifications();
      
      console.log('Notification service initialized successfully');
    } catch (error) {
      console.error('Error initializing notification service:', error);
    }
  }

  // Request notification permissions
  async requestPermissions() {
    if (Device.isDevice) {
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;
      
      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }
      
      if (finalStatus !== 'granted') {
        console.warn('Failed to get push token for push notification!');
        return false;
      }
      
      return true;
    } else {
      console.warn('Must use physical device for Push Notifications');
      return false;
    }
  }

  // Register for push notifications
  async registerForPushNotifications() {
    if (Device.isDevice) {
      try {
        const token = (await Notifications.getExpoPushTokenAsync()).data;
        this.expoPushToken = token;
        console.log('Expo push token:', token);
        
        // Save token to storage
        await AsyncStorage.setItem('expo_push_token', token);
        
        if (Platform.OS === 'android') {
          await Notifications.setNotificationChannelAsync('default', {
            name: 'default',
            importance: Notifications.AndroidImportance.MAX,
            vibrationPattern: [0, 250, 250, 250],
            lightColor: '#FF231F7C',
          });
        }
      } catch (error) {
        console.error('Error getting push token:', error);
      }
    }
  }

  // Set up notification listeners
  setupNotificationListeners() {
    // Listen for notifications received while app is foregrounded
    this.notificationListener = Notifications.addNotificationReceivedListener(notification => {
      console.log('Notification received:', notification);
    });

    // Listen for user interactions with notifications
    this.responseListener = Notifications.addNotificationResponseReceivedListener(response => {
      console.log('Notification response:', response);
      this.handleNotificationResponse(response);
    });
  }

  // Handle notification responses
  handleNotificationResponse(response) {
    const { notification } = response;
    const { data } = notification.request.content;
    
    // Handle different notification types
    switch (data?.type) {
      case 'study_reminder':
        // Navigate to quiz screen
        console.log('Navigate to quiz screen');
        break;
      case 'streak_reminder':
        // Navigate to home screen
        console.log('Navigate to home screen');
        break;
      case 'achievement':
        // Navigate to achievements screen
        console.log('Navigate to achievements screen');
        break;
      case 'new_content':
        // Navigate to new content
        console.log('Navigate to new content');
        break;
      default:
        console.log('Unknown notification type');
    }
  }

  // Schedule daily study reminders
  async scheduleDailyStudyReminder(hour = 19, minute = 0) {
    try {
      // Cancel existing daily reminders
      await this.cancelNotificationsByType('daily_study_reminder');
      
      const trigger = {
        hour,
        minute,
        repeats: true,
      };

      await Notifications.scheduleNotificationAsync({
        content: {
          title: '📚 Time to Study!',
          body: 'Your daily study session is waiting. Keep up the great work!',
          data: { type: 'study_reminder', category: 'daily' },
          sound: true,
        },
        trigger,
      });

      console.log('Daily study reminder scheduled');
    } catch (error) {
      console.error('Error scheduling daily study reminder:', error);
    }
  }

  // Schedule quiz notifications
  async scheduleQuizReminder(delayMinutes = 60) {
    try {
      const trigger = {
        seconds: delayMinutes * 60,
      };

      await Notifications.scheduleNotificationAsync({
        content: {
          title: '🧠 Quiz Time!',
          body: 'Ready for a quick quiz? Test your knowledge now!',
          data: { type: 'quiz_reminder' },
          sound: true,
        },
        trigger,
      });

      console.log('Quiz reminder scheduled');
    } catch (error) {
      console.error('Error scheduling quiz reminder:', error);
    }
  }

  // Schedule study streak reminders
  async scheduleStreakReminder(streakDays) {
    try {
      if (streakDays > 0) {
        const trigger = {
          hour: 20,
          minute: 0,
          repeats: true,
        };

        await Notifications.scheduleNotificationAsync({
          content: {
            title: `🔥 ${streakDays} Day Streak!`,
            body: 'Amazing! Keep your study streak alive. Study now to maintain it!',
            data: { type: 'streak_reminder', streakDays },
            sound: true,
          },
          trigger,
        });

        console.log('Streak reminder scheduled');
      }
    } catch (error) {
      console.error('Error scheduling streak reminder:', error);
    }
  }

  // Send achievement notification
  async sendAchievementNotification(achievement) {
    try {
      await Notifications.scheduleNotificationAsync({
        content: {
          title: '🏆 Achievement Unlocked!',
          body: `Congratulations! You've earned: ${achievement.title}`,
          data: { type: 'achievement', achievement },
          sound: true,
        },
        trigger: null, // Send immediately
      });

      console.log('Achievement notification sent');
    } catch (error) {
      console.error('Error sending achievement notification:', error);
    }
  }

  // Send new content notification
  async sendNewContentNotification(contentType, contentTitle) {
    try {
      await Notifications.scheduleNotificationAsync({
        content: {
          title: '🆕 New Content Available!',
          body: `Check out the new ${contentType}: ${contentTitle}`,
          data: { type: 'new_content', contentType, contentTitle },
          sound: true,
        },
        trigger: null, // Send immediately
      });

      console.log('New content notification sent');
    } catch (error) {
      console.error('Error sending new content notification:', error);
    }
  }

  // Schedule weekly progress summary
  async scheduleWeeklyProgressSummary() {
    try {
      // Cancel existing weekly summaries
      await this.cancelNotificationsByType('weekly_progress');
      
      const trigger = {
        weekday: 1, // Monday
        hour: 18,
        minute: 0,
        repeats: true,
      };

      await Notifications.scheduleNotificationAsync({
        content: {
          title: '📊 Weekly Progress Summary',
          body: 'See how you performed this week and plan for the next!',
          data: { type: 'weekly_progress' },
          sound: true,
        },
        trigger,
      });

      console.log('Weekly progress summary scheduled');
    } catch (error) {
      console.error('Error scheduling weekly progress summary:', error);
    }
  }

  // Schedule motivational notifications
  async scheduleMotivationalNotifications() {
    try {
      const motivationalMessages = [
        { title: '💪 Stay Strong!', body: 'Every expert was once a beginner. Keep practicing!' },
        { title: '🎯 Focus Time!', body: 'Success is the sum of small efforts repeated daily.' },
        { title: '🌟 You\'re Amazing!', body: 'Believe in yourself and your ability to succeed!' },
        { title: '🚀 Keep Going!', body: 'The only way to do great work is to love what you do.' },
        { title: '📈 Progress Check!', body: 'Small progress is still progress. Keep moving forward!' },
      ];

      // Schedule random motivational messages
      for (let i = 0; i < 3; i++) {
        const randomMessage = motivationalMessages[Math.floor(Math.random() * motivationalMessages.length)];
        const randomHour = 10 + Math.floor(Math.random() * 8); // Between 10 AM and 6 PM
        const randomDay = Math.floor(Math.random() * 7) + 1; // Random day of week

        const trigger = {
          weekday: randomDay,
          hour: randomHour,
          minute: 0,
          repeats: true,
        };

        await Notifications.scheduleNotificationAsync({
          content: {
            title: randomMessage.title,
            body: randomMessage.body,
            data: { type: 'motivational' },
            sound: true,
          },
          trigger,
        });
      }

      console.log('Motivational notifications scheduled');
    } catch (error) {
      console.error('Error scheduling motivational notifications:', error);
    }
  }

  // Schedule default notifications
  async scheduleDefaultNotifications() {
    try {
      // Check if notifications are enabled
      const notificationsEnabled = await AsyncStorage.getItem('notifications_enabled');
      if (notificationsEnabled === 'false') {
        return;
      }

      // Schedule daily study reminder
      await this.scheduleDailyStudyReminder();
      
      // Schedule weekly progress summary
      await this.scheduleWeeklyProgressSummary();
      
      // Schedule motivational notifications
      await this.scheduleMotivationalNotifications();
      
      console.log('Default notifications scheduled');
    } catch (error) {
      console.error('Error scheduling default notifications:', error);
    }
  }

  // Cancel notifications by type
  async cancelNotificationsByType(type) {
    try {
      const scheduledNotifications = await Notifications.getAllScheduledNotificationsAsync();
      const notificationsToCancel = scheduledNotifications.filter(
        notification => notification.content.data?.type === type
      );

      for (const notification of notificationsToCancel) {
        await Notifications.cancelScheduledNotificationAsync(notification.identifier);
      }

      console.log(`Cancelled ${notificationsToCancel.length} notifications of type: ${type}`);
    } catch (error) {
      console.error('Error cancelling notifications:', error);
    }
  }

  // Cancel all notifications
  async cancelAllNotifications() {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync();
      console.log('All notifications cancelled');
    } catch (error) {
      console.error('Error cancelling all notifications:', error);
    }
  }

  // Get scheduled notifications
  async getScheduledNotifications() {
    try {
      const notifications = await Notifications.getAllScheduledNotificationsAsync();
      console.log('Scheduled notifications:', notifications.length);
      return notifications;
    } catch (error) {
      console.error('Error getting scheduled notifications:', error);
      return [];
    }
  }

  // Enable/disable notifications
  async setNotificationsEnabled(enabled) {
    try {
      await AsyncStorage.setItem('notifications_enabled', enabled.toString());
      
      if (enabled) {
        await this.scheduleDefaultNotifications();
      } else {
        await this.cancelAllNotifications();
      }
      
      console.log(`Notifications ${enabled ? 'enabled' : 'disabled'}`);
    } catch (error) {
      console.error('Error setting notifications enabled:', error);
    }
  }

  // Check if notifications are enabled
  async areNotificationsEnabled() {
    try {
      const enabled = await AsyncStorage.getItem('notifications_enabled');
      return enabled !== 'false'; // Default to true
    } catch (error) {
      console.error('Error checking notifications enabled:', error);
      return true;
    }
  }

  // Cleanup listeners
  cleanup() {
    if (this.notificationListener) {
      Notifications.removeNotificationSubscription(this.notificationListener);
    }
    if (this.responseListener) {
      Notifications.removeNotificationSubscription(this.responseListener);
    }
  }
}

// Create singleton instance
const notificationService = new NotificationService();

export default notificationService;
