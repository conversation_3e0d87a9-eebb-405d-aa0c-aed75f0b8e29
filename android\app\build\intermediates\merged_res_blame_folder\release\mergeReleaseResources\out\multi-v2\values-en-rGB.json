{"logs": [{"outputFile": "com.gokul719.snack97152fc1f368437dac54171df4ba22bd.app-mergeReleaseResources-74:/values-en-rGB/values-en-rGB.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\46429bd1dac210616d37d633e605f2e8\\transformed\\browser-1.8.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,153,250,359", "endColumns": "97,96,108,98", "endOffsets": "148,245,354,453"}, "to": {"startLines": "82,139,140,141", "startColumns": "4,4,4,4", "startOffsets": "7032,11229,11326,11435", "endColumns": "97,96,108,98", "endOffsets": "7125,11321,11430,11529"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7a0941004ac45c50b486dd320a2bfc96\\transformed\\core-1.13.1\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "51,52,53,54,55,56,57,229", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3612,3708,3810,3909,4008,4112,4215,18143", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "3703,3805,3904,4003,4107,4210,4326,18239"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b56249db71bc4564ba5f782195824c95\\transformed\\exoplayer-core-2.18.1\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,187,252,316,393,458,548,633", "endColumns": "69,61,64,63,76,64,89,84,68", "endOffsets": "120,182,247,311,388,453,543,628,697"}, "to": {"startLines": "111,112,113,114,115,116,117,118,119", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9377,9447,9509,9574,9638,9715,9780,9870,9955", "endColumns": "69,61,64,63,76,64,89,84,68", "endOffsets": "9442,9504,9569,9633,9710,9775,9865,9950,10019"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7bbb068f5d1da3b4f3e1ef6a35abc59e\\transformed\\foundation-release\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,84", "endOffsets": "136,221"}, "to": {"startLines": "235,236", "startColumns": "4,4", "startOffsets": "18656,18742", "endColumns": "85,84", "endOffsets": "18737,18822"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d502ef2bffb1f4edf1dd29201baa894c\\transformed\\play-services-base-18.0.0\\res\\values-en-rGB\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,298,446,567,670,817,936,1048,1147,1302,1403,1551,1672,1814,1958,2017,2075", "endColumns": "100,147,120,102,146,118,111,98,154,100,147,120,141,143,58,57,74", "endOffsets": "297,445,566,669,816,935,1047,1146,1301,1402,1550,1671,1813,1957,2016,2074,2149"}, "to": {"startLines": "64,65,66,67,68,69,70,71,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4873,4978,5130,5255,5362,5513,5636,5752,5993,6152,6257,6409,6534,6680,6828,6891,6953", "endColumns": "104,151,124,106,150,122,115,102,158,104,151,124,145,147,62,61,78", "endOffsets": "4973,5125,5250,5357,5508,5631,5747,5850,6147,6252,6404,6529,6675,6823,6886,6948,7027"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7c7a5afab22fc7e54eb8c70c357abcc1\\transformed\\react-android-0.76.9-release\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,214,288,364,446,526,604,684,758", "endColumns": "75,82,73,75,81,79,77,79,73,73", "endOffsets": "126,209,283,359,441,521,599,679,753,827"}, "to": {"startLines": "63,143,159,160,211,212,222,225,232,234", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "4797,11601,12798,12872,16935,17017,17611,17845,18387,18582", "endColumns": "75,82,73,75,81,79,77,79,73,73", "endOffsets": "4868,11679,12867,12943,17012,17092,17684,17920,18456,18651"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\659eba141f87d2afd24e0f28711a233e\\transformed\\play-services-ads-23.6.0\\res\\values-en-rGB\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "203,244,293,346,409,479,571,633,752,857,972,1022,1080,1194,1273,1311,1382,1416,1451,1500,1564,1599", "endColumns": "40,48,52,62,69,91,61,118,104,114,49,57,113,78,37,70,33,34,48,63,34,55", "endOffsets": "243,292,345,408,478,570,632,751,856,971,1021,1079,1193,1272,1310,1381,1415,1450,1499,1563,1598,1654"}, "to": {"startLines": "194,195,196,199,200,201,202,203,204,205,206,207,208,209,215,216,217,218,219,220,221,237", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15558,15603,15656,15886,15953,16027,16123,16189,16312,16421,16540,16594,16656,16774,17257,17299,17374,17412,17451,17504,17572,18827", "endColumns": "44,52,56,66,73,95,65,122,108,118,53,61,117,82,41,74,37,38,52,67,38,59", "endOffsets": "15598,15651,15708,15948,16022,16118,16184,16307,16416,16535,16589,16651,16769,16852,17294,17369,17407,17446,17499,17567,17606,18882"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\495d9b066df23cebe365f0b1f0fc7029\\transformed\\exoplayer-ui-2.18.1\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,479,656,738,820,898,985,1070,1137,1200,1292,1384,1449,1512,1574,1645,1755,1866,1976,2043,2123,2194,2261,2346,2431,2494,2558,2611,2669,2717,2778,2843,2905,2970,3041,3099,3157,3223,3287,3353,3405,3467,3543,3619", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,81,81,77,86,84,66,62,91,91,64,62,61,70,109,110,109,66,79,70,66,84,84,62,63,52,57,47,60,64,61,64,70,57,57,65,63,65,51,61,75,75,53", "endOffsets": "280,474,651,733,815,893,980,1065,1132,1195,1287,1379,1444,1507,1569,1640,1750,1861,1971,2038,2118,2189,2256,2341,2426,2489,2553,2606,2664,2712,2773,2838,2900,2965,3036,3094,3152,3218,3282,3348,3400,3462,3538,3614,3668"}, "to": {"startLines": "2,11,15,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,529,7475,7557,7639,7717,7804,7889,7956,8019,8111,8203,8268,8331,8393,8464,8574,8685,8795,8862,8942,9013,9080,9165,9250,9313,10024,10077,10135,10183,10244,10309,10371,10436,10507,10565,10623,10689,10753,10819,10871,10933,11009,11085", "endLines": "10,14,18,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137", "endColumns": "17,12,12,81,81,77,86,84,66,62,91,91,64,62,61,70,109,110,109,66,79,70,66,84,84,62,63,52,57,47,60,64,61,64,70,57,57,65,63,65,51,61,75,75,53", "endOffsets": "330,524,701,7552,7634,7712,7799,7884,7951,8014,8106,8198,8263,8326,8388,8459,8569,8680,8790,8857,8937,9008,9075,9160,9245,9308,9372,10072,10130,10178,10239,10304,10366,10431,10502,10560,10618,10684,10748,10814,10866,10928,11004,11080,11134"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ab468244bce13502a51b6cbb55b0c3ba\\transformed\\material-1.6.1\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,227,304,402,517,596,661,751,818,877,967,1031,1094,1163,1227,1281,1393,1451,1513,1567,1639,1761,1848,1929,2039,2116,2197,2288,2355,2421,2491,2568,2655,2726,2803,2872,2941,3032,3104,3193,3282,3356,3428,3514,3564,3630,3710,3794,3856,3920,3983,4083,4180,4272,4371", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,76,97,114,78,64,89,66,58,89,63,62,68,63,53,111,57,61,53,71,121,86,80,109,76,80,90,66,65,69,76,86,70,76,68,68,90,71,88,88,73,71,85,49,65,79,83,61,63,62,99,96,91,98,77", "endOffsets": "222,299,397,512,591,656,746,813,872,962,1026,1089,1158,1222,1276,1388,1446,1508,1562,1634,1756,1843,1924,2034,2111,2192,2283,2350,2416,2486,2563,2650,2721,2798,2867,2936,3027,3099,3188,3277,3351,3423,3509,3559,3625,3705,3789,3851,3915,3978,4078,4175,4267,4366,4444"}, "to": {"startLines": "19,50,58,59,60,86,138,142,146,147,148,149,150,151,152,153,154,155,156,157,158,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,210", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "706,3535,4331,4429,4544,7410,11139,11534,11855,11914,12004,12068,12131,12200,12264,12318,12430,12488,12550,12604,12676,12948,13035,13116,13226,13303,13384,13475,13542,13608,13678,13755,13842,13913,13990,14059,14128,14219,14291,14380,14469,14543,14615,14701,14751,14817,14897,14981,15043,15107,15170,15270,15367,15459,16857", "endLines": "22,50,58,59,60,86,138,142,146,147,148,149,150,151,152,153,154,155,156,157,158,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,210", "endColumns": "12,76,97,114,78,64,89,66,58,89,63,62,68,63,53,111,57,61,53,71,121,86,80,109,76,80,90,66,65,69,76,86,70,76,68,68,90,71,88,88,73,71,85,49,65,79,83,61,63,62,99,96,91,98,77", "endOffsets": "873,3607,4424,4539,4618,7470,11224,11596,11909,11999,12063,12126,12195,12259,12313,12425,12483,12545,12599,12671,12793,13030,13111,13221,13298,13379,13470,13537,13603,13673,13750,13837,13908,13985,14054,14123,14214,14286,14375,14464,14538,14610,14696,14746,14812,14892,14976,15038,15102,15165,15265,15362,15454,15553,16930"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\42358a9cae14349299c1c7a268311762\\transformed\\appcompat-1.7.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,2762", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,2840"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,223", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "878,982,1082,1190,1274,1374,1489,1567,1642,1733,1826,1921,2015,2115,2208,2303,2397,2488,2579,2661,2764,2867,2966,3071,3175,3279,3435,17689", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "977,1077,1185,1269,1369,1484,1562,1637,1728,1821,1916,2010,2110,2203,2298,2392,2483,2574,2656,2759,2862,2961,3066,3170,3274,3430,3530,17767"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0309cb612008fa0a7f120a51a87e98c5\\transformed\\play-services-basement-18.4.0\\res\\values-en-rGB\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "133", "endOffsets": "332"}, "to": {"startLines": "72", "startColumns": "4", "startOffsets": "5855", "endColumns": "137", "endOffsets": "5988"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1c6965b61d3737d12e0f0d1e87d21cde\\transformed\\ui-release\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,279,373,472,559,641,730,819,903,981,1063,1136,1212,1284,1354,1431,1497", "endColumns": "91,81,93,98,86,81,88,88,83,77,81,72,75,71,69,76,65,120", "endOffsets": "192,274,368,467,554,636,725,814,898,976,1058,1131,1207,1279,1349,1426,1492,1613"}, "to": {"startLines": "61,62,83,84,85,144,145,197,198,213,214,224,226,227,228,230,231,233", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4623,4715,7130,7224,7323,11684,11766,15713,15802,17097,17175,17772,17925,18001,18073,18244,18321,18461", "endColumns": "91,81,93,98,86,81,88,88,83,77,81,72,75,71,69,76,65,120", "endOffsets": "4710,4792,7219,7318,7405,11761,11850,15797,15881,17170,17252,17840,17996,18068,18138,18316,18382,18577"}}]}]}