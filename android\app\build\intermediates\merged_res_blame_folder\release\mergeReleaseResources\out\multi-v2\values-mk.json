{"logs": [{"outputFile": "com.gokul719.snack97152fc1f368437dac54171df4ba22bd.app-mergeReleaseResources-74:/values-mk/values-mk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1c6965b61d3737d12e0f0d1e87d21cde\\transformed\\ui-release\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,299,395,498,583,660,750,842,926,1010,1099,1171,1248,1326,1402,1483,1554", "endColumns": "103,89,95,102,84,76,89,91,83,83,88,71,76,77,75,80,70,120", "endOffsets": "204,294,390,493,578,655,745,837,921,1005,1094,1166,1243,1321,1397,1478,1549,1670"}, "to": {"startLines": "62,63,84,85,86,147,148,202,203,218,219,230,234,237,239,244,245,247", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4824,4928,7457,7553,7656,12348,12425,16680,16772,18117,18201,18969,19283,19518,19673,20090,20171,20326", "endColumns": "103,89,95,102,84,76,89,91,83,83,88,71,76,77,75,80,70,120", "endOffsets": "4923,5013,7548,7651,7736,12420,12510,16767,16851,18196,18285,19036,19355,19591,19744,20166,20237,20442"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7a0941004ac45c50b486dd320a2bfc96\\transformed\\core-1.13.1\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,450,555,658,774", "endColumns": "97,101,96,97,104,102,115,100", "endOffsets": "148,250,347,445,550,653,769,870"}, "to": {"startLines": "52,53,54,55,56,57,58,242", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3798,3896,3998,4095,4193,4298,4401,19917", "endColumns": "97,101,96,97,104,102,115,100", "endOffsets": "3891,3993,4090,4188,4293,4396,4512,20013"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7c7a5afab22fc7e54eb8c70c357abcc1\\transformed\\react-android-0.76.9-release\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,216,289,358,440,508,575,651,734,821,901,974,1058,1142,1219,1300,1382,1458,1535,1610,1703,1775,1859,1929", "endColumns": "77,82,72,68,81,67,66,75,82,86,79,72,83,83,76,80,81,75,76,74,92,71,83,69,80", "endOffsets": "128,211,284,353,435,503,570,646,729,816,896,969,1053,1137,1214,1295,1377,1453,1530,1605,1698,1770,1854,1924,2005"}, "to": {"startLines": "50,64,143,145,146,150,163,164,165,216,217,220,228,231,232,233,235,236,238,240,241,243,246,248,249", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3638,5018,12054,12197,12266,12579,13578,13645,13721,17950,18037,18290,18797,19041,19125,19202,19360,19442,19596,19749,19824,20018,20242,20447,20517", "endColumns": "77,82,72,68,81,67,66,75,82,86,79,72,83,83,76,80,81,75,76,74,92,71,83,69,80", "endOffsets": "3711,5096,12122,12261,12343,12642,13640,13716,13799,18032,18112,18358,18876,19120,19197,19278,19437,19513,19668,19819,19912,20085,20321,20512,20593"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\42358a9cae14349299c1c7a268311762\\transformed\\appcompat-1.7.0\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,425,511,619,738,822,903,994,1087,1183,1277,1377,1470,1565,1661,1752,1843,1930,2036,2142,2243,2350,2462,2566,2722,2820", "endColumns": "107,103,107,85,107,118,83,80,90,92,95,93,99,92,94,95,90,90,86,105,105,100,106,111,103,155,97,87", "endOffsets": "208,312,420,506,614,733,817,898,989,1082,1178,1272,1372,1465,1560,1656,1747,1838,1925,2031,2137,2238,2345,2457,2561,2717,2815,2903"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,229", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "923,1031,1135,1243,1329,1437,1556,1640,1721,1812,1905,2001,2095,2195,2288,2383,2479,2570,2661,2748,2854,2960,3061,3168,3280,3384,3540,18881", "endColumns": "107,103,107,85,107,118,83,80,90,92,95,93,99,92,94,95,90,90,86,105,105,100,106,111,103,155,97,87", "endOffsets": "1026,1130,1238,1324,1432,1551,1635,1716,1807,1900,1996,2090,2190,2283,2378,2474,2565,2656,2743,2849,2955,3056,3163,3275,3379,3535,3633,18964"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b56249db71bc4564ba5f782195824c95\\transformed\\exoplayer-core-2.18.1\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,186,250,318,395,468,557,642", "endColumns": "69,60,63,67,76,72,88,84,77", "endOffsets": "120,181,245,313,390,463,552,637,715"}, "to": {"startLines": "112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9841,9911,9972,10036,10104,10181,10254,10343,10428", "endColumns": "69,60,63,67,76,72,88,84,77", "endOffsets": "9906,9967,10031,10099,10176,10249,10338,10423,10501"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7bbb068f5d1da3b4f3e1ef6a35abc59e\\transformed\\foundation-release\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,149", "endColumns": "93,95", "endOffsets": "144,240"}, "to": {"startLines": "250,251", "startColumns": "4,4", "startOffsets": "20598,20692", "endColumns": "93,95", "endOffsets": "20687,20783"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0309cb612008fa0a7f120a51a87e98c5\\transformed\\play-services-basement-18.4.0\\res\\values-mk\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "132", "endOffsets": "327"}, "to": {"startLines": "73", "startColumns": "4", "startOffsets": "6120", "endColumns": "136", "endOffsets": "6252"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\46429bd1dac210616d37d633e605f2e8\\transformed\\browser-1.8.0\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,168,273,388", "endColumns": "112,104,114,100", "endOffsets": "163,268,383,484"}, "to": {"startLines": "83,140,141,142", "startColumns": "4,4,4,4", "startOffsets": "7344,11733,11838,11953", "endColumns": "112,104,114,100", "endOffsets": "7452,11833,11948,12049"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\495d9b066df23cebe365f0b1f0fc7029\\transformed\\exoplayer-ui-2.18.1\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,311,516,702,791,881,962,1052,1143,1221,1286,1389,1494,1559,1623,1686,1758,1876,1992,2107,2184,2273,2344,2423,2513,2604,2668,2736,2789,2847,2895,2956,3022,3089,3152,3222,3286,3344,3410,3475,3541,3593,3658,3737,3816", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,88,89,80,89,90,77,64,102,104,64,63,62,71,117,115,114,76,88,70,78,89,90,63,67,52,57,47,60,65,66,62,69,63,57,65,64,65,51,64,78,78,55", "endOffsets": "306,511,697,786,876,957,1047,1138,1216,1281,1384,1489,1554,1618,1681,1753,1871,1987,2102,2179,2268,2339,2418,2508,2599,2663,2731,2784,2842,2890,2951,3017,3084,3147,3217,3281,3339,3405,3470,3536,3588,3653,3732,3811,3867"}, "to": {"startLines": "2,11,15,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,361,566,7807,7896,7986,8067,8157,8248,8326,8391,8494,8599,8664,8728,8791,8863,8981,9097,9212,9289,9378,9449,9528,9618,9709,9773,10506,10559,10617,10665,10726,10792,10859,10922,10992,11056,11114,11180,11245,11311,11363,11428,11507,11586", "endLines": "10,14,18,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "endColumns": "17,12,12,88,89,80,89,90,77,64,102,104,64,63,62,71,117,115,114,76,88,70,78,89,90,63,67,52,57,47,60,65,66,62,69,63,57,65,64,65,51,64,78,78,55", "endOffsets": "356,561,747,7891,7981,8062,8152,8243,8321,8386,8489,8594,8659,8723,8786,8858,8976,9092,9207,9284,9373,9444,9523,9613,9704,9768,9836,10554,10612,10660,10721,10787,10854,10917,10987,11051,11109,11175,11240,11306,11358,11423,11502,11581,11637"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d502ef2bffb1f4edf1dd29201baa894c\\transformed\\play-services-base-18.0.0\\res\\values-mk\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,453,582,688,829,958,1074,1180,1333,1436,1598,1727,1876,2031,2096,2156", "endColumns": "102,156,128,105,140,128,115,105,152,102,161,128,148,154,64,59,74", "endOffsets": "295,452,581,687,828,957,1073,1179,1332,1435,1597,1726,1875,2030,2095,2155,2230"}, "to": {"startLines": "65,66,67,68,69,70,71,72,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5101,5208,5369,5502,5612,5757,5890,6010,6257,6414,6521,6687,6820,6973,7132,7201,7265", "endColumns": "106,160,132,109,144,132,119,109,156,106,165,132,152,158,68,63,78", "endOffsets": "5203,5364,5497,5607,5752,5885,6005,6115,6409,6516,6682,6815,6968,7127,7196,7260,7339"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\659eba141f87d2afd24e0f28711a233e\\transformed\\play-services-ads-23.6.0\\res\\values-mk\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,248,295,371,436,507,610,673,789,895,1018,1072,1128,1237,1336,1382,1483,1518,1551,1606,1693,1742", "endColumns": "48,46,75,64,70,102,62,115,105,122,53,55,108,98,45,100,34,32,54,86,48,55", "endOffsets": "247,294,370,435,506,609,672,788,894,1017,1071,1127,1236,1335,1381,1482,1517,1550,1605,1692,1741,1797"}, "to": {"startLines": "199,200,201,204,205,206,207,208,209,210,211,212,213,214,221,222,223,224,225,226,227,252", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16496,16549,16600,16856,16925,17000,17107,17174,17294,17404,17531,17589,17649,17762,18363,18413,18518,18557,18594,18653,18744,20788", "endColumns": "52,50,79,68,74,106,66,119,109,126,57,59,112,102,49,104,38,36,58,90,52,59", "endOffsets": "16544,16595,16675,16920,16995,17102,17169,17289,17399,17526,17584,17644,17757,17860,18408,18513,18552,18589,18648,18739,18792,20843"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ab468244bce13502a51b6cbb55b0c3ba\\transformed\\material-1.6.1\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,226,308,404,528,615,681,772,842,906,1009,1074,1134,1202,1265,1320,1448,1505,1567,1622,1697,1837,1924,2007,2110,2192,2277,2364,2431,2497,2570,2646,2735,2808,2884,2959,3029,3117,3192,3284,3376,3450,3524,3616,3669,3736,3819,3906,3968,4032,4095,4209,4316,4418,4529", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,81,95,123,86,65,90,69,63,102,64,59,67,62,54,127,56,61,54,74,139,86,82,102,81,84,86,66,65,72,75,88,72,75,74,69,87,74,91,91,73,73,91,52,66,82,86,61,63,62,113,106,101,110,84", "endOffsets": "221,303,399,523,610,676,767,837,901,1004,1069,1129,1197,1260,1315,1443,1500,1562,1617,1692,1832,1919,2002,2105,2187,2272,2359,2426,2492,2565,2641,2730,2803,2879,2954,3024,3112,3187,3279,3371,3445,3519,3611,3664,3731,3814,3901,3963,4027,4090,4204,4311,4413,4524,4609"}, "to": {"startLines": "19,51,59,60,61,87,139,144,149,151,152,153,154,155,156,157,158,159,160,161,162,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,215", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "752,3716,4517,4613,4737,7741,11642,12127,12515,12647,12750,12815,12875,12943,13006,13061,13189,13246,13308,13363,13438,13804,13891,13974,14077,14159,14244,14331,14398,14464,14537,14613,14702,14775,14851,14926,14996,15084,15159,15251,15343,15417,15491,15583,15636,15703,15786,15873,15935,15999,16062,16176,16283,16385,17865", "endLines": "22,51,59,60,61,87,139,144,149,151,152,153,154,155,156,157,158,159,160,161,162,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,215", "endColumns": "12,81,95,123,86,65,90,69,63,102,64,59,67,62,54,127,56,61,54,74,139,86,82,102,81,84,86,66,65,72,75,88,72,75,74,69,87,74,91,91,73,73,91,52,66,82,86,61,63,62,113,106,101,110,84", "endOffsets": "918,3793,4608,4732,4819,7802,11728,12192,12574,12745,12810,12870,12938,13001,13056,13184,13241,13303,13358,13433,13573,13886,13969,14072,14154,14239,14326,14393,14459,14532,14608,14697,14770,14846,14921,14991,15079,15154,15246,15338,15412,15486,15578,15631,15698,15781,15868,15930,15994,16057,16171,16278,16380,16491,17945"}}]}]}