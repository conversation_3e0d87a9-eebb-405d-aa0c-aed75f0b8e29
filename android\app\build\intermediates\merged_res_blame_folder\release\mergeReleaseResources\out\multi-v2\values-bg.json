{"logs": [{"outputFile": "com.gokul719.snack97152fc1f368437dac54171df4ba22bd.app-mergeReleaseResources-74:/values-bg/values-bg.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7a0941004ac45c50b486dd320a2bfc96\\transformed\\core-1.13.1\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,262,364,465,572,677,796", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "147,257,359,460,567,672,791,892"}, "to": {"startLines": "52,53,54,55,56,57,58,242", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3823,3920,4030,4132,4233,4340,4445,20259", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "3915,4025,4127,4228,4335,4440,4559,20355"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0309cb612008fa0a7f120a51a87e98c5\\transformed\\play-services-basement-18.4.0\\res\\values-bg\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "133", "endOffsets": "328"}, "to": {"startLines": "73", "startColumns": "4", "startOffsets": "6211", "endColumns": "137", "endOffsets": "6344"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1c6965b61d3737d12e0f0d1e87d21cde\\transformed\\ui-release\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,301,404,507,591,667,758,849,933,1017,1105,1177,1254,1332,1408,1491,1560", "endColumns": "102,92,102,102,83,75,90,90,83,83,87,71,76,77,75,82,68,120", "endOffsets": "203,296,399,502,586,662,753,844,928,1012,1100,1172,1249,1327,1403,1486,1555,1676"}, "to": {"startLines": "62,63,84,85,86,147,148,202,203,218,219,230,234,237,239,244,245,247", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4881,4984,7579,7682,7785,12637,12713,17018,17109,18457,18541,19321,19633,19869,20024,20435,20518,20669", "endColumns": "102,92,102,102,83,75,90,90,83,83,87,71,76,77,75,82,68,120", "endOffsets": "4979,5072,7677,7780,7864,12708,12799,17104,17188,18536,18624,19388,19705,19942,20095,20513,20582,20785"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d502ef2bffb1f4edf1dd29201baa894c\\transformed\\play-services-base-18.0.0\\res\\values-bg\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,298,459,590,697,860,991,1106,1211,1376,1484,1655,1789,1942,2104,2170,2225", "endColumns": "104,160,130,106,162,130,114,104,164,107,170,133,152,161,65,54,68", "endOffsets": "297,458,589,696,859,990,1105,1210,1375,1483,1654,1788,1941,2103,2169,2224,2293"}, "to": {"startLines": "65,66,67,68,69,70,71,72,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5161,5270,5435,5570,5681,5848,5983,6102,6349,6518,6630,6805,6943,7100,7266,7336,7395", "endColumns": "108,164,134,110,166,134,118,108,168,111,174,137,156,165,69,58,72", "endOffsets": "5265,5430,5565,5676,5843,5978,6097,6206,6513,6625,6800,6938,7095,7261,7331,7390,7463"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ab468244bce13502a51b6cbb55b0c3ba\\transformed\\material-1.6.1\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,220,303,398,531,620,686,783,863,925,1014,1079,1138,1211,1274,1328,1456,1513,1575,1629,1702,1845,1929,2017,2123,2211,2299,2384,2451,2517,2592,2668,2754,2831,2907,2984,3058,3149,3224,3315,3407,3481,3568,3659,3714,3780,3863,3949,4011,4075,4138,4255,4368,4479,4596", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,82,94,132,88,65,96,79,61,88,64,58,72,62,53,127,56,61,53,72,142,83,87,105,87,87,84,66,65,74,75,85,76,75,76,73,90,74,90,91,73,86,90,54,65,82,85,61,63,62,116,112,110,116,85", "endOffsets": "215,298,393,526,615,681,778,858,920,1009,1074,1133,1206,1269,1323,1451,1508,1570,1624,1697,1840,1924,2012,2118,2206,2294,2379,2446,2512,2587,2663,2749,2826,2902,2979,3053,3144,3219,3310,3402,3476,3563,3654,3709,3775,3858,3944,4006,4070,4133,4250,4363,4474,4591,4677"}, "to": {"startLines": "19,51,59,60,61,87,139,144,149,151,152,153,154,155,156,157,158,159,160,161,162,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,215", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "756,3740,4564,4659,4792,7869,11894,12394,12804,12935,13024,13089,13148,13221,13284,13338,13466,13523,13585,13639,13712,14084,14168,14256,14362,14450,14538,14623,14690,14756,14831,14907,14993,15070,15146,15223,15297,15388,15463,15554,15646,15720,15807,15898,15953,16019,16102,16188,16250,16314,16377,16494,16607,16718,18204", "endLines": "22,51,59,60,61,87,139,144,149,151,152,153,154,155,156,157,158,159,160,161,162,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,215", "endColumns": "12,82,94,132,88,65,96,79,61,88,64,58,72,62,53,127,56,61,53,72,142,83,87,105,87,87,84,66,65,74,75,85,76,75,76,73,90,74,90,91,73,86,90,54,65,82,85,61,63,62,116,112,110,116,85", "endOffsets": "916,3818,4654,4787,4876,7930,11986,12469,12861,13019,13084,13143,13216,13279,13333,13461,13518,13580,13634,13707,13850,14163,14251,14357,14445,14533,14618,14685,14751,14826,14902,14988,15065,15141,15218,15292,15383,15458,15549,15641,15715,15802,15893,15948,16014,16097,16183,16245,16309,16372,16489,16602,16713,16830,18285"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\46429bd1dac210616d37d633e605f2e8\\transformed\\browser-1.8.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,166,274,386", "endColumns": "110,107,111,109", "endOffsets": "161,269,381,491"}, "to": {"startLines": "83,140,141,142", "startColumns": "4,4,4,4", "startOffsets": "7468,11991,12099,12211", "endColumns": "110,107,111,109", "endOffsets": "7574,12094,12206,12316"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\495d9b066df23cebe365f0b1f0fc7029\\transformed\\exoplayer-ui-2.18.1\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,500,706,796,887,970,1057,1145,1225,1290,1394,1499,1577,1651,1715,1783,1908,2029,2157,2234,2326,2398,2479,2580,2682,2748,2816,2870,2930,2978,3039,3111,3179,3242,3318,3383,3441,3512,3577,3648,3700,3759,3840,3921", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,89,90,82,86,87,79,64,103,104,77,73,63,67,124,120,127,76,91,71,80,100,101,65,67,53,59,47,60,71,67,62,75,64,57,70,64,70,51,58,80,80,56", "endOffsets": "282,495,701,791,882,965,1052,1140,1220,1285,1389,1494,1572,1646,1710,1778,1903,2024,2152,2229,2321,2393,2474,2575,2677,2743,2811,2865,2925,2973,3034,3106,3174,3237,3313,3378,3436,3507,3572,3643,3695,3754,3835,3916,3973"}, "to": {"startLines": "2,11,15,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,337,550,7935,8025,8116,8199,8286,8374,8454,8519,8623,8728,8806,8880,8944,9012,9137,9258,9386,9463,9555,9627,9708,9809,9911,9977,10732,10786,10846,10894,10955,11027,11095,11158,11234,11299,11357,11428,11493,11564,11616,11675,11756,11837", "endLines": "10,14,18,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "endColumns": "17,12,12,89,90,82,86,87,79,64,103,104,77,73,63,67,124,120,127,76,91,71,80,100,101,65,67,53,59,47,60,71,67,62,75,64,57,70,64,70,51,58,80,80,56", "endOffsets": "332,545,751,8020,8111,8194,8281,8369,8449,8514,8618,8723,8801,8875,8939,9007,9132,9253,9381,9458,9550,9622,9703,9804,9906,9972,10040,10781,10841,10889,10950,11022,11090,11153,11229,11294,11352,11423,11488,11559,11611,11670,11751,11832,11889"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\42358a9cae14349299c1c7a268311762\\transformed\\appcompat-1.7.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,436,522,632,753,833,910,1001,1094,1189,1283,1383,1476,1571,1679,1770,1861,1944,2058,2166,2266,2380,2487,2595,2755,2854", "endColumns": "119,105,104,85,109,120,79,76,90,92,94,93,99,92,94,107,90,90,82,113,107,99,113,106,107,159,98,83", "endOffsets": "220,326,431,517,627,748,828,905,996,1089,1184,1278,1378,1471,1566,1674,1765,1856,1939,2053,2161,2261,2375,2482,2590,2750,2849,2933"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,229", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "921,1041,1147,1252,1338,1448,1569,1649,1726,1817,1910,2005,2099,2199,2292,2387,2495,2586,2677,2760,2874,2982,3082,3196,3303,3411,3571,19237", "endColumns": "119,105,104,85,109,120,79,76,90,92,94,93,99,92,94,107,90,90,82,113,107,99,113,106,107,159,98,83", "endOffsets": "1036,1142,1247,1333,1443,1564,1644,1721,1812,1905,2000,2094,2194,2287,2382,2490,2581,2672,2755,2869,2977,3077,3191,3298,3406,3566,3665,19316"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7c7a5afab22fc7e54eb8c70c357abcc1\\transformed\\react-android-0.76.9-release\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,282,357,445,514,581,661,743,830,910,981,1068,1155,1229,1308,1390,1467,1544,1619,1703,1778,1860,1930", "endColumns": "69,83,72,74,87,68,66,79,81,86,79,70,86,86,73,78,81,76,76,74,83,74,81,69,84", "endOffsets": "120,204,277,352,440,509,576,656,738,825,905,976,1063,1150,1224,1303,1385,1462,1539,1614,1698,1773,1855,1925,2010"}, "to": {"startLines": "50,64,143,145,146,150,163,164,165,216,217,220,228,231,232,233,235,236,238,240,241,243,246,248,249", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3670,5077,12321,12474,12549,12866,13855,13922,14002,18290,18377,18629,19150,19393,19480,19554,19710,19792,19947,20100,20175,20360,20587,20790,20860", "endColumns": "69,83,72,74,87,68,66,79,81,86,79,70,86,86,73,78,81,76,76,74,83,74,81,69,84", "endOffsets": "3735,5156,12389,12544,12632,12930,13917,13997,14079,18372,18452,18695,19232,19475,19549,19628,19787,19864,20019,20170,20254,20430,20664,20855,20940"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b56249db71bc4564ba5f782195824c95\\transformed\\exoplayer-core-2.18.1\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,189,253,327,405,478,575,666", "endColumns": "70,62,63,73,77,72,96,90,75", "endOffsets": "121,184,248,322,400,473,570,661,737"}, "to": {"startLines": "112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "10045,10116,10179,10243,10317,10395,10468,10565,10656", "endColumns": "70,62,63,73,77,72,96,90,75", "endOffsets": "10111,10174,10238,10312,10390,10463,10560,10651,10727"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\659eba141f87d2afd24e0f28711a233e\\transformed\\play-services-ads-23.6.0\\res\\values-bg\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,249,298,370,439,508,618,681,813,924,1038,1088,1138,1246,1337,1391,1495,1530,1567,1627,1716,1759", "endColumns": "49,48,71,68,68,109,62,131,110,113,49,49,107,90,53,103,34,36,59,88,42,55", "endOffsets": "248,297,369,438,507,617,680,812,923,1037,1087,1137,1245,1336,1390,1494,1529,1566,1626,1715,1758,1814"}, "to": {"startLines": "199,200,201,204,205,206,207,208,209,210,211,212,213,214,221,222,223,224,225,226,227,252", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16835,16889,16942,17193,17266,17339,17453,17520,17656,17771,17889,17943,17997,18109,18700,18758,18866,18905,18946,19010,19103,21128", "endColumns": "53,52,75,72,72,113,66,135,114,117,53,53,111,94,57,107,38,40,63,92,46,59", "endOffsets": "16884,16937,17013,17261,17334,17448,17515,17651,17766,17884,17938,17992,18104,18199,18753,18861,18900,18941,19005,19098,19145,21183"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7bbb068f5d1da3b4f3e1ef6a35abc59e\\transformed\\foundation-release\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,94", "endOffsets": "138,233"}, "to": {"startLines": "250,251", "startColumns": "4,4", "startOffsets": "20945,21033", "endColumns": "87,94", "endOffsets": "21028,21123"}}]}]}