import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Platform } from 'react-native';
import AdManager from '../src/services/AdManager';
import { AD_PLACEMENTS } from '../src/config/adConfig';
import SimpleAdBanner from './SimpleAdBanner';

// Try to import native ads, fall back gracefully if not available
let BannerAd, BannerAdSize;
try {
  const GoogleMobileAds = require('react-native-google-mobile-ads');
  BannerAd = GoogleMobileAds.BannerAd;
  BannerAdSize = GoogleMobileAds.BannerAdSize;
} catch (_error) {
  console.log('Native Google Mobile Ads not available, using WebView fallback');
  BannerAd = null;
  BannerAdSize = null;
}

const BannerAdComponent = ({
  size = BannerAdSize?.BANNER || 'banner',
  placement = AD_PLACEMENTS.HOME_BANNER,
  fallbackToWebView = true,
  style = {}
}) => {
  const [adError, setAdError] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const [showFallback, setShowFallback] = useState(!BannerAd); // Show fallback if native ads not available

  const maxRetries = 3;
  const unitId = AdManager.getAdUnitForPlacement(placement);
  const requestConfig = AdManager.getAdRequestConfig(placement);

  useEffect(() => {
    // If native ads are not available, always show fallback
    if (!BannerAd) {
      setShowFallback(true);
      return;
    }

    // Check if this placement has excessive errors
    if (AdManager.hasExcessiveErrors(placement, 'banner')) {
      console.log(`Banner ad blocked for ${placement} due to excessive errors`);
      setShowFallback(true);
    }
  }, [placement]);

  const handleAdLoaded = () => {
    console.log(`Banner ad loaded successfully for ${placement}`);
    setAdError(false);
    setRetryCount(0);
    AdManager.recordImpression(placement, 'banner');
    AdManager.resetErrors(placement, 'banner');
  };

  const handleAdFailedToLoad = (error) => {
    console.error(`Banner ad failed to load for ${placement}:`, error);
    AdManager.recordError(placement, 'banner', error);

    if (retryCount < maxRetries) {
      setRetryCount(prev => prev + 1);
      console.log(`Retrying banner ad load (${retryCount + 1}/${maxRetries})`);
      // The BannerAd component will automatically retry
    } else {
      setAdError(true);
      if (fallbackToWebView) {
        setShowFallback(true);
      }
    }
  };

  // If we should show fallback or there's an error with fallback enabled
  if (showFallback || (adError && fallbackToWebView)) {
    return (
      <View style={[styles.container, style]}>
        <SimpleAdBanner height={60} testMode={false} />
      </View>
    );
  }

  // If there's an error and no fallback, show empty container
  if (adError) {
    return <View style={[styles.container, styles.emptyContainer, style]} />;
  }

  // If native ads are available, render them
  if (BannerAd) {
    return (
      <View style={[styles.container, style]}>
        <BannerAd
          unitId={unitId}
          size={size}
          requestOptions={requestConfig}
          onAdLoaded={handleAdLoaded}
          onAdFailedToLoad={handleAdFailedToLoad}
        />
      </View>
    );
  }

  // Fallback if native ads are not available
  return (
    <View style={[styles.container, style]}>
      <SimpleAdBanner height={60} testMode={false} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    // Add bottom margin for iOS to account for the safe area
    marginBottom: Platform.OS === 'ios' ? 20 : 0,
  },
  emptyContainer: {
    height: 50, // Approximate height of a banner ad
  },
});

export default BannerAdComponent;