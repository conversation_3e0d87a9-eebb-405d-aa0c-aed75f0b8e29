import React, { useEffect, useRef, useState, useContext } from 'react'; // Import useContext
import { View, Text, StyleSheet, TouchableOpacity, Animated, ImageBackground, ScrollView } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { ThemeContext } from './ThemeContext'; // Import ThemeContext
import { StatusBar } from 'expo-status-bar';
import { Audio } from 'expo-av';
import BannerAdComponent from '../components/BannerAdComponent';
import { AD_PLACEMENTS } from '../src/config/adConfig';

const HomeScreen = ({ navigation }) => {
  const { darkMode, soundEnabled, fontSizeMultiplier } = useContext(ThemeContext); // Get fontSizeMultiplier
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  const [sound, setSound] = useState(null);

  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 4,
        useNativeDriver: true,
      }),
    ]).start();
  }, [fadeAnim, scaleAnim]);

  async function playSound() {
    if (!soundEnabled) return; // Check if sound is enabled

    if (sound) {
      await sound.replayAsync();
      return;
    }
    const { sound: newSound } = await Audio.Sound.createAsync(require('../assets/audios/click.mp3'));
    setSound(newSound);
    await newSound.playAsync();
  }

  useEffect(() => {
    return () => {
      if (sound) {
        sound.unloadAsync();
      }
    };
  }, [sound]);

  const handleNavigation = async (screen) => {
    await playSound();
    setTimeout(() => {
      try {
        console.log('Navigating to:', screen);
        if (screen === 'Quiz') {
          // Navigate to Quiz within the drawer using navigate
          console.log('Attempting to navigate to Quiz within drawer using navigate');
          navigation.navigate('Quiz');
        } else if (screen === 'Settings') {
          // Navigate to Settings within the drawer using navigate
          console.log('Attempting to navigate to Settings within drawer using navigate');
          navigation.navigate('Settings');
        } else if (['StudyTools', 'AiChat', 'Favorites', 'StudyPlaylists', 'Notes'].includes(screen)) {
          // Navigate to stack screens
          console.log('Attempting to navigate to stack screen:', screen);
          navigation.navigate(screen);
        } else {
          // Default navigation
          console.log('Attempting default navigation to:', screen);
          navigation.navigate(screen);
        }
      } catch (error) {
        console.error('Navigation error:', error);
      }
    }, 300);
  };

  // Define dynamic styles based on darkMode
  const dynamicStyles = StyleSheet.create({
    overlay: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: darkMode ? 'rgba(0,0,0,0.7)' : 'rgba(0,0,0,0.2)',
    },
    welcomeText: {
      fontSize: 32 * fontSizeMultiplier, // Apply multiplier
      fontWeight: 'bold',
      color: darkMode ? '#E0E0E0' : '#fff',
      marginBottom: 10,
      textAlign: 'center',
    },
    subtitleText: {
      fontSize: 18 * fontSizeMultiplier, // Apply multiplier
      color: darkMode ? '#B0B0B0' : '#ddd',
      marginBottom: 30,
      textAlign: 'center',
    },
    button: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: darkMode ? '#444' : '#ff9800', // Different button color in dark mode
      paddingVertical: 16,
      paddingHorizontal: 28,
      borderRadius: 14,
      marginBottom: 16,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: darkMode ? 0.6 : 0.4,
      shadowRadius: 5,
      elevation: 7,
    },
    buttonText: {
      color: darkMode ? '#E0E0E0' : '#fff',
      fontWeight: 'bold',
      fontSize: 18 * fontSizeMultiplier, // Apply multiplier
      marginLeft: 12,
    },
    iconColor: darkMode ? '#E0E0E0' : '#fff', // Icon color based on theme
  });

  return (
    // Use a different background or adjust overlay based on theme
    <ImageBackground
      source={darkMode ? require('../assets/images/background1.jpg') : require('../assets/images/background.jpg')} // Optional: different background for dark mode
      style={styles.background}
    >
      {/* Re-add LinearGradient and apply dynamic overlay style */}
      <LinearGradient
        colors={darkMode ? ['rgba(0,0,0,0.8)', 'rgba(0,0,0,0.6)'] : ['rgba(0,0,0,0.6)', 'rgba(0,0,0,0.2)']}
        style={styles.overlay} // Use static overlay style here, background color is handled by gradient
      >
        <StatusBar style={darkMode ? "light" : "light"} /> {/* Keep status bar light for both */}
        <ScrollView
          contentContainerStyle={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
          bounces={true}
        >
          <View style={styles.homeContainer}>
          <Animated.Image source={require('../assets/images/logo.png')} style={[styles.logo, { transform: [{ scale: scaleAnim }] }]} />
          {/* Apply dynamic text styles */}
          <Animated.Text style={[dynamicStyles.welcomeText, { opacity: fadeAnim }]}>Welcome to BeeTech!</Animated.Text>
          <Animated.Text style={[dynamicStyles.subtitleText, { opacity: fadeAnim }]}>Enhance your knowledge with fun quizzes!</Animated.Text>

          <Animated.View style={{ opacity: fadeAnim }}>
            {/* Apply dynamic button styles */}

            <TouchableOpacity onPress={() => handleNavigation('Settings')} style={dynamicStyles.button} activeOpacity={0.7} accessibilityLabel="Open settings screen">
              <Ionicons name="settings" size={24} color={dynamicStyles.iconColor} />
              <Text style={dynamicStyles.buttonText}>Settings</Text>
            </TouchableOpacity>
            <TouchableOpacity onPress={() => {console.log('Start Quiz button pressed'); handleNavigation('Quiz');}} style={dynamicStyles.button} activeOpacity={0.7} accessibilityLabel="Start quiz">
              <Ionicons name="game-controller" size={24} color={dynamicStyles.iconColor} />
              <Text style={dynamicStyles.buttonText}>Start Quiz</Text>
            </TouchableOpacity>
            {/* Add Ask Nijeta Button */}
            <TouchableOpacity onPress={() => handleNavigation('AiChat')} style={dynamicStyles.button} activeOpacity={0.7} accessibilityLabel="Ask Nijeta">
              <Ionicons name="chatbubbles-outline" size={24} color={dynamicStyles.iconColor} />
              <Text style={dynamicStyles.buttonText}>Ask Nijeta</Text>
            </TouchableOpacity>

            {/* Add Study Tools Button */}
            <TouchableOpacity onPress={() => handleNavigation('StudyTools')} style={dynamicStyles.button} activeOpacity={0.7} accessibilityLabel="Study Tools">
              <Ionicons name="library-outline" size={24} color={dynamicStyles.iconColor} />
              <Text style={dynamicStyles.buttonText}>Study Tools</Text>
            </TouchableOpacity>

            {/* Add Favorites Button */}
            <TouchableOpacity onPress={() => handleNavigation('Favorites')} style={dynamicStyles.button} activeOpacity={0.7} accessibilityLabel="Favorites">
              <Ionicons name="heart-outline" size={24} color={dynamicStyles.iconColor} />
              <Text style={dynamicStyles.buttonText}>Favorites</Text>
            </TouchableOpacity>

            {/* Add Study Playlists Button */}
            <TouchableOpacity onPress={() => handleNavigation('StudyPlaylists')} style={dynamicStyles.button} activeOpacity={0.7} accessibilityLabel="Study Playlists">
              <Ionicons name="list-outline" size={24} color={dynamicStyles.iconColor} />
              <Text style={dynamicStyles.buttonText}>Playlists</Text>
            </TouchableOpacity>

            {/* Add Notes Button */}
            <TouchableOpacity onPress={() => handleNavigation('Notes')} style={dynamicStyles.button} activeOpacity={0.7} accessibilityLabel="My Notes">
              <Ionicons name="document-text-outline" size={24} color={dynamicStyles.iconColor} />
              <Text style={dynamicStyles.buttonText}>My Notes</Text>
            </TouchableOpacity>
          </Animated.View>
          </View>

          {/* Enhanced Banner Ad */}
          <View style={styles.adContainer}>
            <BannerAdComponent
              placement={AD_PLACEMENTS.HOME_BANNER}
              fallbackToWebView={true}
            />
          </View>
        </ScrollView>
      </LinearGradient>
    </ImageBackground>
  );
};

const styles = StyleSheet.create({
  background: {
    flex: 1,
    resizeMode: 'cover',
  },
  overlay: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingBottom: 100, // Add padding for the ad
  },
  homeContainer: {
    alignItems: 'center',
    padding: 20,
  },
  logo: {
    width: 140,
    height: 140,
    marginBottom: 20,
  },
  welcomeText: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 10,
    textAlign: 'center',
  },
  subtitleText: {
    fontSize: 18,
    color: '#ddd',
    marginBottom: 30,
    textAlign: 'center',
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ff9800',
    paddingVertical: 16,
    paddingHorizontal: 28,
    borderRadius: 14,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.4,
    shadowRadius: 5,
    elevation: 7,
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 18,
    marginLeft: 12,
  },
  adContainer: {
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'transparent',
    marginTop: 20,
  },
});

export default HomeScreen;
