import { Platform } from 'react-native';

// Try to import native ads, fall back gracefully if not available
let mobileAds, BannerAd, BannerAdSize, InterstitialAd, AdEventType, TestIds;
try {
  const GoogleMobileAds = require('react-native-google-mobile-ads');
  mobileAds = GoogleMobileAds.default;
  BannerAd = GoogleMobileAds.BannerAd;
  BannerAdSize = GoogleMobileAds.BannerAdSize;
  InterstitialAd = GoogleMobileAds.InterstitialAd;
  AdEventType = GoogleMobileAds.AdEventType;
  TestIds = GoogleMobileAds.TestIds;
} catch (_error) {
  console.log('Native Google Mobile Ads not available, using fallback');
  mobileAds = null;
  BannerAd = null;
  BannerAdSize = null;
  InterstitialAd = null;
  AdEventType = null;
  TestIds = {
    BANNER: 'ca-app-pub-3940256099942544/6300978111',
    INTERSTITIAL: 'ca-app-pub-3940256099942544/1033173712'
  };
}

// Initialize the Google Mobile Ads SDK with error handling
export const initializeAdMob = () => {
  return new Promise((resolve, reject) => {
    // If native ads are not available, resolve immediately
    if (!mobileAds) {
      console.log('Native Google Mobile Ads not available, skipping initialization');
      resolve({ fallback: true });
      return;
    }

    try {
      console.log('Initializing Google Mobile Ads SDK...');
      // Add a timeout to detect issues
      const timeoutId = setTimeout(() => {
        console.warn('AdMob initialization timed out. This may indicate a native module issue.');
        reject(new Error('AdMob initialization timed out'));
      }, 5000);

      mobileAds()
        .initialize()
        .then(adapterStatuses => {
          clearTimeout(timeoutId);

          // Check adapter statuses to verify initialization
          const isInitialized = Object.values(adapterStatuses).every(
            status => status.state === 1 // 1 means ready
          );

          if (isInitialized) {
            console.log('Google Mobile Ads SDK initialized successfully!');
            resolve(adapterStatuses);
          } else {
            console.warn('Some adapters failed to initialize:',
              Object.entries(adapterStatuses)
                .filter(([_, status]) => status.state !== 1)
                .map(([name, status]) => `${name}: ${status.description}`)
                .join(', ')
            );
            resolve(adapterStatuses); // Still resolve to continue app flow
          }
        })
        .catch(error => {
          clearTimeout(timeoutId);
          console.error('AdMob initialization failed:', error);
          reject(error);
        });
    } catch (error) {
      console.error('Error during AdMob setup:', error);
      reject(error);
    }
  });
};

// Ad unit IDs
// For production, replace these with your actual ad unit IDs
export const adUnitIds = {
  // Banner ad unit IDs
  banner: __DEV__
    ? TestIds.BANNER
    : Platform.select({
        ios: 'ca-app-pub-9706687137550019/4124160377', // Using same banner ad unit ID for iOS
        android: 'ca-app-pub-9706687137550019/4124160377', // Updated with your real Android banner ad unit ID
      }),

  // Interstitial ad unit IDs
  interstitial: __DEV__
    ? TestIds.INTERSTITIAL
    : Platform.select({
        ios: 'ca-app-pub-9706687137550019/7998992050', // Using your real interstitial ad unit ID for iOS
        android: 'ca-app-pub-9706687137550019/7998992050', // Using your real interstitial ad unit ID for Android
      }),
};

// Load and show interstitial ad
export const loadInterstitialAd = (onAdLoaded = () => {}, onAdDismissed = () => {}) => {
  // If native ads are not available, skip interstitial
  if (!InterstitialAd || !AdEventType) {
    console.log('Interstitial ads not available, skipping');
    onAdDismissed();
    return null;
  }

  try {
    const interstitial = InterstitialAd.createForAdRequest(adUnitIds.interstitial);

    // Add event listeners
    const unsubscribeLoaded = interstitial.addAdEventListener(AdEventType.LOADED, () => {
      console.log('Interstitial ad loaded');
      onAdLoaded();
    });

    const unsubscribeClosed = interstitial.addAdEventListener(AdEventType.CLOSED, () => {
      console.log('Interstitial ad closed');
      onAdDismissed();
      // Clean up listeners
      unsubscribeLoaded();
      unsubscribeClosed();
      unsubscribeError();
    });

    const unsubscribeError = interstitial.addAdEventListener(AdEventType.ERROR, (error) => {
      console.error('Interstitial ad error:', error);
      // Still call the dismiss handler so app flow continues
      onAdDismissed();
      // Clean up listeners
      unsubscribeLoaded();
      unsubscribeClosed();
      unsubscribeError();
    });

    // Load the interstitial ad
    interstitial.load();

    return interstitial;
  } catch (error) {
    console.error('Failed to create interstitial ad:', error);
    // Call dismiss handler immediately so app flow continues
    onAdDismissed();
    return null;
  }
};

// Export BannerAd and BannerAdSize for direct use in components
export { BannerAd, BannerAdSize };