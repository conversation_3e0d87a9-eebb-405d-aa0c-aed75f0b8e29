// Enhanced Ad Manager Service with Revenue Optimization
// This service handles all ad-related functionality with advanced revenue optimization

import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  AD_PLACEMENTS,
  AD_FREQUENCY,
  getAdUnitId,
  AD_REQUEST_CONFIG,
  AD_ERROR_CONFIG,
  REVENUE_CONFIG,
  AD_PLACEMENT_PRIORITY
} from '../config/adConfig';

class AdManager {
  constructor() {
    this.interstitialLoadCount = 0;
    this.lastInterstitialShow = 0;
    this.adErrors = {};
    this.adImpressions = {};

    // Enhanced tracking for revenue optimization
    this.sessionStartTime = Date.now();
    this.userEngagementTime = 0;
    this.bannerRefreshCounts = {};
    this.interstitialSessionCount = 0;
    this.revenueOptimizationData = {};
    this.placementPerformance = {};
    this.adVariant = 'default'; // Will be set during initialization
    this.isInitialized = false;
  }

  // Get A/B test variant for ad placements
  async getAdVariant() {
    if (!REVENUE_CONFIG.enableAdPlacementTesting) {
      return 'default';
    }

    const variants = REVENUE_CONFIG.testVariants;
    const savedVariant = await this.getSavedVariant();

    if (savedVariant && variants.includes(savedVariant)) {
      return savedVariant;
    }

    // Assign random variant and save it
    const randomVariant = variants[Math.floor(Math.random() * variants.length)];
    await this.saveVariant(randomVariant);
    return randomVariant;
  }

  // Get saved A/B test variant
  async getSavedVariant() {
    try {
      return await AsyncStorage.getItem('ad_variant') || null;
    } catch {
      return null;
    }
  }

  // Save A/B test variant
  async saveVariant(variant) {
    try {
      await AsyncStorage.setItem('ad_variant', variant);
    } catch (error) {
      console.error('Failed to save ad variant:', error);
    }
  }

  // Start tracking user engagement for revenue optimization
  startEngagementTracking() {
    if (!REVENUE_CONFIG.trackUserEngagement) return;

    this.engagementInterval = setInterval(() => {
      this.userEngagementTime += 1000; // Add 1 second
    }, 1000);
  }

  // Enhanced initialize ad manager
  async initialize() {
    try {
      await this.loadStats();
      await this.loadRevenueOptimizationData();
      this.adVariant = await this.getAdVariant();
      this.startEngagementTracking();
      this.isInitialized = true;
      console.log('Enhanced AdManager initialized successfully');
      console.log(`Using ad variant: ${this.adVariant}`);
    } catch (error) {
      console.error('Failed to initialize AdManager:', error);
    }
  }

  // Load enhanced ad statistics from storage
  async loadStats() {
    try {
      const stats = await AsyncStorage.getItem('ad_stats');
      if (stats) {
        const parsedStats = JSON.parse(stats);
        this.adImpressions = parsedStats.impressions || {};
        this.adErrors = parsedStats.errors || {};
        this.lastInterstitialShow = parsedStats.lastInterstitialShow || 0;
        this.interstitialLoadCount = parsedStats.interstitialLoadCount || 0;
        this.bannerRefreshCounts = parsedStats.bannerRefreshCounts || {};
        this.interstitialSessionCount = parsedStats.interstitialSessionCount || 0;
      }
    } catch (error) {
      console.error('Failed to load ad stats:', error);
    }
  }

  // Load revenue optimization data
  async loadRevenueOptimizationData() {
    try {
      const data = await AsyncStorage.getItem('revenue_optimization_data');
      if (data) {
        this.revenueOptimizationData = JSON.parse(data);
        this.placementPerformance = this.revenueOptimizationData.placementPerformance || {};
      }
    } catch (error) {
      console.error('Failed to load revenue optimization data:', error);
    }
  }

  // Enhanced save ad statistics
  async saveStats() {
    try {
      const stats = {
        impressions: this.adImpressions,
        errors: this.adErrors,
        lastInterstitialShow: this.lastInterstitialShow,
        interstitialLoadCount: this.interstitialLoadCount,
        bannerRefreshCounts: this.bannerRefreshCounts,
        interstitialSessionCount: this.interstitialSessionCount,
        timestamp: Date.now(),
      };
      await AsyncStorage.setItem('ad_stats', JSON.stringify(stats));

      // Save revenue optimization data
      const revenueData = {
        placementPerformance: this.placementPerformance,
        userEngagementTime: this.userEngagementTime,
        adVariant: this.adVariant,
        sessionStartTime: this.sessionStartTime,
        timestamp: Date.now(),
      };
      await AsyncStorage.setItem('revenue_optimization_data', JSON.stringify(revenueData));
    } catch (error) {
      console.error('Failed to save ad stats:', error);
    }
  }

  // Enhanced check if interstitial should be shown with revenue optimization
  shouldShowInterstitial(placement) {
    const now = Date.now();
    const timeSinceLastAd = now - this.lastInterstitialShow;
    const minInterval = AD_FREQUENCY.INTERSTITIAL_MIN_INTERVAL;

    // Check session limits
    if (this.interstitialSessionCount >= REVENUE_CONFIG.interstitialCapping) {
      console.log('Interstitial blocked: Session limit reached');
      return false;
    }

    if (timeSinceLastAd < minInterval) {
      console.log('Interstitial blocked: Too soon since last ad');
      return false;
    }

    // Enhanced logic based on placement and user engagement
    if (placement === AD_PLACEMENTS.QUIZ_INTERSTITIAL) {
      this.interstitialLoadCount++;
      if (this.interstitialLoadCount % AD_FREQUENCY.INTERSTITIAL_AFTER_QUIZ === 0) {
        return this.shouldShowBasedOnEngagement();
      }
    }

    if (placement === AD_PLACEMENTS.CHAPTER_COMPLETE_INTERSTITIAL) {
      return Math.random() < AD_FREQUENCY.CHAPTER_COMPLETE_INTERSTITIAL_CHANCE;
    }

    if (placement === AD_PLACEMENTS.APP_LAUNCH_INTERSTITIAL) {
      const timeSinceAppStart = now - this.sessionStartTime;
      return timeSinceAppStart > AD_FREQUENCY.APP_LAUNCH_INTERSTITIAL_DELAY;
    }

    return false;
  }

  // Check if ad should be shown based on user engagement
  shouldShowBasedOnEngagement() {
    if (!REVENUE_CONFIG.trackUserEngagement) return true;

    const isHighEngagement = this.userEngagementTime > REVENUE_CONFIG.highEngagementThreshold;

    // Show more ads to highly engaged users
    if (isHighEngagement) {
      return true;
    }

    // Be more conservative with less engaged users
    return Math.random() < 0.7; // 70% chance for less engaged users
  }

  // Check if banner should refresh
  shouldRefreshBanner(placement) {
    if (!REVENUE_CONFIG.enableBannerRefresh) return false;

    const refreshCount = this.bannerRefreshCounts[placement] || 0;
    if (refreshCount >= REVENUE_CONFIG.maxBannerRefreshes) {
      return false;
    }

    return true;
  }

  // Record banner refresh
  recordBannerRefresh(placement) {
    this.bannerRefreshCounts[placement] = (this.bannerRefreshCounts[placement] || 0) + 1;
    this.saveStats();
  }

  // Enhanced record ad impression with performance tracking
  recordImpression(placement, adType) {
    const key = `${placement}_${adType}`;
    this.adImpressions[key] = (this.adImpressions[key] || 0) + 1;

    if (adType === 'interstitial') {
      this.lastInterstitialShow = Date.now();
      this.interstitialSessionCount++;
    }

    // Track placement performance
    if (!this.placementPerformance[placement]) {
      this.placementPerformance[placement] = {
        impressions: 0,
        clicks: 0,
        revenue: 0,
        ctr: 0,
      };
    }
    this.placementPerformance[placement].impressions++;

    this.saveStats();
    console.log(`Ad impression recorded: ${key} (Total: ${this.adImpressions[key]})`);
  }

  // Record ad click for performance tracking
  recordClick(placement, adType) {
    const key = `${placement}_${adType}`;

    if (!this.placementPerformance[placement]) {
      this.placementPerformance[placement] = {
        impressions: 0,
        clicks: 0,
        revenue: 0,
        ctr: 0,
      };
    }

    this.placementPerformance[placement].clicks++;

    // Calculate CTR
    const impressions = this.placementPerformance[placement].impressions;
    if (impressions > 0) {
      this.placementPerformance[placement].ctr =
        (this.placementPerformance[placement].clicks / impressions) * 100;
    }

    this.saveStats();
    console.log(`Ad click recorded: ${key}`);
  }

  // Record ad error
  recordError(placement, adType, error) {
    const key = `${placement}_${adType}`;
    this.adErrors[key] = {
      count: (this.adErrors[key]?.count || 0) + 1,
      lastError: error.message || error,
      timestamp: Date.now(),
    };
    console.error(`Ad error recorded for ${key}:`, error);
  }

  // Enhanced get ad unit ID for placement with new placements
  getAdUnitForPlacement(placement) {
    switch (placement) {
      // Banner placements
      case AD_PLACEMENTS.HOME_BANNER:
      case AD_PLACEMENTS.QUIZ_BANNER:
      case AD_PLACEMENTS.CHAPTER_BANNER:
      case AD_PLACEMENTS.SETTINGS_BANNER:
      case AD_PLACEMENTS.AI_CHAT_BANNER:
      case AD_PLACEMENTS.STUDY_TOOLS_BANNER:
      case AD_PLACEMENTS.FAVORITES_BANNER:
      case AD_PLACEMENTS.NOTES_BANNER:
      case AD_PLACEMENTS.PREMIUM_BANNER:
      case AD_PLACEMENTS.MOCK_EXAMS_BANNER:
        return getAdUnitId('banner');

      // Interstitial placements
      case AD_PLACEMENTS.QUIZ_INTERSTITIAL:
      case AD_PLACEMENTS.CHAPTER_COMPLETE_INTERSTITIAL:
      case AD_PLACEMENTS.STUDY_SESSION_INTERSTITIAL:
      case AD_PLACEMENTS.APP_LAUNCH_INTERSTITIAL:
        return getAdUnitId('interstitial');

      // Rewarded placements
      case AD_PLACEMENTS.HINT_REWARDED:
      case AD_PLACEMENTS.PREMIUM_TRIAL_REWARDED:
      case AD_PLACEMENTS.EXTRA_LIVES_REWARDED:
        return getAdUnitId('rewarded');

      // Native placements
      case AD_PLACEMENTS.QUIZ_LIST_NATIVE:
      case AD_PLACEMENTS.STUDY_TOOLS_NATIVE:
        return getAdUnitId('native');

      default:
        return getAdUnitId('banner');
    }
  }

  // Get ad request configuration
  getAdRequestConfig(placement) {
    return {
      ...AD_REQUEST_CONFIG,
      // Add placement-specific keywords
      keywords: [
        ...AD_REQUEST_CONFIG.keywords,
        placement.replace('_', ' '),
      ],
    };
  }

  // Check if placement has too many errors
  hasExcessiveErrors(placement, adType) {
    const key = `${placement}_${adType}`;
    const errorInfo = this.adErrors[key];

    if (!errorInfo) return false;

    const maxErrors = AD_ERROR_CONFIG.maxRetries;
    const errorWindow = 300000; // 5 minutes
    const now = Date.now();

    return (
      errorInfo.count >= maxErrors &&
      (now - errorInfo.timestamp) < errorWindow
    );
  }

  // Reset error count for placement
  resetErrors(placement, adType) {
    const key = `${placement}_${adType}`;
    if (this.adErrors[key]) {
      this.adErrors[key].count = 0;
    }
  }

  // Get ad statistics
  getStats() {
    return {
      impressions: this.adImpressions,
      errors: this.adErrors,
      lastInterstitialShow: this.lastInterstitialShow,
      interstitialLoadCount: this.interstitialLoadCount,
    };
  }


}

// Export singleton instance
export default new AdManager();
