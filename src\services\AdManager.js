// Enhanced Ad Manager Service
// This service handles all ad-related functionality with better error handling and analytics

import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  AD_PLACEMENTS,
  AD_FREQUENCY,
  getAdUnitId,
  AD_REQUEST_CONFIG,
  AD_ERROR_CONFIG
} from '../config/adConfig';

class AdManager {
  constructor() {
    this.interstitialLoadCount = 0;
    this.lastInterstitialShow = 0;
    this.adErrors = {};
    this.adImpressions = {};
  }

  // Initialize ad manager
  async initialize() {
    try {
      // Load ad statistics from storage
      const stats = await AsyncStorage.getItem('ad_stats');
      if (stats) {
        const parsedStats = JSON.parse(stats);
        this.adImpressions = parsedStats.impressions || {};
        this.lastInterstitialShow = parsedStats.lastInterstitialShow || 0;
      }
      console.log('AdManager initialized successfully');
    } catch (error) {
      console.error('Failed to initialize AdManager:', error);
    }
  }

  // Save ad statistics
  async saveStats() {
    try {
      const stats = {
        impressions: this.adImpressions,
        lastInterstitialShow: this.lastInterstitialShow,
        timestamp: Date.now(),
      };
      await AsyncStorage.setItem('ad_stats', JSON.stringify(stats));
    } catch (error) {
      console.error('Failed to save ad stats:', error);
    }
  }

  // Check if interstitial should be shown
  shouldShowInterstitial(placement) {
    const now = Date.now();
    const timeSinceLastAd = now - this.lastInterstitialShow;
    const minInterval = 60000; // 1 minute minimum between interstitials

    if (timeSinceLastAd < minInterval) {
      console.log('Interstitial blocked: Too soon since last ad');
      return false;
    }

    if (placement === AD_PLACEMENTS.QUIZ_INTERSTITIAL) {
      this.interstitialLoadCount++;
      if (this.interstitialLoadCount % AD_FREQUENCY.INTERSTITIAL_AFTER_QUIZ === 0) {
        return true;
      }
    }

    return false;
  }

  // Record ad impression
  recordImpression(placement, adType) {
    const key = `${placement}_${adType}`;
    this.adImpressions[key] = (this.adImpressions[key] || 0) + 1;

    if (adType === 'interstitial') {
      this.lastInterstitialShow = Date.now();
    }

    this.saveStats();
    console.log(`Ad impression recorded: ${key}`);
  }

  // Record ad error
  recordError(placement, adType, error) {
    const key = `${placement}_${adType}`;
    this.adErrors[key] = {
      count: (this.adErrors[key]?.count || 0) + 1,
      lastError: error.message || error,
      timestamp: Date.now(),
    };
    console.error(`Ad error recorded for ${key}:`, error);
  }

  // Get ad unit ID for placement
  getAdUnitForPlacement(placement) {
    switch (placement) {
      case AD_PLACEMENTS.HOME_BANNER:
      case AD_PLACEMENTS.QUIZ_BANNER:
      case AD_PLACEMENTS.CHAPTER_BANNER:
      case AD_PLACEMENTS.SETTINGS_BANNER:
      case AD_PLACEMENTS.AI_CHAT_BANNER:
        return getAdUnitId('banner');

      case AD_PLACEMENTS.QUIZ_INTERSTITIAL:
        return getAdUnitId('interstitial');

      default:
        return getAdUnitId('banner');
    }
  }

  // Get ad request configuration
  getAdRequestConfig(placement) {
    return {
      ...AD_REQUEST_CONFIG,
      // Add placement-specific keywords
      keywords: [
        ...AD_REQUEST_CONFIG.keywords,
        placement.replace('_', ' '),
      ],
    };
  }

  // Check if placement has too many errors
  hasExcessiveErrors(placement, adType) {
    const key = `${placement}_${adType}`;
    const errorInfo = this.adErrors[key];

    if (!errorInfo) return false;

    const maxErrors = AD_ERROR_CONFIG.maxRetries;
    const errorWindow = 300000; // 5 minutes
    const now = Date.now();

    return (
      errorInfo.count >= maxErrors &&
      (now - errorInfo.timestamp) < errorWindow
    );
  }

  // Reset error count for placement
  resetErrors(placement, adType) {
    const key = `${placement}_${adType}`;
    if (this.adErrors[key]) {
      this.adErrors[key].count = 0;
    }
  }

  // Get ad statistics
  getStats() {
    return {
      impressions: this.adImpressions,
      errors: this.adErrors,
      lastInterstitialShow: this.lastInterstitialShow,
      interstitialLoadCount: this.interstitialLoadCount,
    };
  }


}

// Export singleton instance
export default new AdManager();
