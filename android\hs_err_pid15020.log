#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1478592 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=15020, tid=26992
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.7+6 (21.0.7+6) (build 21.0.7+6-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.7+6 (21.0.7+6-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: -XX:MaxMetaspaceSize=512m --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED -Xmx2048m -Dfile.encoding=UTF-8 -Duser.country=IN -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.10.2-all\7iv73wktx1xtkvlq19urqw1wm\gradle-8.10.2\lib\agents\gradle-instrumentation-agent-8.10.2.jar org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.10.2

Host: AMD Ryzen 7 4800H with Radeon Graphics         , 16 cores, 7G,  Windows 11 , 64 bit Build 26100 (10.0.26100.3912)
Time: Tue May 27 16:50:57 2025 India Standard Time elapsed time: 10337.989315 seconds (0d 2h 52m 17s)

---------------  T H R E A D  ---------------

Current thread (0x000001ed2b0eb2f0):  JavaThread "C2 CompilerThread2" daemon [_thread_in_native, id=26992, stack(0x000000cf28600000,0x000000cf28700000) (1024K)]


Current CompileTask:
C2:10337989 38372       4       org.jetbrains.kotlin.metadata.ProtoBuf$Function::writeTo (448 bytes)

Stack: [0x000000cf28600000,0x000000cf28700000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6ce119]
V  [jvm.dll+0x8a84a1]
V  [jvm.dll+0x8aa9ce]
V  [jvm.dll+0x8ab0b3]
V  [jvm.dll+0x27f8a6]
V  [jvm.dll+0xc507d]
V  [jvm.dll+0xc55b3]
V  [jvm.dll+0x3b692c]
V  [jvm.dll+0x382aa5]
V  [jvm.dll+0x381f0a]
V  [jvm.dll+0x247af0]
V  [jvm.dll+0x2470cf]
V  [jvm.dll+0x1c760e]
V  [jvm.dll+0x25695a]
V  [jvm.dll+0x254efa]
V  [jvm.dll+0x3f03f6]
V  [jvm.dll+0x851f6b]
V  [jvm.dll+0x6cc7dd]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0x9c5dc]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001ed1f8aa710, length=257, elements={
0x000001ed603693b0, 0x000001ed7da7d810, 0x000001ed7da80370, 0x000001ed7da87960,
0x000001ed7da883b0, 0x000001ed7da88f30, 0x000001ed7da89ab0, 0x000001ed7da8a440,
0x000001ed7da8b000, 0x000001ed7da4c310, 0x000001ed7dda6f00, 0x000001ed1f0fdef0,
0x000001ed1f3c6a30, 0x000001ed1f3bc1f0, 0x000001ed1f374a30, 0x000001ed1f375750,
0x000001ed1f3750c0, 0x000001ed1f377820, 0x000001ed1f376470, 0x000001ed216b28d0,
0x000001ed216b49a0, 0x000001ed205394e0, 0x000001ed7dba6100, 0x000001ed7dba4d50,
0x000001ed2428c4a0, 0x000001ed24292710, 0x000001ed24293ac0, 0x000001ed24292080,
0x000001ed24290cd0, 0x000001ed24296220, 0x000001ed24296f40, 0x000001ed24297c60,
0x000001ed242982f0, 0x000001ed2147d860, 0x000001ed2147cb40, 0x000001ed2147f2a0,
0x000001ed2147def0, 0x000001ed2147f930, 0x000001ed2147c4b0, 0x000001ed21481a00,
0x000001ed21481370, 0x000001ed2147ffc0, 0x000001ed2147e580, 0x000001ed21482090,
0x000001ed2147ec10, 0x000001ed21482720, 0x000001ed21483440, 0x000001ed21482db0,
0x000001ed21697850, 0x000001ed21697ee0, 0x000001ed216950f0, 0x000001ed21695780,
0x000001ed21699290, 0x000001ed21695e10, 0x000001ed21698c00, 0x000001ed21699920,
0x000001ed216964a0, 0x000001ed21699fb0, 0x000001ed2169c080, 0x000001ed2169a640,
0x000001ed2169acd0, 0x000001ed2169c710, 0x000001ed2169b360, 0x000001ed2053dd10,
0x000001ed2053b5b0, 0x000001ed2053bc40, 0x000001ed2053cff0, 0x000001ed2053d680,
0x000001ed2053a890, 0x000001ed2053fde0, 0x000001ed2053ea30, 0x000001ed2053f0c0,
0x000001ed2053f750, 0x000001ed20540470, 0x000001ed20540b00, 0x000001ed216b2f60,
0x000001ed216b5030, 0x000001ed216b6a70, 0x000001ed216b7790, 0x000001ed216b7e20,
0x000001ed216b84b0, 0x000001ed7dba74b0, 0x000001ed7dba5a70, 0x000001ed7dba6790,
0x000001ed7dba6e20, 0x000001ed7dba46c0, 0x000001ed1f3743a0, 0x000001ed1f375de0,
0x000001ed1f376b00, 0x000001ed1f377190, 0x000001ed21dc29e0, 0x000001ed24890360,
0x000001ed2488dc00, 0x000001ed2488fcd0, 0x000001ed2488e920, 0x000001ed248909f0,
0x000001ed24891080, 0x000001ed24891710, 0x000001ed2488e290, 0x000001ed24891da0,
0x000001ed2488f640, 0x000001ed24892430, 0x000001ed2488efb0, 0x000001ed24895220,
0x000001ed24892ac0, 0x000001ed248937e0, 0x000001ed248958b0, 0x000001ed24894500,
0x000001ed24894b90, 0x000001ed24893e70, 0x000001ed24895f40, 0x000001ed248965d0,
0x000001ed24893150, 0x000001ed24899a50, 0x000001ed2489a0e0, 0x000001ed24898010,
0x000001ed248986a0, 0x000001ed24898d30, 0x000001ed24896c60, 0x000001ed248972f0,
0x000001ed2489a770, 0x000001ed2489ae00, 0x000001ed248993c0, 0x000001ed24897980,
0x000001ed2489b490, 0x000001ed2489c1b0, 0x000001ed2489bb20, 0x000001ed2489c840,
0x000001ed2489ced0, 0x000001ed2070a740, 0x000001ed2070c180, 0x000001ed2070add0,
0x000001ed2070c810, 0x000001ed20709a20, 0x000001ed2070d530, 0x000001ed2070dbc0,
0x000001ed2070e250, 0x000001ed2070e8e0, 0x000001ed2070ef70, 0x000001ed2070baf0,
0x000001ed207109b0, 0x000001ed20710320, 0x000001ed20711040, 0x000001ed2070f600,
0x000001ed2070fc90, 0x000001ed21dc0910, 0x000001ed21dc3070, 0x000001ed21dc0280,
0x000001ed21dc3d90, 0x000001ed21dc4420, 0x000001ed21dc0fa0, 0x000001ed21dc4ab0,
0x000001ed21dc1630, 0x000001ed21dc2350, 0x000001ed21dc5140, 0x000001ed21dc1cc0,
0x000001ed21dc78a0, 0x000001ed21dc8c50, 0x000001ed21dc7f30, 0x000001ed21dc57d0,
0x000001ed21dc85c0, 0x000001ed21dc64f0, 0x000001ed21dc6b80, 0x000001ed21dc92e0,
0x000001ed21dc5e60, 0x000001ed21dccdf0, 0x000001ed21dca000, 0x000001ed21dcc0d0,
0x000001ed21dcad20, 0x000001ed21dcc760, 0x000001ed21dcdb10, 0x000001ed21dca690,
0x000001ed21dce1a0, 0x000001ed21dce830, 0x000001ed21dceec0, 0x000001ed21dcf550,
0x000001ed2428be10, 0x000001ed2428aa60, 0x000001ed2428b0f0, 0x000001ed24289020,
0x000001ed2428b780, 0x000001ed24289d40, 0x000001ed2428cb30, 0x000001ed242896b0,
0x000001ed2428d1c0, 0x000001ed2428d850, 0x000001ed2428a3d0, 0x000001ed24291360,
0x000001ed2428f290, 0x000001ed2428dee0, 0x000001ed2428ffb0, 0x000001ed2428f920,
0x000001ed24292da0, 0x000001ed24295b90, 0x000001ed24290640, 0x000001ed24294e70,
0x000001ed242975d0, 0x000001ed24293430, 0x000001ed2070a0b0, 0x000001ed2070b460,
0x000001ed2070cea0, 0x000001ed242968b0, 0x000001ed29042110, 0x000001ed2903f9b0,
0x000001ed29040040, 0x000001ed29040d60, 0x000001ed2903ec90, 0x000001ed290427a0,
0x000001ed29041a80, 0x000001ed290406d0, 0x000001ed290413f0, 0x000001ed2903f320,
0x000001ed29044f00, 0x000001ed29043b50, 0x000001ed290441e0, 0x000001ed290462b0,
0x000001ed29044870, 0x000001ed29045590, 0x000001ed29042e30, 0x000001ed29045c20,
0x000001ed29046940, 0x000001ed290434c0, 0x000001ed29047660, 0x000001ed29046fd0,
0x000001ed2904aae0, 0x000001ed2904a450, 0x000001ed2904b170, 0x000001ed29048a10,
0x000001ed29047cf0, 0x000001ed290490a0, 0x000001ed2904b800, 0x000001ed2053c2d0,
0x000001ed244cc720, 0x000001ed244cd440, 0x000001ed2a3817e0, 0x000001ed2a368e20,
0x000001ed2a3666c0, 0x000001ed2a36aef0, 0x000001ed2a36b580, 0x000001ed2a36c2a0,
0x000001ed2a36c930, 0x000001ed2a36a1d0, 0x000001ed2a3694b0, 0x000001ed2a36e370,
0x000001ed24774a80, 0x000001ed2b3bca20, 0x000001ed2db33130, 0x000001ed2b3b8a40,
0x000001ed2ae4c7a0, 0x000001ed2fac3770, 0x000001ed7ea350f0, 0x000001ed2ba945d0,
0x000001ed23e2dac0, 0x000001ed30f94910, 0x000001ed2b0eb2f0, 0x000001ed2db14fc0,
0x000001ed2ced2e40
}

Java Threads: ( => current thread )
  0x000001ed603693b0 JavaThread "main"                              [_thread_blocked, id=11620, stack(0x000000cf17300000,0x000000cf17400000) (1024K)]
  0x000001ed7da7d810 JavaThread "Reference Handler"          daemon [_thread_blocked, id=15860, stack(0x000000cf17b00000,0x000000cf17c00000) (1024K)]
  0x000001ed7da80370 JavaThread "Finalizer"                  daemon [_thread_blocked, id=4668, stack(0x000000cf17c00000,0x000000cf17d00000) (1024K)]
  0x000001ed7da87960 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=16884, stack(0x000000cf17d00000,0x000000cf17e00000) (1024K)]
  0x000001ed7da883b0 JavaThread "Attach Listener"            daemon [_thread_blocked, id=30316, stack(0x000000cf17e00000,0x000000cf17f00000) (1024K)]
  0x000001ed7da88f30 JavaThread "Service Thread"             daemon [_thread_blocked, id=10128, stack(0x000000cf17f00000,0x000000cf18000000) (1024K)]
  0x000001ed7da89ab0 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=13884, stack(0x000000cf18000000,0x000000cf18100000) (1024K)]
  0x000001ed7da8a440 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=23444, stack(0x000000cf18100000,0x000000cf18200000) (1024K)]
  0x000001ed7da8b000 JavaThread "C1 CompilerThread0"         daemon [_thread_in_vm, id=28992, stack(0x000000cf18200000,0x000000cf18300000) (1024K)]
  0x000001ed7da4c310 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=30024, stack(0x000000cf18300000,0x000000cf18400000) (1024K)]
  0x000001ed7dda6f00 JavaThread "Notification Thread"        daemon [_thread_blocked, id=15096, stack(0x000000cf18500000,0x000000cf18600000) (1024K)]
  0x000001ed1f0fdef0 JavaThread "Daemon health stats"               [_thread_blocked, id=29408, stack(0x000000cf18e00000,0x000000cf18f00000) (1024K)]
  0x000001ed1f3c6a30 JavaThread "Incoming local TCP Connector on port 58129"        [_thread_in_native, id=27664, stack(0x000000cf18f00000,0x000000cf19000000) (1024K)]
  0x000001ed1f3bc1f0 JavaThread "Daemon periodic checks"            [_thread_blocked, id=12760, stack(0x000000cf19000000,0x000000cf19100000) (1024K)]
  0x000001ed1f374a30 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)"        [_thread_blocked, id=23528, stack(0x000000cf19800000,0x000000cf19900000) (1024K)]
  0x000001ed1f375750 JavaThread "File lock request listener"        [_thread_in_native, id=26424, stack(0x000000cf19900000,0x000000cf19a00000) (1024K)]
  0x000001ed1f3750c0 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\8.10.2\fileHashes)"        [_thread_blocked, id=28916, stack(0x000000cf18600000,0x000000cf18700000) (1024K)]
  0x000001ed1f377820 JavaThread "File watcher server"        daemon [_thread_in_native, id=27252, stack(0x000000cf1a600000,0x000000cf1a700000) (1024K)]
  0x000001ed1f376470 JavaThread "File watcher consumer"      daemon [_thread_blocked, id=19104, stack(0x000000cf1a700000,0x000000cf1a800000) (1024K)]
  0x000001ed216b28d0 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\8.10.2\fileContent)"        [_thread_blocked, id=10268, stack(0x000000cf1a900000,0x000000cf1aa00000) (1024K)]
  0x000001ed216b49a0 JavaThread "jar transforms"                    [_thread_blocked, id=9356, stack(0x000000cf1ac00000,0x000000cf1ad00000) (1024K)]
  0x000001ed205394e0 JavaThread "Memory manager"                    [_thread_in_native, id=13732, stack(0x000000cf1a800000,0x000000cf1a900000) (1024K)]
  0x000001ed7dba6100 JavaThread "RMI Scheduler(0)"           daemon [_thread_blocked, id=11084, stack(0x000000cf21600000,0x000000cf21700000) (1024K)]
  0x000001ed7dba4d50 JavaThread "RMI TCP Accept-0"           daemon [_thread_in_native, id=20448, stack(0x000000cf21a00000,0x000000cf21b00000) (1024K)]
  0x000001ed2428c4a0 JavaThread "jar transforms Thread 2"           [_thread_blocked, id=11488, stack(0x000000cf17000000,0x000000cf17100000) (1024K)]
  0x000001ed24292710 JavaThread "jar transforms Thread 3"           [_thread_blocked, id=5032, stack(0x000000cf22c00000,0x000000cf22d00000) (1024K)]
  0x000001ed24293ac0 JavaThread "jar transforms Thread 4"           [_thread_blocked, id=24032, stack(0x000000cf25100000,0x000000cf25200000) (1024K)]
  0x000001ed24292080 JavaThread "jar transforms Thread 5"           [_thread_blocked, id=24324, stack(0x000000cf25200000,0x000000cf25300000) (1024K)]
  0x000001ed24290cd0 JavaThread "jar transforms Thread 6"           [_thread_blocked, id=508, stack(0x000000cf25300000,0x000000cf25400000) (1024K)]
  0x000001ed24296220 JavaThread "Daemon Thread 2"                   [_thread_blocked, id=13500, stack(0x000000cf17100000,0x000000cf17200000) (1024K)]
  0x000001ed24296f40 JavaThread "Handler for socket connection from /127.0.0.1:58129 to /127.0.0.1:56784"        [_thread_in_native, id=8232, stack(0x000000cf17200000,0x000000cf17300000) (1024K)]
  0x000001ed24297c60 JavaThread "Cancel handler"                    [_thread_blocked, id=7248, stack(0x000000cf18400000,0x000000cf18500000) (1024K)]
  0x000001ed242982f0 JavaThread "Daemon worker Thread 2"            [_thread_blocked, id=25400, stack(0x000000cf18a00000,0x000000cf18b00000) (1024K)]
  0x000001ed2147d860 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:58129 to /127.0.0.1:56784"        [_thread_blocked, id=30216, stack(0x000000cf18b00000,0x000000cf18c00000) (1024K)]
  0x000001ed2147cb40 JavaThread "Stdin handler"                     [_thread_blocked, id=14728, stack(0x000000cf19100000,0x000000cf19200000) (1024K)]
  0x000001ed2147f2a0 JavaThread "Daemon client event forwarder"        [_thread_blocked, id=25468, stack(0x000000cf19200000,0x000000cf19300000) (1024K)]
  0x000001ed2147def0 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\quiz-bee-techs\android\.gradle\8.10.2\fileHashes)"        [_thread_blocked, id=8144, stack(0x000000cf19300000,0x000000cf19400000) (1024K)]
  0x000001ed2147f930 JavaThread "Cache worker for Build Output Cleanup Cache (C:\Users\<USER>\quiz-bee-techs\android\.gradle\buildOutputCleanup)"        [_thread_blocked, id=11836, stack(0x000000cf19400000,0x000000cf19500000) (1024K)]
  0x000001ed2147c4b0 JavaThread "Cache worker for checksums cache (C:\Users\<USER>\quiz-bee-techs\android\.gradle\8.10.2\checksums)"        [_thread_blocked, id=20704, stack(0x000000cf19500000,0x000000cf19600000) (1024K)]
  0x000001ed21481a00 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\8.10.2\md-rule)"        [_thread_blocked, id=14844, stack(0x000000cf19600000,0x000000cf19700000) (1024K)]
  0x000001ed21481370 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\8.10.2\md-supplier)"        [_thread_blocked, id=19468, stack(0x000000cf19700000,0x000000cf19800000) (1024K)]
  0x000001ed2147ffc0 JavaThread "Cache worker for Build Output Cleanup Cache (C:\Users\<USER>\quiz-bee-techs\node_modules\@react-native\gradle-plugin\.gradle\buildOutputCleanup)"        [_thread_blocked, id=25376, stack(0x000000cf19a00000,0x000000cf19b00000) (1024K)]
  0x000001ed2147e580 JavaThread "Unconstrained build operations"        [_thread_blocked, id=14340, stack(0x000000cf1ab00000,0x000000cf1ac00000) (1024K)]
  0x000001ed21482090 JavaThread "Unconstrained build operations Thread 2"        [_thread_blocked, id=12884, stack(0x000000cf1ad00000,0x000000cf1ae00000) (1024K)]
  0x000001ed2147ec10 JavaThread "Unconstrained build operations Thread 3"        [_thread_blocked, id=15924, stack(0x000000cf1ae00000,0x000000cf1af00000) (1024K)]
  0x000001ed21482720 JavaThread "Unconstrained build operations Thread 4"        [_thread_blocked, id=19436, stack(0x000000cf1af00000,0x000000cf1b000000) (1024K)]
  0x000001ed21483440 JavaThread "Unconstrained build operations Thread 5"        [_thread_blocked, id=28428, stack(0x000000cf1b000000,0x000000cf1b100000) (1024K)]
  0x000001ed21482db0 JavaThread "Unconstrained build operations Thread 6"        [_thread_blocked, id=17116, stack(0x000000cf1b100000,0x000000cf1b200000) (1024K)]
  0x000001ed21697850 JavaThread "Unconstrained build operations Thread 7"        [_thread_blocked, id=26396, stack(0x000000cf1b200000,0x000000cf1b300000) (1024K)]
  0x000001ed21697ee0 JavaThread "Unconstrained build operations Thread 8"        [_thread_blocked, id=9804, stack(0x000000cf1b300000,0x000000cf1b400000) (1024K)]
  0x000001ed216950f0 JavaThread "Unconstrained build operations Thread 9"        [_thread_blocked, id=4264, stack(0x000000cf1b400000,0x000000cf1b500000) (1024K)]
  0x000001ed21695780 JavaThread "Unconstrained build operations Thread 10"        [_thread_blocked, id=23948, stack(0x000000cf1b500000,0x000000cf1b600000) (1024K)]
  0x000001ed21699290 JavaThread "Unconstrained build operations Thread 11"        [_thread_blocked, id=25916, stack(0x000000cf1b600000,0x000000cf1b700000) (1024K)]
  0x000001ed21695e10 JavaThread "Unconstrained build operations Thread 12"        [_thread_blocked, id=15668, stack(0x000000cf1b700000,0x000000cf1b800000) (1024K)]
  0x000001ed21698c00 JavaThread "Unconstrained build operations Thread 13"        [_thread_blocked, id=28100, stack(0x000000cf1b800000,0x000000cf1b900000) (1024K)]
  0x000001ed21699920 JavaThread "Unconstrained build operations Thread 14"        [_thread_blocked, id=15688, stack(0x000000cf1b900000,0x000000cf1ba00000) (1024K)]
  0x000001ed216964a0 JavaThread "Unconstrained build operations Thread 15"        [_thread_blocked, id=10928, stack(0x000000cf1ba00000,0x000000cf1bb00000) (1024K)]
  0x000001ed21699fb0 JavaThread "Unconstrained build operations Thread 16"        [_thread_blocked, id=2936, stack(0x000000cf1bb00000,0x000000cf1bc00000) (1024K)]
  0x000001ed2169c080 JavaThread "Unconstrained build operations Thread 17"        [_thread_blocked, id=28844, stack(0x000000cf1bc00000,0x000000cf1bd00000) (1024K)]
  0x000001ed2169a640 JavaThread "Unconstrained build operations Thread 18"        [_thread_blocked, id=2152, stack(0x000000cf1bd00000,0x000000cf1be00000) (1024K)]
  0x000001ed2169acd0 JavaThread "Unconstrained build operations Thread 19"        [_thread_blocked, id=22764, stack(0x000000cf1be00000,0x000000cf1bf00000) (1024K)]
  0x000001ed2169c710 JavaThread "Unconstrained build operations Thread 20"        [_thread_blocked, id=832, stack(0x000000cf1bf00000,0x000000cf1c000000) (1024K)]
  0x000001ed2169b360 JavaThread "Unconstrained build operations Thread 21"        [_thread_blocked, id=18116, stack(0x000000cf1c000000,0x000000cf1c100000) (1024K)]
  0x000001ed2053dd10 JavaThread "Unconstrained build operations Thread 22"        [_thread_blocked, id=4480, stack(0x000000cf1c100000,0x000000cf1c200000) (1024K)]
  0x000001ed2053b5b0 JavaThread "Unconstrained build operations Thread 23"        [_thread_blocked, id=24096, stack(0x000000cf1c200000,0x000000cf1c300000) (1024K)]
  0x000001ed2053bc40 JavaThread "Unconstrained build operations Thread 24"        [_thread_blocked, id=26752, stack(0x000000cf1c300000,0x000000cf1c400000) (1024K)]
  0x000001ed2053cff0 JavaThread "Unconstrained build operations Thread 25"        [_thread_blocked, id=16520, stack(0x000000cf1c400000,0x000000cf1c500000) (1024K)]
  0x000001ed2053d680 JavaThread "Unconstrained build operations Thread 26"        [_thread_blocked, id=13188, stack(0x000000cf1c500000,0x000000cf1c600000) (1024K)]
  0x000001ed2053a890 JavaThread "Unconstrained build operations Thread 27"        [_thread_blocked, id=16044, stack(0x000000cf1c600000,0x000000cf1c700000) (1024K)]
  0x000001ed2053fde0 JavaThread "Unconstrained build operations Thread 28"        [_thread_blocked, id=13872, stack(0x000000cf1c700000,0x000000cf1c800000) (1024K)]
  0x000001ed2053ea30 JavaThread "Unconstrained build operations Thread 29"        [_thread_blocked, id=3076, stack(0x000000cf1c800000,0x000000cf1c900000) (1024K)]
  0x000001ed2053f0c0 JavaThread "Unconstrained build operations Thread 30"        [_thread_blocked, id=16768, stack(0x000000cf1c900000,0x000000cf1ca00000) (1024K)]
  0x000001ed2053f750 JavaThread "Unconstrained build operations Thread 31"        [_thread_blocked, id=29796, stack(0x000000cf1ca00000,0x000000cf1cb00000) (1024K)]
  0x000001ed20540470 JavaThread "Unconstrained build operations Thread 32"        [_thread_blocked, id=9124, stack(0x000000cf1cb00000,0x000000cf1cc00000) (1024K)]
  0x000001ed20540b00 JavaThread "Unconstrained build operations Thread 33"        [_thread_blocked, id=27960, stack(0x000000cf1cc00000,0x000000cf1cd00000) (1024K)]
  0x000001ed216b2f60 JavaThread "Unconstrained build operations Thread 34"        [_thread_blocked, id=30660, stack(0x000000cf1cd00000,0x000000cf1ce00000) (1024K)]
  0x000001ed216b5030 JavaThread "build event listener"              [_thread_blocked, id=24484, stack(0x000000cf1ce00000,0x000000cf1cf00000) (1024K)]
  0x000001ed216b6a70 JavaThread "ForkJoinPool.commonPool-worker-13" daemon [_thread_blocked, id=28920, stack(0x000000cf1d000000,0x000000cf1d100000) (1024K)]
  0x000001ed216b7790 JavaThread "ForkJoinPool.commonPool-worker-15" daemon [_thread_blocked, id=26184, stack(0x000000cf1d200000,0x000000cf1d300000) (1024K)]
  0x000001ed216b7e20 JavaThread "ForkJoinPool.commonPool-worker-16" daemon [_thread_blocked, id=29232, stack(0x000000cf1d300000,0x000000cf1d400000) (1024K)]
  0x000001ed216b84b0 JavaThread "ForkJoinPool.commonPool-worker-17" daemon [_thread_blocked, id=24892, stack(0x000000cf1d400000,0x000000cf1d500000) (1024K)]
  0x000001ed7dba74b0 JavaThread "ForkJoinPool.commonPool-worker-18" daemon [_thread_blocked, id=26144, stack(0x000000cf1d500000,0x000000cf1d600000) (1024K)]
  0x000001ed7dba5a70 JavaThread "Execution worker"                  [_thread_blocked, id=8496, stack(0x000000cf1d800000,0x000000cf1d900000) (1024K)]
  0x000001ed7dba6790 JavaThread "Execution worker Thread 2"         [_thread_blocked, id=24292, stack(0x000000cf1d900000,0x000000cf1da00000) (1024K)]
  0x000001ed7dba6e20 JavaThread "Execution worker Thread 3"         [_thread_blocked, id=16172, stack(0x000000cf1da00000,0x000000cf1db00000) (1024K)]
  0x000001ed7dba46c0 JavaThread "Execution worker Thread 4"         [_thread_blocked, id=8988, stack(0x000000cf1db00000,0x000000cf1dc00000) (1024K)]
  0x000001ed1f3743a0 JavaThread "Execution worker Thread 5"         [_thread_blocked, id=11016, stack(0x000000cf1dc00000,0x000000cf1dd00000) (1024K)]
  0x000001ed1f375de0 JavaThread "Execution worker Thread 6"         [_thread_blocked, id=28140, stack(0x000000cf1dd00000,0x000000cf1de00000) (1024K)]
  0x000001ed1f376b00 JavaThread "Execution worker Thread 7"         [_thread_blocked, id=17204, stack(0x000000cf1de00000,0x000000cf1df00000) (1024K)]
  0x000001ed1f377190 JavaThread "Execution worker Thread 8"         [_thread_blocked, id=21848, stack(0x000000cf1df00000,0x000000cf1e000000) (1024K)]
  0x000001ed21dc29e0 JavaThread "Execution worker Thread 9"         [_thread_blocked, id=8760, stack(0x000000cf1e000000,0x000000cf1e100000) (1024K)]
  0x000001ed24890360 JavaThread "Execution worker Thread 10"        [_thread_blocked, id=23464, stack(0x000000cf1e100000,0x000000cf1e200000) (1024K)]
  0x000001ed2488dc00 JavaThread "Execution worker Thread 11"        [_thread_blocked, id=28236, stack(0x000000cf1e200000,0x000000cf1e300000) (1024K)]
  0x000001ed2488fcd0 JavaThread "Execution worker Thread 12"        [_thread_blocked, id=19224, stack(0x000000cf1e300000,0x000000cf1e400000) (1024K)]
  0x000001ed2488e920 JavaThread "Execution worker Thread 13"        [_thread_blocked, id=23092, stack(0x000000cf1e400000,0x000000cf1e500000) (1024K)]
  0x000001ed248909f0 JavaThread "Execution worker Thread 14"        [_thread_blocked, id=18292, stack(0x000000cf1e500000,0x000000cf1e600000) (1024K)]
  0x000001ed24891080 JavaThread "Execution worker Thread 15"        [_thread_blocked, id=21204, stack(0x000000cf1e600000,0x000000cf1e700000) (1024K)]
  0x000001ed24891710 JavaThread "Cache worker for execution history cache (C:\Users\<USER>\quiz-bee-techs\node_modules\@react-native\gradle-plugin\.gradle\8.10.2\executionHistory)"        [_thread_blocked, id=24760, stack(0x000000cf1e700000,0x000000cf1e800000) (1024K)]
  0x000001ed2488e290 JavaThread "Unconstrained build operations Thread 35"        [_thread_blocked, id=20216, stack(0x000000cf1e800000,0x000000cf1e900000) (1024K)]
  0x000001ed24891da0 JavaThread "Unconstrained build operations Thread 36"        [_thread_blocked, id=2928, stack(0x000000cf1e900000,0x000000cf1ea00000) (1024K)]
  0x000001ed2488f640 JavaThread "Unconstrained build operations Thread 37"        [_thread_blocked, id=24584, stack(0x000000cf1ea00000,0x000000cf1eb00000) (1024K)]
  0x000001ed24892430 JavaThread "Unconstrained build operations Thread 38"        [_thread_blocked, id=6672, stack(0x000000cf1eb00000,0x000000cf1ec00000) (1024K)]
  0x000001ed2488efb0 JavaThread "Unconstrained build operations Thread 39"        [_thread_blocked, id=30108, stack(0x000000cf1ec00000,0x000000cf1ed00000) (1024K)]
  0x000001ed24895220 JavaThread "Unconstrained build operations Thread 40"        [_thread_blocked, id=28292, stack(0x000000cf1ed00000,0x000000cf1ee00000) (1024K)]
  0x000001ed24892ac0 JavaThread "Unconstrained build operations Thread 41"        [_thread_blocked, id=13220, stack(0x000000cf1ee00000,0x000000cf1ef00000) (1024K)]
  0x000001ed248937e0 JavaThread "Unconstrained build operations Thread 42"        [_thread_blocked, id=12224, stack(0x000000cf1ef00000,0x000000cf1f000000) (1024K)]
  0x000001ed248958b0 JavaThread "Unconstrained build operations Thread 43"        [_thread_blocked, id=19140, stack(0x000000cf1f000000,0x000000cf1f100000) (1024K)]
  0x000001ed24894500 JavaThread "Unconstrained build operations Thread 44"        [_thread_blocked, id=23672, stack(0x000000cf1f100000,0x000000cf1f200000) (1024K)]
  0x000001ed24894b90 JavaThread "Unconstrained build operations Thread 45"        [_thread_blocked, id=24132, stack(0x000000cf1f200000,0x000000cf1f300000) (1024K)]
  0x000001ed24893e70 JavaThread "Unconstrained build operations Thread 46"        [_thread_blocked, id=23832, stack(0x000000cf1f300000,0x000000cf1f400000) (1024K)]
  0x000001ed24895f40 JavaThread "Unconstrained build operations Thread 47"        [_thread_blocked, id=15892, stack(0x000000cf1aa00000,0x000000cf1ab00000) (1024K)]
  0x000001ed248965d0 JavaThread "Unconstrained build operations Thread 48"        [_thread_blocked, id=9016, stack(0x000000cf1f400000,0x000000cf1f500000) (1024K)]
  0x000001ed24893150 JavaThread "Unconstrained build operations Thread 49"        [_thread_blocked, id=11068, stack(0x000000cf1f500000,0x000000cf1f600000) (1024K)]
  0x000001ed24899a50 JavaThread "Unconstrained build operations Thread 50"        [_thread_blocked, id=25704, stack(0x000000cf1f600000,0x000000cf1f700000) (1024K)]
  0x000001ed2489a0e0 JavaThread "Unconstrained build operations Thread 51"        [_thread_blocked, id=30564, stack(0x000000cf1f700000,0x000000cf1f800000) (1024K)]
  0x000001ed24898010 JavaThread "Unconstrained build operations Thread 52"        [_thread_blocked, id=22636, stack(0x000000cf1f800000,0x000000cf1f900000) (1024K)]
  0x000001ed248986a0 JavaThread "Unconstrained build operations Thread 53"        [_thread_blocked, id=28676, stack(0x000000cf1f900000,0x000000cf1fa00000) (1024K)]
  0x000001ed24898d30 JavaThread "Unconstrained build operations Thread 54"        [_thread_blocked, id=14488, stack(0x000000cf1fa00000,0x000000cf1fb00000) (1024K)]
  0x000001ed24896c60 JavaThread "Unconstrained build operations Thread 55"        [_thread_blocked, id=13616, stack(0x000000cf1fb00000,0x000000cf1fc00000) (1024K)]
  0x000001ed248972f0 JavaThread "Unconstrained build operations Thread 56"        [_thread_blocked, id=19800, stack(0x000000cf1fc00000,0x000000cf1fd00000) (1024K)]
  0x000001ed2489a770 JavaThread "Unconstrained build operations Thread 57"        [_thread_blocked, id=23676, stack(0x000000cf1fd00000,0x000000cf1fe00000) (1024K)]
  0x000001ed2489ae00 JavaThread "Unconstrained build operations Thread 58"        [_thread_blocked, id=27936, stack(0x000000cf1fe00000,0x000000cf1ff00000) (1024K)]
  0x000001ed248993c0 JavaThread "Unconstrained build operations Thread 59"        [_thread_blocked, id=20468, stack(0x000000cf1ff00000,0x000000cf20000000) (1024K)]
  0x000001ed24897980 JavaThread "Unconstrained build operations Thread 60"        [_thread_blocked, id=22572, stack(0x000000cf20000000,0x000000cf20100000) (1024K)]
  0x000001ed2489b490 JavaThread "Unconstrained build operations Thread 61"        [_thread_blocked, id=21740, stack(0x000000cf20100000,0x000000cf20200000) (1024K)]
  0x000001ed2489c1b0 JavaThread "Unconstrained build operations Thread 62"        [_thread_blocked, id=20404, stack(0x000000cf20200000,0x000000cf20300000) (1024K)]
  0x000001ed2489bb20 JavaThread "Unconstrained build operations Thread 63"        [_thread_blocked, id=27292, stack(0x000000cf20300000,0x000000cf20400000) (1024K)]
  0x000001ed2489c840 JavaThread "Unconstrained build operations Thread 64"        [_thread_blocked, id=25052, stack(0x000000cf20400000,0x000000cf20500000) (1024K)]
  0x000001ed2489ced0 JavaThread "Unconstrained build operations Thread 65"        [_thread_blocked, id=29372, stack(0x000000cf20500000,0x000000cf20600000) (1024K)]
  0x000001ed2070a740 JavaThread "Unconstrained build operations Thread 66"        [_thread_blocked, id=20260, stack(0x000000cf20600000,0x000000cf20700000) (1024K)]
  0x000001ed2070c180 JavaThread "Unconstrained build operations Thread 67"        [_thread_blocked, id=27284, stack(0x000000cf20700000,0x000000cf20800000) (1024K)]
  0x000001ed2070add0 JavaThread "Unconstrained build operations Thread 68"        [_thread_blocked, id=19424, stack(0x000000cf20800000,0x000000cf20900000) (1024K)]
  0x000001ed2070c810 JavaThread "Unconstrained build operations Thread 69"        [_thread_blocked, id=20000, stack(0x000000cf20900000,0x000000cf20a00000) (1024K)]
  0x000001ed20709a20 JavaThread "Cache worker for Build Output Cleanup Cache (C:\Users\<USER>\quiz-bee-techs\node_modules\expo-dev-launcher\expo-dev-launcher-gradle-plugin\.gradle\buildOutputCleanup)"        [_thread_blocked, id=29652, stack(0x000000cf20c00000,0x000000cf20d00000) (1024K)]
  0x000001ed2070d530 JavaThread "Unconstrained build operations Thread 70"        [_thread_blocked, id=23544, stack(0x000000cf21000000,0x000000cf21100000) (1024K)]
  0x000001ed2070dbc0 JavaThread "Unconstrained build operations Thread 71"        [_thread_blocked, id=11292, stack(0x000000cf21100000,0x000000cf21200000) (1024K)]
  0x000001ed2070e250 JavaThread "Unconstrained build operations Thread 72"        [_thread_blocked, id=30152, stack(0x000000cf21200000,0x000000cf21300000) (1024K)]
  0x000001ed2070e8e0 JavaThread "Unconstrained build operations Thread 73"        [_thread_blocked, id=13688, stack(0x000000cf21300000,0x000000cf21400000) (1024K)]
  0x000001ed2070ef70 JavaThread "Unconstrained build operations Thread 74"        [_thread_blocked, id=24488, stack(0x000000cf21400000,0x000000cf21500000) (1024K)]
  0x000001ed2070baf0 JavaThread "Unconstrained build operations Thread 75"        [_thread_blocked, id=21328, stack(0x000000cf21500000,0x000000cf21600000) (1024K)]
  0x000001ed207109b0 JavaThread "Unconstrained build operations Thread 76"        [_thread_blocked, id=30428, stack(0x000000cf21700000,0x000000cf21800000) (1024K)]
  0x000001ed20710320 JavaThread "Unconstrained build operations Thread 77"        [_thread_blocked, id=26900, stack(0x000000cf21800000,0x000000cf21900000) (1024K)]
  0x000001ed20711040 JavaThread "Unconstrained build operations Thread 78"        [_thread_blocked, id=9984, stack(0x000000cf21900000,0x000000cf21a00000) (1024K)]
  0x000001ed2070f600 JavaThread "Unconstrained build operations Thread 79"        [_thread_blocked, id=19696, stack(0x000000cf21b00000,0x000000cf21c00000) (1024K)]
  0x000001ed2070fc90 JavaThread "Unconstrained build operations Thread 80"        [_thread_blocked, id=24456, stack(0x000000cf21c00000,0x000000cf21d00000) (1024K)]
  0x000001ed21dc0910 JavaThread "Unconstrained build operations Thread 81"        [_thread_blocked, id=29528, stack(0x000000cf21d00000,0x000000cf21e00000) (1024K)]
  0x000001ed21dc3070 JavaThread "Unconstrained build operations Thread 82"        [_thread_blocked, id=28520, stack(0x000000cf21e00000,0x000000cf21f00000) (1024K)]
  0x000001ed21dc0280 JavaThread "Unconstrained build operations Thread 83"        [_thread_blocked, id=25516, stack(0x000000cf21f00000,0x000000cf22000000) (1024K)]
  0x000001ed21dc3d90 JavaThread "Unconstrained build operations Thread 84"        [_thread_blocked, id=23492, stack(0x000000cf22000000,0x000000cf22100000) (1024K)]
  0x000001ed21dc4420 JavaThread "Unconstrained build operations Thread 85"        [_thread_blocked, id=4868, stack(0x000000cf22100000,0x000000cf22200000) (1024K)]
  0x000001ed21dc0fa0 JavaThread "Unconstrained build operations Thread 86"        [_thread_blocked, id=26284, stack(0x000000cf22200000,0x000000cf22300000) (1024K)]
  0x000001ed21dc4ab0 JavaThread "Unconstrained build operations Thread 87"        [_thread_blocked, id=26892, stack(0x000000cf22300000,0x000000cf22400000) (1024K)]
  0x000001ed21dc1630 JavaThread "Unconstrained build operations Thread 88"        [_thread_blocked, id=9296, stack(0x000000cf22400000,0x000000cf22500000) (1024K)]
  0x000001ed21dc2350 JavaThread "Unconstrained build operations Thread 89"        [_thread_blocked, id=22840, stack(0x000000cf22500000,0x000000cf22600000) (1024K)]
  0x000001ed21dc5140 JavaThread "Unconstrained build operations Thread 90"        [_thread_blocked, id=11176, stack(0x000000cf22600000,0x000000cf22700000) (1024K)]
  0x000001ed21dc1cc0 JavaThread "Unconstrained build operations Thread 91"        [_thread_blocked, id=30436, stack(0x000000cf22700000,0x000000cf22800000) (1024K)]
  0x000001ed21dc78a0 JavaThread "Unconstrained build operations Thread 92"        [_thread_blocked, id=19428, stack(0x000000cf22800000,0x000000cf22900000) (1024K)]
  0x000001ed21dc8c50 JavaThread "Unconstrained build operations Thread 93"        [_thread_blocked, id=7300, stack(0x000000cf22900000,0x000000cf22a00000) (1024K)]
  0x000001ed21dc7f30 JavaThread "Unconstrained build operations Thread 94"        [_thread_blocked, id=30572, stack(0x000000cf22a00000,0x000000cf22b00000) (1024K)]
  0x000001ed21dc57d0 JavaThread "Unconstrained build operations Thread 95"        [_thread_blocked, id=7360, stack(0x000000cf22b00000,0x000000cf22c00000) (1024K)]
  0x000001ed21dc85c0 JavaThread "Unconstrained build operations Thread 96"        [_thread_blocked, id=13264, stack(0x000000cf22d00000,0x000000cf22e00000) (1024K)]
  0x000001ed21dc64f0 JavaThread "Unconstrained build operations Thread 97"        [_thread_blocked, id=21128, stack(0x000000cf22e00000,0x000000cf22f00000) (1024K)]
  0x000001ed21dc6b80 JavaThread "Unconstrained build operations Thread 98"        [_thread_blocked, id=596, stack(0x000000cf22f00000,0x000000cf23000000) (1024K)]
  0x000001ed21dc92e0 JavaThread "Unconstrained build operations Thread 99"        [_thread_blocked, id=7112, stack(0x000000cf23000000,0x000000cf23100000) (1024K)]
  0x000001ed21dc5e60 JavaThread "build event listener"              [_thread_blocked, id=17768, stack(0x000000cf23100000,0x000000cf23200000) (1024K)]
  0x000001ed21dccdf0 JavaThread "jar transforms Thread 7"           [_thread_blocked, id=25252, stack(0x000000cf23300000,0x000000cf23400000) (1024K)]
  0x000001ed21dca000 JavaThread "Cache worker for execution history cache (C:\Users\<USER>\quiz-bee-techs\node_modules\expo-dev-launcher\expo-dev-launcher-gradle-plugin\.gradle\8.10.2\executionHistory)"        [_thread_blocked, id=28408, stack(0x000000cf23600000,0x000000cf23700000) (1024K)]
  0x000001ed21dcc0d0 JavaThread "Unconstrained build operations Thread 100"        [_thread_blocked, id=29924, stack(0x000000cf23400000,0x000000cf23500000) (1024K)]
  0x000001ed21dcad20 JavaThread "Unconstrained build operations Thread 101"        [_thread_blocked, id=26496, stack(0x000000cf23700000,0x000000cf23800000) (1024K)]
  0x000001ed21dcc760 JavaThread "Unconstrained build operations Thread 102"        [_thread_blocked, id=11120, stack(0x000000cf23800000,0x000000cf23900000) (1024K)]
  0x000001ed21dcdb10 JavaThread "Unconstrained build operations Thread 103"        [_thread_blocked, id=4652, stack(0x000000cf23900000,0x000000cf23a00000) (1024K)]
  0x000001ed21dca690 JavaThread "Unconstrained build operations Thread 104"        [_thread_blocked, id=18332, stack(0x000000cf23a00000,0x000000cf23b00000) (1024K)]
  0x000001ed21dce1a0 JavaThread "Unconstrained build operations Thread 105"        [_thread_blocked, id=23416, stack(0x000000cf23b00000,0x000000cf23c00000) (1024K)]
  0x000001ed21dce830 JavaThread "Unconstrained build operations Thread 106"        [_thread_blocked, id=29612, stack(0x000000cf23c00000,0x000000cf23d00000) (1024K)]
  0x000001ed21dceec0 JavaThread "Unconstrained build operations Thread 107"        [_thread_blocked, id=2480, stack(0x000000cf23d00000,0x000000cf23e00000) (1024K)]
  0x000001ed21dcf550 JavaThread "Unconstrained build operations Thread 108"        [_thread_blocked, id=7008, stack(0x000000cf23e00000,0x000000cf23f00000) (1024K)]
  0x000001ed2428be10 JavaThread "Unconstrained build operations Thread 109"        [_thread_blocked, id=22396, stack(0x000000cf23f00000,0x000000cf24000000) (1024K)]
  0x000001ed2428aa60 JavaThread "Unconstrained build operations Thread 110"        [_thread_blocked, id=29244, stack(0x000000cf24000000,0x000000cf24100000) (1024K)]
  0x000001ed2428b0f0 JavaThread "Unconstrained build operations Thread 111"        [_thread_blocked, id=15644, stack(0x000000cf24100000,0x000000cf24200000) (1024K)]
  0x000001ed24289020 JavaThread "Unconstrained build operations Thread 112"        [_thread_blocked, id=27408, stack(0x000000cf24200000,0x000000cf24300000) (1024K)]
  0x000001ed2428b780 JavaThread "Unconstrained build operations Thread 113"        [_thread_blocked, id=14032, stack(0x000000cf24300000,0x000000cf24400000) (1024K)]
  0x000001ed24289d40 JavaThread "Unconstrained build operations Thread 114"        [_thread_blocked, id=28752, stack(0x000000cf24400000,0x000000cf24500000) (1024K)]
  0x000001ed2428cb30 JavaThread "Unconstrained build operations Thread 115"        [_thread_blocked, id=1096, stack(0x000000cf24500000,0x000000cf24600000) (1024K)]
  0x000001ed242896b0 JavaThread "Unconstrained build operations Thread 116"        [_thread_blocked, id=29900, stack(0x000000cf24600000,0x000000cf24700000) (1024K)]
  0x000001ed2428d1c0 JavaThread "Unconstrained build operations Thread 117"        [_thread_blocked, id=20632, stack(0x000000cf24700000,0x000000cf24800000) (1024K)]
  0x000001ed2428d850 JavaThread "Unconstrained build operations Thread 118"        [_thread_blocked, id=29336, stack(0x000000cf24800000,0x000000cf24900000) (1024K)]
  0x000001ed2428a3d0 JavaThread "Unconstrained build operations Thread 119"        [_thread_blocked, id=24384, stack(0x000000cf24900000,0x000000cf24a00000) (1024K)]
  0x000001ed24291360 JavaThread "Unconstrained build operations Thread 120"        [_thread_blocked, id=29024, stack(0x000000cf24a00000,0x000000cf24b00000) (1024K)]
  0x000001ed2428f290 JavaThread "Unconstrained build operations Thread 121"        [_thread_blocked, id=30684, stack(0x000000cf24b00000,0x000000cf24c00000) (1024K)]
  0x000001ed2428dee0 JavaThread "Unconstrained build operations Thread 122"        [_thread_blocked, id=8576, stack(0x000000cf24c00000,0x000000cf24d00000) (1024K)]
  0x000001ed2428ffb0 JavaThread "Unconstrained build operations Thread 123"        [_thread_blocked, id=20496, stack(0x000000cf24d00000,0x000000cf24e00000) (1024K)]
  0x000001ed2428f920 JavaThread "Unconstrained build operations Thread 124"        [_thread_blocked, id=7944, stack(0x000000cf24e00000,0x000000cf24f00000) (1024K)]
  0x000001ed24292da0 JavaThread "WorkerExecutor Queue"              [_thread_in_native, id=14392, stack(0x000000cf24f00000,0x000000cf25000000) (1024K)]
  0x000001ed24295b90 JavaThread "RMI GC Daemon"              daemon [_thread_blocked, id=6824, stack(0x000000cf25000000,0x000000cf25100000) (1024K)]
  0x000001ed24290640 JavaThread "RMI Reaper"                        [_thread_blocked, id=2440, stack(0x000000cf25400000,0x000000cf25500000) (1024K)]
  0x000001ed24294e70 JavaThread "Unconstrained build operations Thread 125"        [_thread_blocked, id=26064, stack(0x000000cf1a400000,0x000000cf1a500000) (1024K)]
  0x000001ed242975d0 JavaThread "Unconstrained build operations Thread 126"        [_thread_blocked, id=23856, stack(0x000000cf1cf00000,0x000000cf1d000000) (1024K)]
  0x000001ed24293430 JavaThread "Unconstrained build operations Thread 127"        [_thread_blocked, id=25064, stack(0x000000cf20a00000,0x000000cf20b00000) (1024K)]
  0x000001ed2070a0b0 JavaThread "Unconstrained build operations Thread 128"        [_thread_blocked, id=30332, stack(0x000000cf20b00000,0x000000cf20c00000) (1024K)]
  0x000001ed2070b460 JavaThread "Unconstrained build operations Thread 129"        [_thread_blocked, id=27372, stack(0x000000cf20d00000,0x000000cf20e00000) (1024K)]
  0x000001ed2070cea0 JavaThread "Unconstrained build operations Thread 130"        [_thread_blocked, id=16808, stack(0x000000cf20e00000,0x000000cf20f00000) (1024K)]
  0x000001ed242968b0 JavaThread "Unconstrained build operations Thread 131"        [_thread_blocked, id=15316, stack(0x000000cf20f00000,0x000000cf21000000) (1024K)]
  0x000001ed29042110 JavaThread "Unconstrained build operations Thread 132"        [_thread_blocked, id=21712, stack(0x000000cf25600000,0x000000cf25700000) (1024K)]
  0x000001ed2903f9b0 JavaThread "Unconstrained build operations Thread 133"        [_thread_blocked, id=28016, stack(0x000000cf25700000,0x000000cf25800000) (1024K)]
  0x000001ed29040040 JavaThread "Unconstrained build operations Thread 134"        [_thread_blocked, id=7452, stack(0x000000cf25800000,0x000000cf25900000) (1024K)]
  0x000001ed29040d60 JavaThread "Unconstrained build operations Thread 135"        [_thread_blocked, id=28276, stack(0x000000cf25900000,0x000000cf25a00000) (1024K)]
  0x000001ed2903ec90 JavaThread "Unconstrained build operations Thread 136"        [_thread_blocked, id=24952, stack(0x000000cf25a00000,0x000000cf25b00000) (1024K)]
  0x000001ed290427a0 JavaThread "Unconstrained build operations Thread 137"        [_thread_blocked, id=28484, stack(0x000000cf25b00000,0x000000cf25c00000) (1024K)]
  0x000001ed29041a80 JavaThread "Unconstrained build operations Thread 138"        [_thread_blocked, id=15828, stack(0x000000cf25c00000,0x000000cf25d00000) (1024K)]
  0x000001ed290406d0 JavaThread "Unconstrained build operations Thread 139"        [_thread_blocked, id=14220, stack(0x000000cf25d00000,0x000000cf25e00000) (1024K)]
  0x000001ed290413f0 JavaThread "Unconstrained build operations Thread 140"        [_thread_blocked, id=20092, stack(0x000000cf25e00000,0x000000cf25f00000) (1024K)]
  0x000001ed2903f320 JavaThread "Unconstrained build operations Thread 141"        [_thread_blocked, id=17300, stack(0x000000cf25f00000,0x000000cf26000000) (1024K)]
  0x000001ed29044f00 JavaThread "Unconstrained build operations Thread 142"        [_thread_blocked, id=16584, stack(0x000000cf26000000,0x000000cf26100000) (1024K)]
  0x000001ed29043b50 JavaThread "Unconstrained build operations Thread 143"        [_thread_blocked, id=4424, stack(0x000000cf26100000,0x000000cf26200000) (1024K)]
  0x000001ed290441e0 JavaThread "Unconstrained build operations Thread 144"        [_thread_blocked, id=21860, stack(0x000000cf26200000,0x000000cf26300000) (1024K)]
  0x000001ed290462b0 JavaThread "Unconstrained build operations Thread 145"        [_thread_blocked, id=27864, stack(0x000000cf26300000,0x000000cf26400000) (1024K)]
  0x000001ed29044870 JavaThread "Unconstrained build operations Thread 146"        [_thread_blocked, id=1696, stack(0x000000cf26400000,0x000000cf26500000) (1024K)]
  0x000001ed29045590 JavaThread "Unconstrained build operations Thread 147"        [_thread_blocked, id=11340, stack(0x000000cf26500000,0x000000cf26600000) (1024K)]
  0x000001ed29042e30 JavaThread "Unconstrained build operations Thread 148"        [_thread_blocked, id=17688, stack(0x000000cf26900000,0x000000cf26a00000) (1024K)]
  0x000001ed29045c20 JavaThread "Unconstrained build operations Thread 149"        [_thread_blocked, id=6492, stack(0x000000cf26a00000,0x000000cf26b00000) (1024K)]
  0x000001ed29046940 JavaThread "Unconstrained build operations Thread 150"        [_thread_blocked, id=9616, stack(0x000000cf26b00000,0x000000cf26c00000) (1024K)]
  0x000001ed290434c0 JavaThread "Unconstrained build operations Thread 151"        [_thread_blocked, id=26328, stack(0x000000cf26c00000,0x000000cf26d00000) (1024K)]
  0x000001ed29047660 JavaThread "Unconstrained build operations Thread 152"        [_thread_blocked, id=6884, stack(0x000000cf26d00000,0x000000cf26e00000) (1024K)]
  0x000001ed29046fd0 JavaThread "Unconstrained build operations Thread 153"        [_thread_blocked, id=25012, stack(0x000000cf26e00000,0x000000cf26f00000) (1024K)]
  0x000001ed2904aae0 JavaThread "Unconstrained build operations Thread 154"        [_thread_blocked, id=30652, stack(0x000000cf26f00000,0x000000cf27000000) (1024K)]
  0x000001ed2904a450 JavaThread "Unconstrained build operations Thread 155"        [_thread_blocked, id=3184, stack(0x000000cf27200000,0x000000cf27300000) (1024K)]
  0x000001ed2904b170 JavaThread "Unconstrained build operations Thread 156"        [_thread_blocked, id=7964, stack(0x000000cf27300000,0x000000cf27400000) (1024K)]
  0x000001ed29048a10 JavaThread "Unconstrained build operations Thread 157"        [_thread_blocked, id=19932, stack(0x000000cf27400000,0x000000cf27500000) (1024K)]
  0x000001ed29047cf0 JavaThread "Unconstrained build operations Thread 158"        [_thread_blocked, id=6064, stack(0x000000cf27500000,0x000000cf27600000) (1024K)]
  0x000001ed290490a0 JavaThread "Unconstrained build operations Thread 159"        [_thread_blocked, id=30476, stack(0x000000cf27600000,0x000000cf27700000) (1024K)]
  0x000001ed2904b800 JavaThread "Unconstrained build operations Thread 160"        [_thread_blocked, id=27160, stack(0x000000cf27700000,0x000000cf27800000) (1024K)]
  0x000001ed2053c2d0 JavaThread "jar transforms Thread 8"           [_thread_blocked, id=3048, stack(0x000000cf23200000,0x000000cf23300000) (1024K)]
  0x000001ed244cc720 JavaThread "jar transforms Thread 9"           [_thread_blocked, id=11996, stack(0x000000cf26600000,0x000000cf26700000) (1024K)]
  0x000001ed244cd440 JavaThread "jar transforms Thread 10"          [_thread_blocked, id=29384, stack(0x000000cf26700000,0x000000cf26800000) (1024K)]
  0x000001ed2a3817e0 JavaThread "build event listener"              [_thread_blocked, id=29492, stack(0x000000cf27800000,0x000000cf27900000) (1024K)]
  0x000001ed2a368e20 JavaThread "jar transforms Thread 11"          [_thread_blocked, id=27020, stack(0x000000cf26800000,0x000000cf26900000) (1024K)]
  0x000001ed2a3666c0 JavaThread "jar transforms Thread 12"          [_thread_blocked, id=26140, stack(0x000000cf27900000,0x000000cf27a00000) (1024K)]
  0x000001ed2a36aef0 JavaThread "jar transforms Thread 13"          [_thread_blocked, id=22768, stack(0x000000cf27b00000,0x000000cf27c00000) (1024K)]
  0x000001ed2a36b580 JavaThread "jar transforms Thread 14"          [_thread_blocked, id=8736, stack(0x000000cf27c00000,0x000000cf27d00000) (1024K)]
  0x000001ed2a36c2a0 JavaThread "Exec process Thread 7"             [_thread_in_native, id=18644, stack(0x000000cf27d00000,0x000000cf27e00000) (1024K)]
  0x000001ed2a36c930 JavaThread "Exec process Thread 8"             [_thread_in_native, id=11840, stack(0x000000cf1d100000,0x000000cf1d200000) (1024K)]
  0x000001ed2a36a1d0 JavaThread "Exec process Thread 9"             [_thread_in_native, id=28608, stack(0x000000cf27e00000,0x000000cf27f00000) (1024K)]
  0x000001ed2a3694b0 JavaThread "jar transforms Thread 15"          [_thread_blocked, id=19312, stack(0x000000cf27f00000,0x000000cf28000000) (1024K)]
  0x000001ed2a36e370 JavaThread "jar transforms Thread 16"          [_thread_blocked, id=18304, stack(0x000000cf27a00000,0x000000cf27b00000) (1024K)]
  0x000001ed24774a80 JavaThread "ForkJoinPool.commonPool-worker-21" daemon [_thread_blocked, id=10548, stack(0x000000cf28300000,0x000000cf28400000) (1024K)]
  0x000001ed2b3bca20 JavaThread "ForkJoinPool.commonPool-worker-20" daemon [_thread_blocked, id=14504, stack(0x000000cf28400000,0x000000cf28500000) (1024K)]
  0x000001ed2db33130 JavaThread "included builds Thread 3"          [_thread_blocked, id=27496, stack(0x000000cf25500000,0x000000cf25600000) (1024K)]
  0x000001ed2b3b8a40 JavaThread "Cache worker for execution history cache (C:\Users\<USER>\quiz-bee-techs\android\.gradle\8.10.2\executionHistory)"        [_thread_blocked, id=12272, stack(0x000000cf28000000,0x000000cf28100000) (1024K)]
  0x000001ed2ae4c7a0 JavaThread "WorkerExecutor Queue Thread 2"        [_thread_in_vm, id=15588, stack(0x000000cf23500000,0x000000cf23600000) (1024K)]
  0x000001ed2fac3770 JavaThread "WorkerExecutor Queue Thread 3"        [_thread_in_Java, id=22400, stack(0x000000cf28200000,0x000000cf28300000) (1024K)]
  0x000001ed7ea350f0 JavaThread "WorkerExecutor Queue Thread 4"        [_thread_blocked, id=7256, stack(0x000000cf28500000,0x000000cf28600000) (1024K)]
  0x000001ed2ba945d0 JavaThread "C1 CompilerThread1"         daemon [_thread_blocked, id=11476, stack(0x000000cf1d700000,0x000000cf1d800000) (1024K)]
  0x000001ed23e2dac0 JavaThread "C1 CompilerThread2"         daemon [_thread_blocked, id=18344, stack(0x000000cf28100000,0x000000cf28200000) (1024K)]
  0x000001ed30f94910 JavaThread "C2 CompilerThread1"         daemon [_thread_in_native, id=16248, stack(0x000000cf1d600000,0x000000cf1d700000) (1024K)]
=>0x000001ed2b0eb2f0 JavaThread "C2 CompilerThread2"         daemon [_thread_in_native, id=26992, stack(0x000000cf28600000,0x000000cf28700000) (1024K)]
  0x000001ed2db14fc0 JavaThread "C2 CompilerThread3"         daemon [_thread_in_native, id=24336, stack(0x000000cf28700000,0x000000cf28800000) (1024K)]
  0x000001ed2ced2e40 JavaThread "C1 CompilerThread3"         daemon [_thread_blocked, id=23208, stack(0x000000cf28800000,0x000000cf28900000) (1024K)]
Total: 257

Other Threads:
  0x000001ed7da65850 VMThread "VM Thread"                           [id=3396, stack(0x000000cf17a00000,0x000000cf17b00000) (1024K)]
  0x000001ed7da4b390 WatcherThread "VM Periodic Task Thread"        [id=2292, stack(0x000000cf17900000,0x000000cf17a00000) (1024K)]
  0x000001ed603c3ea0 WorkerThread "GC Thread#0"                     [id=30716, stack(0x000000cf17400000,0x000000cf17500000) (1024K)]
  0x000001ed7e222600 WorkerThread "GC Thread#1"                     [id=28528, stack(0x000000cf18700000,0x000000cf18800000) (1024K)]
  0x000001ed7e1fc380 WorkerThread "GC Thread#2"                     [id=22776, stack(0x000000cf18800000,0x000000cf18900000) (1024K)]
  0x000001ed7e1fc720 WorkerThread "GC Thread#3"                     [id=26008, stack(0x000000cf18900000,0x000000cf18a00000) (1024K)]
  0x000001ed7ed86ea0 WorkerThread "GC Thread#4"                     [id=30288, stack(0x000000cf18c00000,0x000000cf18d00000) (1024K)]
  0x000001ed1f0a34c0 WorkerThread "GC Thread#5"                     [id=27968, stack(0x000000cf18d00000,0x000000cf18e00000) (1024K)]
  0x000001ed1fa1e960 WorkerThread "GC Thread#6"                     [id=10708, stack(0x000000cf19b00000,0x000000cf19c00000) (1024K)]
  0x000001ed1fa1ed00 WorkerThread "GC Thread#7"                     [id=4744, stack(0x000000cf19c00000,0x000000cf19d00000) (1024K)]
  0x000001ed20764850 WorkerThread "GC Thread#8"                     [id=29104, stack(0x000000cf19d00000,0x000000cf19e00000) (1024K)]
  0x000001ed20763290 WorkerThread "GC Thread#9"                     [id=27472, stack(0x000000cf19e00000,0x000000cf19f00000) (1024K)]
  0x000001ed207639d0 WorkerThread "GC Thread#10"                    [id=22372, stack(0x000000cf19f00000,0x000000cf1a000000) (1024K)]
  0x000001ed20762ef0 WorkerThread "GC Thread#11"                    [id=27944, stack(0x000000cf1a000000,0x000000cf1a100000) (1024K)]
  0x000001ed20763630 WorkerThread "GC Thread#12"                    [id=14416, stack(0x000000cf1a100000,0x000000cf1a200000) (1024K)]
  0x000001ed603d4d90 ConcurrentGCThread "G1 Main Marker"            [id=5516, stack(0x000000cf17500000,0x000000cf17600000) (1024K)]
  0x000001ed603d6620 WorkerThread "G1 Conc#0"                       [id=23148, stack(0x000000cf17600000,0x000000cf17700000) (1024K)]
  0x000001ed207644b0 WorkerThread "G1 Conc#1"                       [id=15088, stack(0x000000cf1a200000,0x000000cf1a300000) (1024K)]
  0x000001ed218ea930 WorkerThread "G1 Conc#2"                       [id=29732, stack(0x000000cf1a300000,0x000000cf1a400000) (1024K)]
  0x000001ed7d918670 ConcurrentGCThread "G1 Refine#0"               [id=4948, stack(0x000000cf17700000,0x000000cf17800000) (1024K)]
  0x000001ed7d918e90 ConcurrentGCThread "G1 Service"                [id=11024, stack(0x000000cf17800000,0x000000cf17900000) (1024K)]
Total: 21

Threads with active compile tasks:
C2 CompilerThread0  10338167 38571   !   4       org.jetbrains.kotlin.metadata.ProtoBuf$TypeParameter::<init> (662 bytes)
C2 CompilerThread1  10338167 38555       4       org.jetbrains.org.objectweb.asm.ClassReader::readMethod (1095 bytes)
C2 CompilerThread2  10338167 38372       4       org.jetbrains.kotlin.metadata.ProtoBuf$Function::writeTo (448 bytes)
C2 CompilerThread3  10338167 38569       4       org.jetbrains.kotlin.metadata.ProtoBuf$TypeParameter$1::parsePartialFrom (7 bytes)
C1 CompilerThread3  10338167 38710       3       org.jetbrains.kotlin.types.checker.SimpleClassicTypeSystemContext::asSimpleType (6 bytes)
Total: 5

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x000001ed00000000-0x000001ed00c80000-0x000001ed00c80000), size 13107200, SharedBaseAddress: 0x000001ed00000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000001ed01000000-0x000001ed1b000000, reserved size: 436207616
Narrow klass base: 0x000001ed00000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 5 size 8 Array Of Cards #cards 12 size 40 Howl #buckets 4 coarsen threshold 1843 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 2048
 CPUs: 16 total, 16 available
 Memory: 7599M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Heap Region Size: 1M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 120M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 13
 Concurrent Workers: 3
 Concurrent Refinement Workers: 13
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 1063936K, used 697629K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 80 young (81920K), 20 survivors (20480K)
 Metaspace       used 208794K, committed 213760K, reserved 622592K
  class space    used 27130K, committed 29312K, reserved 425984K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000080000000, 0x0000000080100000, 0x0000000080100000|100%| O|  |TAMS 0x0000000080100000| PB 0x0000000080000000| Updating 
|   1|0x0000000080100000, 0x0000000080200000, 0x0000000080200000|100%| O|  |TAMS 0x0000000080200000| PB 0x0000000080100000| Updating 
|   2|0x0000000080200000, 0x0000000080300000, 0x0000000080300000|100%|HS|  |TAMS 0x0000000080300000| PB 0x0000000080200000| Complete 
|   3|0x0000000080300000, 0x0000000080400000, 0x0000000080400000|100%|HC|  |TAMS 0x0000000080400000| PB 0x0000000080300000| Complete 
|   4|0x0000000080400000, 0x0000000080500000, 0x0000000080500000|100%|HC|  |TAMS 0x0000000080500000| PB 0x0000000080400000| Complete 
|   5|0x0000000080500000, 0x0000000080600000, 0x0000000080600000|100%|HC|  |TAMS 0x0000000080600000| PB 0x0000000080500000| Complete 
|   6|0x0000000080600000, 0x0000000080700000, 0x0000000080700000|100%| O|  |TAMS 0x0000000080700000| PB 0x0000000080600000| Untracked 
|   7|0x0000000080700000, 0x0000000080800000, 0x0000000080800000|100%| O|  |TAMS 0x0000000080800000| PB 0x0000000080700000| Untracked 
|   8|0x0000000080800000, 0x0000000080900000, 0x0000000080900000|100%| O|  |TAMS 0x0000000080900000| PB 0x0000000080800000| Updating 
|   9|0x0000000080900000, 0x0000000080a00000, 0x0000000080a00000|100%|HS|  |TAMS 0x0000000080a00000| PB 0x0000000080900000| Complete 
|  10|0x0000000080a00000, 0x0000000080b00000, 0x0000000080b00000|100%| O|  |TAMS 0x0000000080b00000| PB 0x0000000080a00000| Updating 
|  11|0x0000000080b00000, 0x0000000080c00000, 0x0000000080c00000|100%| O|  |TAMS 0x0000000080c00000| PB 0x0000000080b00000| Untracked 
|  12|0x0000000080c00000, 0x0000000080d00000, 0x0000000080d00000|100%| O|  |TAMS 0x0000000080d00000| PB 0x0000000080c00000| Untracked 
|  13|0x0000000080d00000, 0x0000000080e00000, 0x0000000080e00000|100%| O|  |TAMS 0x0000000080e00000| PB 0x0000000080d00000| Updating 
|  14|0x0000000080e00000, 0x0000000080f00000, 0x0000000080f00000|100%| O|  |TAMS 0x0000000080f00000| PB 0x0000000080e00000| Updating 
|  15|0x0000000080f00000, 0x0000000081000000, 0x0000000081000000|100%| O|  |TAMS 0x0000000081000000| PB 0x0000000080f00000| Untracked 
|  16|0x0000000081000000, 0x0000000081100000, 0x0000000081100000|100%| O|  |TAMS 0x0000000081100000| PB 0x0000000081000000| Untracked 
|  17|0x0000000081100000, 0x0000000081200000, 0x0000000081200000|100%| O|  |TAMS 0x0000000081200000| PB 0x0000000081100000| Untracked 
|  18|0x0000000081200000, 0x0000000081300000, 0x0000000081300000|100%| O|  |TAMS 0x0000000081300000| PB 0x0000000081200000| Untracked 
|  19|0x0000000081300000, 0x0000000081400000, 0x0000000081400000|100%| O|  |TAMS 0x0000000081400000| PB 0x0000000081300000| Updating 
|  20|0x0000000081400000, 0x0000000081500000, 0x0000000081500000|100%| O|  |TAMS 0x0000000081500000| PB 0x0000000081400000| Updating 
|  21|0x0000000081500000, 0x0000000081600000, 0x0000000081600000|100%| O|  |TAMS 0x0000000081600000| PB 0x0000000081500000| Updating 
|  22|0x0000000081600000, 0x0000000081700000, 0x0000000081700000|100%| O|  |TAMS 0x0000000081700000| PB 0x0000000081600000| Updating 
|  23|0x0000000081700000, 0x0000000081800000, 0x0000000081800000|100%| O|  |TAMS 0x0000000081800000| PB 0x0000000081700000| Updating 
|  24|0x0000000081800000, 0x0000000081900000, 0x0000000081900000|100%| O|  |TAMS 0x0000000081900000| PB 0x0000000081800000| Updating 
|  25|0x0000000081900000, 0x0000000081a00000, 0x0000000081a00000|100%| O|  |TAMS 0x0000000081a00000| PB 0x0000000081900000| Untracked 
|  26|0x0000000081a00000, 0x0000000081b00000, 0x0000000081b00000|100%| O|  |TAMS 0x0000000081b00000| PB 0x0000000081a00000| Untracked 
|  27|0x0000000081b00000, 0x0000000081c00000, 0x0000000081c00000|100%| O|  |TAMS 0x0000000081c00000| PB 0x0000000081b00000| Untracked 
|  28|0x0000000081c00000, 0x0000000081d00000, 0x0000000081d00000|100%| O|  |TAMS 0x0000000081d00000| PB 0x0000000081c00000| Updating 
|  29|0x0000000081d00000, 0x0000000081e00000, 0x0000000081e00000|100%| O|  |TAMS 0x0000000081e00000| PB 0x0000000081d00000| Updating 
|  30|0x0000000081e00000, 0x0000000081f00000, 0x0000000081f00000|100%| O|  |TAMS 0x0000000081f00000| PB 0x0000000081e00000| Untracked 
|  31|0x0000000081f00000, 0x0000000082000000, 0x0000000082000000|100%|HS|  |TAMS 0x0000000082000000| PB 0x0000000081f00000| Complete 
|  32|0x0000000082000000, 0x0000000082100000, 0x0000000082100000|100%| O|  |TAMS 0x0000000082100000| PB 0x0000000082000000| Updating 
|  33|0x0000000082100000, 0x0000000082200000, 0x0000000082200000|100%| O|  |TAMS 0x0000000082200000| PB 0x0000000082100000| Untracked 
|  34|0x0000000082200000, 0x0000000082300000, 0x0000000082300000|100%| O|  |TAMS 0x0000000082300000| PB 0x0000000082200000| Updating 
|  35|0x0000000082300000, 0x0000000082400000, 0x0000000082400000|100%| O|  |TAMS 0x0000000082400000| PB 0x0000000082300000| Untracked 
|  36|0x0000000082400000, 0x0000000082500000, 0x0000000082500000|100%| O|  |TAMS 0x0000000082500000| PB 0x0000000082400000| Updating 
|  37|0x0000000082500000, 0x0000000082600000, 0x0000000082600000|100%| O|  |TAMS 0x0000000082600000| PB 0x0000000082500000| Updating 
|  38|0x0000000082600000, 0x0000000082700000, 0x0000000082700000|100%| O|  |TAMS 0x0000000082700000| PB 0x0000000082600000| Updating 
|  39|0x0000000082700000, 0x0000000082800000, 0x0000000082800000|100%| O|  |TAMS 0x0000000082800000| PB 0x0000000082700000| Untracked 
|  40|0x0000000082800000, 0x0000000082900000, 0x0000000082900000|100%| O|  |TAMS 0x0000000082900000| PB 0x0000000082800000| Updating 
|  41|0x0000000082900000, 0x0000000082a00000, 0x0000000082a00000|100%| O|  |TAMS 0x0000000082a00000| PB 0x0000000082900000| Updating 
|  42|0x0000000082a00000, 0x0000000082b00000, 0x0000000082b00000|100%|HS|  |TAMS 0x0000000082b00000| PB 0x0000000082a00000| Complete 
|  43|0x0000000082b00000, 0x0000000082c00000, 0x0000000082c00000|100%|HC|  |TAMS 0x0000000082c00000| PB 0x0000000082b00000| Complete 
|  44|0x0000000082c00000, 0x0000000082d00000, 0x0000000082d00000|100%|HS|  |TAMS 0x0000000082d00000| PB 0x0000000082c00000| Complete 
|  45|0x0000000082d00000, 0x0000000082e00000, 0x0000000082e00000|100%|HC|  |TAMS 0x0000000082e00000| PB 0x0000000082d00000| Complete 
|  46|0x0000000082e00000, 0x0000000082f00000, 0x0000000082f00000|100%| O|  |TAMS 0x0000000082f00000| PB 0x0000000082e00000| Untracked 
|  47|0x0000000082f00000, 0x0000000083000000, 0x0000000083000000|100%| O|  |TAMS 0x0000000083000000| PB 0x0000000082f00000| Updating 
|  48|0x0000000083000000, 0x0000000083100000, 0x0000000083100000|100%| O|  |TAMS 0x0000000083100000| PB 0x0000000083000000| Untracked 
|  49|0x0000000083100000, 0x0000000083200000, 0x0000000083200000|100%| O|  |TAMS 0x0000000083200000| PB 0x0000000083100000| Updating 
|  50|0x0000000083200000, 0x0000000083300000, 0x0000000083300000|100%| O|  |TAMS 0x0000000083300000| PB 0x0000000083200000| Untracked 
|  51|0x0000000083300000, 0x0000000083400000, 0x0000000083400000|100%| O|  |TAMS 0x0000000083400000| PB 0x0000000083300000| Updating 
|  52|0x0000000083400000, 0x0000000083500000, 0x0000000083500000|100%|HS|  |TAMS 0x0000000083500000| PB 0x0000000083400000| Complete 
|  53|0x0000000083500000, 0x0000000083600000, 0x0000000083600000|100%|HC|  |TAMS 0x0000000083600000| PB 0x0000000083500000| Complete 
|  54|0x0000000083600000, 0x0000000083700000, 0x0000000083700000|100%|HC|  |TAMS 0x0000000083700000| PB 0x0000000083600000| Complete 
|  55|0x0000000083700000, 0x0000000083800000, 0x0000000083800000|100%|HC|  |TAMS 0x0000000083800000| PB 0x0000000083700000| Complete 
|  56|0x0000000083800000, 0x0000000083900000, 0x0000000083900000|100%|HS|  |TAMS 0x0000000083900000| PB 0x0000000083800000| Complete 
|  57|0x0000000083900000, 0x0000000083a00000, 0x0000000083a00000|100%|HC|  |TAMS 0x0000000083a00000| PB 0x0000000083900000| Complete 
|  58|0x0000000083a00000, 0x0000000083b00000, 0x0000000083b00000|100%|HC|  |TAMS 0x0000000083b00000| PB 0x0000000083a00000| Complete 
|  59|0x0000000083b00000, 0x0000000083c00000, 0x0000000083c00000|100%|HC|  |TAMS 0x0000000083c00000| PB 0x0000000083b00000| Complete 
|  60|0x0000000083c00000, 0x0000000083d00000, 0x0000000083d00000|100%| O|  |TAMS 0x0000000083d00000| PB 0x0000000083c00000| Untracked 
|  61|0x0000000083d00000, 0x0000000083e00000, 0x0000000083e00000|100%| O|  |TAMS 0x0000000083e00000| PB 0x0000000083d00000| Updating 
|  62|0x0000000083e00000, 0x0000000083f00000, 0x0000000083f00000|100%| O|  |TAMS 0x0000000083f00000| PB 0x0000000083e00000| Untracked 
|  63|0x0000000083f00000, 0x0000000084000000, 0x0000000084000000|100%| O|  |TAMS 0x0000000084000000| PB 0x0000000083f00000| Untracked 
|  64|0x0000000084000000, 0x0000000084100000, 0x0000000084100000|100%| O|  |TAMS 0x0000000084100000| PB 0x0000000084000000| Untracked 
|  65|0x0000000084100000, 0x0000000084200000, 0x0000000084200000|100%| O|  |TAMS 0x0000000084200000| PB 0x0000000084100000| Untracked 
|  66|0x0000000084200000, 0x0000000084300000, 0x0000000084300000|100%| O|  |TAMS 0x0000000084300000| PB 0x0000000084200000| Updating 
|  67|0x0000000084300000, 0x0000000084400000, 0x0000000084400000|100%| O|  |TAMS 0x0000000084400000| PB 0x0000000084300000| Untracked 
|  68|0x0000000084400000, 0x0000000084500000, 0x0000000084500000|100%| O|  |TAMS 0x0000000084500000| PB 0x0000000084400000| Untracked 
|  69|0x0000000084500000, 0x0000000084600000, 0x0000000084600000|100%| O|  |TAMS 0x0000000084600000| PB 0x0000000084500000| Untracked 
|  70|0x0000000084600000, 0x0000000084700000, 0x0000000084700000|100%| O|  |TAMS 0x0000000084700000| PB 0x0000000084600000| Updating 
|  71|0x0000000084700000, 0x0000000084800000, 0x0000000084800000|100%| O|  |TAMS 0x0000000084800000| PB 0x0000000084700000| Updating 
|  72|0x0000000084800000, 0x0000000084900000, 0x0000000084900000|100%| O|  |TAMS 0x0000000084900000| PB 0x0000000084800000| Untracked 
|  73|0x0000000084900000, 0x0000000084a00000, 0x0000000084a00000|100%| O|  |TAMS 0x0000000084a00000| PB 0x0000000084900000| Updating 
|  74|0x0000000084a00000, 0x0000000084b00000, 0x0000000084b00000|100%| O|  |TAMS 0x0000000084b00000| PB 0x0000000084a00000| Untracked 
|  75|0x0000000084b00000, 0x0000000084c00000, 0x0000000084c00000|100%| O|  |TAMS 0x0000000084c00000| PB 0x0000000084b00000| Untracked 
|  76|0x0000000084c00000, 0x0000000084d00000, 0x0000000084d00000|100%| O|  |TAMS 0x0000000084d00000| PB 0x0000000084c00000| Updating 
|  77|0x0000000084d00000, 0x0000000084e00000, 0x0000000084e00000|100%| O|  |TAMS 0x0000000084e00000| PB 0x0000000084d00000| Updating 
|  78|0x0000000084e00000, 0x0000000084f00000, 0x0000000084f00000|100%| O|  |TAMS 0x0000000084f00000| PB 0x0000000084e00000| Untracked 
|  79|0x0000000084f00000, 0x0000000085000000, 0x0000000085000000|100%| O|  |TAMS 0x0000000085000000| PB 0x0000000084f00000| Updating 
|  80|0x0000000085000000, 0x0000000085100000, 0x0000000085100000|100%| O|  |TAMS 0x0000000085100000| PB 0x0000000085000000| Untracked 
|  81|0x0000000085100000, 0x0000000085200000, 0x0000000085200000|100%|HS|  |TAMS 0x0000000085200000| PB 0x0000000085100000| Complete 
|  82|0x0000000085200000, 0x0000000085300000, 0x0000000085300000|100%| O|  |TAMS 0x0000000085300000| PB 0x0000000085200000| Updating 
|  83|0x0000000085300000, 0x0000000085400000, 0x0000000085400000|100%| O|  |TAMS 0x0000000085400000| PB 0x0000000085300000| Updating 
|  84|0x0000000085400000, 0x0000000085500000, 0x0000000085500000|100%| O|  |TAMS 0x0000000085500000| PB 0x0000000085400000| Updating 
|  85|0x0000000085500000, 0x0000000085600000, 0x0000000085600000|100%| O|  |TAMS 0x0000000085600000| PB 0x0000000085500000| Untracked 
|  86|0x0000000085600000, 0x0000000085700000, 0x0000000085700000|100%| O|  |TAMS 0x0000000085700000| PB 0x0000000085600000| Untracked 
|  87|0x0000000085700000, 0x0000000085800000, 0x0000000085800000|100%| O|  |TAMS 0x0000000085800000| PB 0x0000000085700000| Untracked 
|  88|0x0000000085800000, 0x0000000085900000, 0x0000000085900000|100%| O|  |TAMS 0x0000000085900000| PB 0x0000000085800000| Updating 
|  89|0x0000000085900000, 0x0000000085a00000, 0x0000000085a00000|100%| O|  |TAMS 0x0000000085a00000| PB 0x0000000085900000| Untracked 
|  90|0x0000000085a00000, 0x0000000085b00000, 0x0000000085b00000|100%| O|  |TAMS 0x0000000085b00000| PB 0x0000000085a00000| Updating 
|  91|0x0000000085b00000, 0x0000000085c00000, 0x0000000085c00000|100%| O|  |TAMS 0x0000000085c00000| PB 0x0000000085b00000| Untracked 
|  92|0x0000000085c00000, 0x0000000085d00000, 0x0000000085d00000|100%|HS|  |TAMS 0x0000000085d00000| PB 0x0000000085c00000| Complete 
|  93|0x0000000085d00000, 0x0000000085e00000, 0x0000000085e00000|100%|HS|  |TAMS 0x0000000085e00000| PB 0x0000000085d00000| Complete 
|  94|0x0000000085e00000, 0x0000000085f00000, 0x0000000085f00000|100%| O|  |TAMS 0x0000000085f00000| PB 0x0000000085e00000| Untracked 
|  95|0x0000000085f00000, 0x0000000086000000, 0x0000000086000000|100%|HS|  |TAMS 0x0000000086000000| PB 0x0000000085f00000| Complete 
|  96|0x0000000086000000, 0x0000000086100000, 0x0000000086100000|100%|HS|  |TAMS 0x0000000086100000| PB 0x0000000086000000| Complete 
|  97|0x0000000086100000, 0x0000000086200000, 0x0000000086200000|100%| O|  |TAMS 0x0000000086200000| PB 0x0000000086100000| Untracked 
|  98|0x0000000086200000, 0x0000000086300000, 0x0000000086300000|100%| O|  |TAMS 0x0000000086300000| PB 0x0000000086200000| Untracked 
|  99|0x0000000086300000, 0x0000000086400000, 0x0000000086400000|100%| O|  |TAMS 0x0000000086400000| PB 0x0000000086300000| Untracked 
| 100|0x0000000086400000, 0x0000000086500000, 0x0000000086500000|100%| O|  |TAMS 0x0000000086500000| PB 0x0000000086400000| Untracked 
| 101|0x0000000086500000, 0x0000000086600000, 0x0000000086600000|100%| O|  |TAMS 0x0000000086600000| PB 0x0000000086500000| Untracked 
| 102|0x0000000086600000, 0x0000000086700000, 0x0000000086700000|100%| O|  |TAMS 0x0000000086700000| PB 0x0000000086600000| Untracked 
| 103|0x0000000086700000, 0x0000000086800000, 0x0000000086800000|100%| O|  |TAMS 0x0000000086800000| PB 0x0000000086700000| Updating 
| 104|0x0000000086800000, 0x0000000086900000, 0x0000000086900000|100%| O|  |TAMS 0x0000000086900000| PB 0x0000000086800000| Updating 
| 105|0x0000000086900000, 0x0000000086a00000, 0x0000000086a00000|100%| O|  |TAMS 0x0000000086a00000| PB 0x0000000086900000| Untracked 
| 106|0x0000000086a00000, 0x0000000086b00000, 0x0000000086b00000|100%| O|  |TAMS 0x0000000086b00000| PB 0x0000000086a00000| Untracked 
| 107|0x0000000086b00000, 0x0000000086c00000, 0x0000000086c00000|100%| O|  |TAMS 0x0000000086c00000| PB 0x0000000086b00000| Untracked 
| 108|0x0000000086c00000, 0x0000000086d00000, 0x0000000086d00000|100%| O|  |TAMS 0x0000000086d00000| PB 0x0000000086c00000| Untracked 
| 109|0x0000000086d00000, 0x0000000086e00000, 0x0000000086e00000|100%| O|  |TAMS 0x0000000086e00000| PB 0x0000000086d00000| Untracked 
| 110|0x0000000086e00000, 0x0000000086f00000, 0x0000000086f00000|100%| O|  |TAMS 0x0000000086f00000| PB 0x0000000086e00000| Untracked 
| 111|0x0000000086f00000, 0x0000000087000000, 0x0000000087000000|100%| O|  |TAMS 0x0000000087000000| PB 0x0000000086f00000| Untracked 
| 112|0x0000000087000000, 0x0000000087100000, 0x0000000087100000|100%| O|  |TAMS 0x0000000087100000| PB 0x0000000087000000| Untracked 
| 113|0x0000000087100000, 0x0000000087200000, 0x0000000087200000|100%| O|  |TAMS 0x0000000087200000| PB 0x0000000087100000| Untracked 
| 114|0x0000000087200000, 0x0000000087300000, 0x0000000087300000|100%| O|  |TAMS 0x0000000087300000| PB 0x0000000087200000| Untracked 
| 115|0x0000000087300000, 0x0000000087400000, 0x0000000087400000|100%| O|  |TAMS 0x0000000087400000| PB 0x0000000087300000| Untracked 
| 116|0x0000000087400000, 0x0000000087500000, 0x0000000087500000|100%| O|  |TAMS 0x0000000087500000| PB 0x0000000087400000| Untracked 
| 117|0x0000000087500000, 0x0000000087600000, 0x0000000087600000|100%| O|  |TAMS 0x0000000087600000| PB 0x0000000087500000| Updating 
| 118|0x0000000087600000, 0x0000000087700000, 0x0000000087700000|100%|HS|  |TAMS 0x0000000087700000| PB 0x0000000087600000| Complete 
| 119|0x0000000087700000, 0x0000000087800000, 0x0000000087800000|100%| O|  |TAMS 0x0000000087800000| PB 0x0000000087700000| Untracked 
| 120|0x0000000087800000, 0x0000000087900000, 0x0000000087900000|100%| O|  |TAMS 0x0000000087900000| PB 0x0000000087800000| Untracked 
| 121|0x0000000087900000, 0x0000000087a00000, 0x0000000087a00000|100%| O|  |TAMS 0x0000000087a00000| PB 0x0000000087900000| Untracked 
| 122|0x0000000087a00000, 0x0000000087b00000, 0x0000000087b00000|100%| O|  |TAMS 0x0000000087b00000| PB 0x0000000087a00000| Untracked 
| 123|0x0000000087b00000, 0x0000000087c00000, 0x0000000087c00000|100%| O|  |TAMS 0x0000000087c00000| PB 0x0000000087b00000| Updating 
| 124|0x0000000087c00000, 0x0000000087d00000, 0x0000000087d00000|100%|HS|  |TAMS 0x0000000087d00000| PB 0x0000000087c00000| Complete 
| 125|0x0000000087d00000, 0x0000000087e00000, 0x0000000087e00000|100%| O|  |TAMS 0x0000000087e00000| PB 0x0000000087d00000| Untracked 
| 126|0x0000000087e00000, 0x0000000087f00000, 0x0000000087f00000|100%| O|  |TAMS 0x0000000087f00000| PB 0x0000000087e00000| Untracked 
| 127|0x0000000087f00000, 0x0000000088000000, 0x0000000088000000|100%| O|  |TAMS 0x0000000088000000| PB 0x0000000087f00000| Untracked 
| 128|0x0000000088000000, 0x0000000088100000, 0x0000000088100000|100%| O|  |TAMS 0x0000000088100000| PB 0x0000000088000000| Untracked 
| 129|0x0000000088100000, 0x0000000088200000, 0x0000000088200000|100%| O|  |TAMS 0x0000000088200000| PB 0x0000000088100000| Updating 
| 130|0x0000000088200000, 0x0000000088300000, 0x0000000088300000|100%|HS|  |TAMS 0x0000000088300000| PB 0x0000000088200000| Complete 
| 131|0x0000000088300000, 0x0000000088400000, 0x0000000088400000|100%|HS|  |TAMS 0x0000000088400000| PB 0x0000000088300000| Complete 
| 132|0x0000000088400000, 0x0000000088500000, 0x0000000088500000|100%|HC|  |TAMS 0x0000000088500000| PB 0x0000000088400000| Complete 
| 133|0x0000000088500000, 0x0000000088600000, 0x0000000088600000|100%| O|  |TAMS 0x0000000088600000| PB 0x0000000088500000| Untracked 
| 134|0x0000000088600000, 0x0000000088700000, 0x0000000088700000|100%| O|  |TAMS 0x0000000088700000| PB 0x0000000088600000| Untracked 
| 135|0x0000000088700000, 0x0000000088800000, 0x0000000088800000|100%| O|  |TAMS 0x0000000088800000| PB 0x0000000088700000| Updating 
| 136|0x0000000088800000, 0x0000000088900000, 0x0000000088900000|100%| O|  |TAMS 0x0000000088900000| PB 0x0000000088800000| Untracked 
| 137|0x0000000088900000, 0x0000000088a00000, 0x0000000088a00000|100%| O|  |TAMS 0x0000000088a00000| PB 0x0000000088900000| Untracked 
| 138|0x0000000088a00000, 0x0000000088b00000, 0x0000000088b00000|100%| O|  |TAMS 0x0000000088b00000| PB 0x0000000088a00000| Untracked 
| 139|0x0000000088b00000, 0x0000000088c00000, 0x0000000088c00000|100%| O|  |TAMS 0x0000000088c00000| PB 0x0000000088b00000| Untracked 
| 140|0x0000000088c00000, 0x0000000088d00000, 0x0000000088d00000|100%| O|  |TAMS 0x0000000088d00000| PB 0x0000000088c00000| Untracked 
| 141|0x0000000088d00000, 0x0000000088e00000, 0x0000000088e00000|100%| O|  |TAMS 0x0000000088e00000| PB 0x0000000088d00000| Untracked 
| 142|0x0000000088e00000, 0x0000000088f00000, 0x0000000088f00000|100%| O|  |TAMS 0x0000000088f00000| PB 0x0000000088e00000| Untracked 
| 143|0x0000000088f00000, 0x0000000089000000, 0x0000000089000000|100%| O|  |TAMS 0x0000000089000000| PB 0x0000000088f00000| Untracked 
| 144|0x0000000089000000, 0x0000000089100000, 0x0000000089100000|100%| O|  |TAMS 0x0000000089100000| PB 0x0000000089000000| Untracked 
| 145|0x0000000089100000, 0x0000000089200000, 0x0000000089200000|100%| O|  |TAMS 0x0000000089200000| PB 0x0000000089100000| Untracked 
| 146|0x0000000089200000, 0x0000000089300000, 0x0000000089300000|100%| O|  |TAMS 0x0000000089300000| PB 0x0000000089200000| Untracked 
| 147|0x0000000089300000, 0x0000000089400000, 0x0000000089400000|100%| O|  |TAMS 0x0000000089400000| PB 0x0000000089300000| Untracked 
| 148|0x0000000089400000, 0x0000000089500000, 0x0000000089500000|100%| O|  |TAMS 0x0000000089500000| PB 0x0000000089400000| Untracked 
| 149|0x0000000089500000, 0x0000000089600000, 0x0000000089600000|100%| O|  |TAMS 0x0000000089600000| PB 0x0000000089500000| Updating 
| 150|0x0000000089600000, 0x0000000089700000, 0x0000000089700000|100%| O|  |TAMS 0x0000000089700000| PB 0x0000000089600000| Untracked 
| 151|0x0000000089700000, 0x0000000089800000, 0x0000000089800000|100%| O|  |TAMS 0x0000000089800000| PB 0x0000000089700000| Untracked 
| 152|0x0000000089800000, 0x0000000089900000, 0x0000000089900000|100%| O|  |TAMS 0x0000000089900000| PB 0x0000000089800000| Untracked 
| 153|0x0000000089900000, 0x0000000089a00000, 0x0000000089a00000|100%| O|  |TAMS 0x0000000089a00000| PB 0x0000000089900000| Untracked 
| 154|0x0000000089a00000, 0x0000000089b00000, 0x0000000089b00000|100%| O|  |TAMS 0x0000000089b00000| PB 0x0000000089a00000| Untracked 
| 155|0x0000000089b00000, 0x0000000089c00000, 0x0000000089c00000|100%| O|  |TAMS 0x0000000089c00000| PB 0x0000000089b00000| Untracked 
| 156|0x0000000089c00000, 0x0000000089d00000, 0x0000000089d00000|100%| O|  |TAMS 0x0000000089d00000| PB 0x0000000089c00000| Untracked 
| 157|0x0000000089d00000, 0x0000000089e00000, 0x0000000089e00000|100%| O|  |TAMS 0x0000000089e00000| PB 0x0000000089d00000| Untracked 
| 158|0x0000000089e00000, 0x0000000089f00000, 0x0000000089f00000|100%| O|  |TAMS 0x0000000089f00000| PB 0x0000000089e00000| Untracked 
| 159|0x0000000089f00000, 0x000000008a000000, 0x000000008a000000|100%| O|  |TAMS 0x000000008a000000| PB 0x0000000089f00000| Untracked 
| 160|0x000000008a000000, 0x000000008a100000, 0x000000008a100000|100%| O|  |TAMS 0x000000008a100000| PB 0x000000008a000000| Untracked 
| 161|0x000000008a100000, 0x000000008a200000, 0x000000008a200000|100%| O|  |TAMS 0x000000008a200000| PB 0x000000008a100000| Updating 
| 162|0x000000008a200000, 0x000000008a300000, 0x000000008a300000|100%| O|  |TAMS 0x000000008a300000| PB 0x000000008a200000| Untracked 
| 163|0x000000008a300000, 0x000000008a400000, 0x000000008a400000|100%| O|  |TAMS 0x000000008a400000| PB 0x000000008a300000| Untracked 
| 164|0x000000008a400000, 0x000000008a500000, 0x000000008a500000|100%| O|  |TAMS 0x000000008a500000| PB 0x000000008a400000| Untracked 
| 165|0x000000008a500000, 0x000000008a600000, 0x000000008a600000|100%| O|  |TAMS 0x000000008a600000| PB 0x000000008a500000| Untracked 
| 166|0x000000008a600000, 0x000000008a700000, 0x000000008a700000|100%| O|  |TAMS 0x000000008a700000| PB 0x000000008a600000| Untracked 
| 167|0x000000008a700000, 0x000000008a800000, 0x000000008a800000|100%| O|  |TAMS 0x000000008a800000| PB 0x000000008a700000| Untracked 
| 168|0x000000008a800000, 0x000000008a900000, 0x000000008a900000|100%| O|  |TAMS 0x000000008a900000| PB 0x000000008a800000| Untracked 
| 169|0x000000008a900000, 0x000000008aa00000, 0x000000008aa00000|100%| O|  |TAMS 0x000000008aa00000| PB 0x000000008a900000| Untracked 
| 170|0x000000008aa00000, 0x000000008ab00000, 0x000000008ab00000|100%| O|  |TAMS 0x000000008ab00000| PB 0x000000008aa00000| Untracked 
| 171|0x000000008ab00000, 0x000000008ac00000, 0x000000008ac00000|100%| O|  |TAMS 0x000000008ac00000| PB 0x000000008ab00000| Untracked 
| 172|0x000000008ac00000, 0x000000008ad00000, 0x000000008ad00000|100%| O|  |TAMS 0x000000008ad00000| PB 0x000000008ac00000| Untracked 
| 173|0x000000008ad00000, 0x000000008ae00000, 0x000000008ae00000|100%| O|  |TAMS 0x000000008ae00000| PB 0x000000008ad00000| Untracked 
| 174|0x000000008ae00000, 0x000000008af00000, 0x000000008af00000|100%| O|  |TAMS 0x000000008af00000| PB 0x000000008ae00000| Untracked 
| 175|0x000000008af00000, 0x000000008b000000, 0x000000008b000000|100%| O|  |TAMS 0x000000008b000000| PB 0x000000008af00000| Untracked 
| 176|0x000000008b000000, 0x000000008b100000, 0x000000008b100000|100%| O|  |TAMS 0x000000008b100000| PB 0x000000008b000000| Untracked 
| 177|0x000000008b100000, 0x000000008b200000, 0x000000008b200000|100%| O|  |TAMS 0x000000008b200000| PB 0x000000008b100000| Updating 
| 178|0x000000008b200000, 0x000000008b300000, 0x000000008b300000|100%| O|  |TAMS 0x000000008b300000| PB 0x000000008b200000| Untracked 
| 179|0x000000008b300000, 0x000000008b400000, 0x000000008b400000|100%| O|  |TAMS 0x000000008b400000| PB 0x000000008b300000| Untracked 
| 180|0x000000008b400000, 0x000000008b500000, 0x000000008b500000|100%| O|  |TAMS 0x000000008b500000| PB 0x000000008b400000| Untracked 
| 181|0x000000008b500000, 0x000000008b600000, 0x000000008b600000|100%| O|  |TAMS 0x000000008b600000| PB 0x000000008b500000| Untracked 
| 182|0x000000008b600000, 0x000000008b700000, 0x000000008b700000|100%| O|  |TAMS 0x000000008b700000| PB 0x000000008b600000| Untracked 
| 183|0x000000008b700000, 0x000000008b800000, 0x000000008b800000|100%| O|  |TAMS 0x000000008b800000| PB 0x000000008b700000| Updating 
| 184|0x000000008b800000, 0x000000008b900000, 0x000000008b900000|100%| O|  |TAMS 0x000000008b900000| PB 0x000000008b800000| Untracked 
| 185|0x000000008b900000, 0x000000008ba00000, 0x000000008ba00000|100%| O|  |TAMS 0x000000008ba00000| PB 0x000000008b900000| Untracked 
| 186|0x000000008ba00000, 0x000000008bb00000, 0x000000008bb00000|100%| O|  |TAMS 0x000000008bb00000| PB 0x000000008ba00000| Untracked 
| 187|0x000000008bb00000, 0x000000008bc00000, 0x000000008bc00000|100%| O|  |TAMS 0x000000008bc00000| PB 0x000000008bb00000| Untracked 
| 188|0x000000008bc00000, 0x000000008bd00000, 0x000000008bd00000|100%| O|  |TAMS 0x000000008bd00000| PB 0x000000008bc00000| Updating 
| 189|0x000000008bd00000, 0x000000008be00000, 0x000000008be00000|100%| O|  |TAMS 0x000000008be00000| PB 0x000000008bd00000| Untracked 
| 190|0x000000008be00000, 0x000000008bf00000, 0x000000008bf00000|100%| O|  |TAMS 0x000000008bf00000| PB 0x000000008be00000| Untracked 
| 191|0x000000008bf00000, 0x000000008c000000, 0x000000008c000000|100%| O|  |TAMS 0x000000008c000000| PB 0x000000008bf00000| Untracked 
| 192|0x000000008c000000, 0x000000008c100000, 0x000000008c100000|100%| O|  |TAMS 0x000000008c100000| PB 0x000000008c000000| Untracked 
| 193|0x000000008c100000, 0x000000008c200000, 0x000000008c200000|100%| O|  |TAMS 0x000000008c200000| PB 0x000000008c100000| Untracked 
| 194|0x000000008c200000, 0x000000008c300000, 0x000000008c300000|100%| O|  |TAMS 0x000000008c300000| PB 0x000000008c200000| Untracked 
| 195|0x000000008c300000, 0x000000008c400000, 0x000000008c400000|100%| O|  |TAMS 0x000000008c400000| PB 0x000000008c300000| Untracked 
| 196|0x000000008c400000, 0x000000008c500000, 0x000000008c500000|100%| O|  |TAMS 0x000000008c500000| PB 0x000000008c400000| Untracked 
| 197|0x000000008c500000, 0x000000008c600000, 0x000000008c600000|100%| O|  |TAMS 0x000000008c600000| PB 0x000000008c500000| Untracked 
| 198|0x000000008c600000, 0x000000008c700000, 0x000000008c700000|100%| O|  |TAMS 0x000000008c700000| PB 0x000000008c600000| Untracked 
| 199|0x000000008c700000, 0x000000008c800000, 0x000000008c800000|100%| O|  |TAMS 0x000000008c800000| PB 0x000000008c700000| Untracked 
| 200|0x000000008c800000, 0x000000008c900000, 0x000000008c900000|100%| O|  |TAMS 0x000000008c900000| PB 0x000000008c800000| Updating 
| 201|0x000000008c900000, 0x000000008ca00000, 0x000000008ca00000|100%|HS|  |TAMS 0x000000008ca00000| PB 0x000000008c900000| Complete 
| 202|0x000000008ca00000, 0x000000008cb00000, 0x000000008cb00000|100%|HC|  |TAMS 0x000000008cb00000| PB 0x000000008ca00000| Complete 
| 203|0x000000008cb00000, 0x000000008cc00000, 0x000000008cc00000|100%| O|  |TAMS 0x000000008cc00000| PB 0x000000008cb00000| Updating 
| 204|0x000000008cc00000, 0x000000008cd00000, 0x000000008cd00000|100%| O|  |TAMS 0x000000008cd00000| PB 0x000000008cc00000| Untracked 
| 205|0x000000008cd00000, 0x000000008ce00000, 0x000000008ce00000|100%| O|  |TAMS 0x000000008ce00000| PB 0x000000008cd00000| Untracked 
| 206|0x000000008ce00000, 0x000000008cf00000, 0x000000008cf00000|100%| O|  |TAMS 0x000000008cf00000| PB 0x000000008ce00000| Updating 
| 207|0x000000008cf00000, 0x000000008d000000, 0x000000008d000000|100%| O|  |TAMS 0x000000008d000000| PB 0x000000008cf00000| Updating 
| 208|0x000000008d000000, 0x000000008d100000, 0x000000008d100000|100%| O|  |TAMS 0x000000008d100000| PB 0x000000008d000000| Updating 
| 209|0x000000008d100000, 0x000000008d200000, 0x000000008d200000|100%| O|  |TAMS 0x000000008d200000| PB 0x000000008d100000| Updating 
| 210|0x000000008d200000, 0x000000008d300000, 0x000000008d300000|100%| O|  |TAMS 0x000000008d300000| PB 0x000000008d200000| Untracked 
| 211|0x000000008d300000, 0x000000008d400000, 0x000000008d400000|100%| O|  |TAMS 0x000000008d400000| PB 0x000000008d300000| Untracked 
| 212|0x000000008d400000, 0x000000008d500000, 0x000000008d500000|100%| O|  |TAMS 0x000000008d500000| PB 0x000000008d400000| Untracked 
| 213|0x000000008d500000, 0x000000008d600000, 0x000000008d600000|100%| O|  |TAMS 0x000000008d600000| PB 0x000000008d500000| Untracked 
| 214|0x000000008d600000, 0x000000008d700000, 0x000000008d700000|100%| O|  |TAMS 0x000000008d700000| PB 0x000000008d600000| Untracked 
| 215|0x000000008d700000, 0x000000008d800000, 0x000000008d800000|100%| O|  |TAMS 0x000000008d800000| PB 0x000000008d700000| Updating 
| 216|0x000000008d800000, 0x000000008d900000, 0x000000008d900000|100%| O|  |TAMS 0x000000008d900000| PB 0x000000008d800000| Untracked 
| 217|0x000000008d900000, 0x000000008da00000, 0x000000008da00000|100%| O|  |TAMS 0x000000008da00000| PB 0x000000008d900000| Untracked 
| 218|0x000000008da00000, 0x000000008db00000, 0x000000008db00000|100%| O|  |TAMS 0x000000008db00000| PB 0x000000008da00000| Updating 
| 219|0x000000008db00000, 0x000000008dc00000, 0x000000008dc00000|100%| O|  |TAMS 0x000000008dc00000| PB 0x000000008db00000| Untracked 
| 220|0x000000008dc00000, 0x000000008dd00000, 0x000000008dd00000|100%| O|  |TAMS 0x000000008dd00000| PB 0x000000008dc00000| Untracked 
| 221|0x000000008dd00000, 0x000000008de00000, 0x000000008de00000|100%| O|  |TAMS 0x000000008de00000| PB 0x000000008dd00000| Untracked 
| 222|0x000000008de00000, 0x000000008df00000, 0x000000008df00000|100%| O|  |TAMS 0x000000008df00000| PB 0x000000008de00000| Untracked 
| 223|0x000000008df00000, 0x000000008e000000, 0x000000008e000000|100%| O|  |TAMS 0x000000008e000000| PB 0x000000008df00000| Untracked 
| 224|0x000000008e000000, 0x000000008e100000, 0x000000008e100000|100%| O|  |TAMS 0x000000008e100000| PB 0x000000008e000000| Untracked 
| 225|0x000000008e100000, 0x000000008e200000, 0x000000008e200000|100%| O|  |TAMS 0x000000008e200000| PB 0x000000008e100000| Untracked 
| 226|0x000000008e200000, 0x000000008e300000, 0x000000008e300000|100%| O|  |TAMS 0x000000008e300000| PB 0x000000008e200000| Untracked 
| 227|0x000000008e300000, 0x000000008e400000, 0x000000008e400000|100%| O|  |TAMS 0x000000008e400000| PB 0x000000008e300000| Updating 
| 228|0x000000008e400000, 0x000000008e500000, 0x000000008e500000|100%| O|  |TAMS 0x000000008e500000| PB 0x000000008e400000| Updating 
| 229|0x000000008e500000, 0x000000008e600000, 0x000000008e600000|100%| O|  |TAMS 0x000000008e600000| PB 0x000000008e500000| Untracked 
| 230|0x000000008e600000, 0x000000008e700000, 0x000000008e700000|100%|HS|  |TAMS 0x000000008e700000| PB 0x000000008e600000| Complete 
| 231|0x000000008e700000, 0x000000008e800000, 0x000000008e800000|100%|HC|  |TAMS 0x000000008e800000| PB 0x000000008e700000| Complete 
| 232|0x000000008e800000, 0x000000008e900000, 0x000000008e900000|100%|HS|  |TAMS 0x000000008e900000| PB 0x000000008e800000| Complete 
| 233|0x000000008e900000, 0x000000008ea00000, 0x000000008ea00000|100%|HC|  |TAMS 0x000000008ea00000| PB 0x000000008e900000| Complete 
| 234|0x000000008ea00000, 0x000000008eb00000, 0x000000008eb00000|100%| O|  |TAMS 0x000000008eb00000| PB 0x000000008ea00000| Untracked 
| 235|0x000000008eb00000, 0x000000008ec00000, 0x000000008ec00000|100%|HS|  |TAMS 0x000000008ec00000| PB 0x000000008eb00000| Complete 
| 236|0x000000008ec00000, 0x000000008ed00000, 0x000000008ed00000|100%|HS|  |TAMS 0x000000008ed00000| PB 0x000000008ec00000| Complete 
| 237|0x000000008ed00000, 0x000000008ee00000, 0x000000008ee00000|100%|HS|  |TAMS 0x000000008ee00000| PB 0x000000008ed00000| Complete 
| 238|0x000000008ee00000, 0x000000008ef00000, 0x000000008ef00000|100%| O|  |TAMS 0x000000008ef00000| PB 0x000000008ee00000| Untracked 
| 239|0x000000008ef00000, 0x000000008f000000, 0x000000008f000000|100%| O|  |TAMS 0x000000008f000000| PB 0x000000008ef00000| Untracked 
| 240|0x000000008f000000, 0x000000008f100000, 0x000000008f100000|100%| O|  |TAMS 0x000000008f100000| PB 0x000000008f000000| Updating 
| 241|0x000000008f100000, 0x000000008f200000, 0x000000008f200000|100%| O|  |TAMS 0x000000008f200000| PB 0x000000008f100000| Untracked 
| 242|0x000000008f200000, 0x000000008f300000, 0x000000008f300000|100%| O|  |TAMS 0x000000008f300000| PB 0x000000008f200000| Untracked 
| 243|0x000000008f300000, 0x000000008f400000, 0x000000008f400000|100%| O|  |TAMS 0x000000008f400000| PB 0x000000008f300000| Untracked 
| 244|0x000000008f400000, 0x000000008f500000, 0x000000008f500000|100%| O|  |TAMS 0x000000008f500000| PB 0x000000008f400000| Untracked 
| 245|0x000000008f500000, 0x000000008f600000, 0x000000008f600000|100%| O|  |TAMS 0x000000008f600000| PB 0x000000008f500000| Untracked 
| 246|0x000000008f600000, 0x000000008f700000, 0x000000008f700000|100%| O|  |TAMS 0x000000008f700000| PB 0x000000008f600000| Untracked 
| 247|0x000000008f700000, 0x000000008f800000, 0x000000008f800000|100%| O|  |TAMS 0x000000008f800000| PB 0x000000008f700000| Untracked 
| 248|0x000000008f800000, 0x000000008f900000, 0x000000008f900000|100%| O|  |TAMS 0x000000008f900000| PB 0x000000008f800000| Untracked 
| 249|0x000000008f900000, 0x000000008fa00000, 0x000000008fa00000|100%| O|  |TAMS 0x000000008fa00000| PB 0x000000008f900000| Untracked 
| 250|0x000000008fa00000, 0x000000008fb00000, 0x000000008fb00000|100%| O|  |TAMS 0x000000008fb00000| PB 0x000000008fa00000| Untracked 
| 251|0x000000008fb00000, 0x000000008fc00000, 0x000000008fc00000|100%| O|  |TAMS 0x000000008fc00000| PB 0x000000008fb00000| Untracked 
| 252|0x000000008fc00000, 0x000000008fd00000, 0x000000008fd00000|100%| O|  |TAMS 0x000000008fd00000| PB 0x000000008fc00000| Updating 
| 253|0x000000008fd00000, 0x000000008fe00000, 0x000000008fe00000|100%| O|  |TAMS 0x000000008fe00000| PB 0x000000008fd00000| Updating 
| 254|0x000000008fe00000, 0x000000008ff00000, 0x000000008ff00000|100%|HS|  |TAMS 0x000000008ff00000| PB 0x000000008fe00000| Complete 
| 255|0x000000008ff00000, 0x0000000090000000, 0x0000000090000000|100%| O|  |TAMS 0x0000000090000000| PB 0x000000008ff00000| Untracked 
| 256|0x0000000090000000, 0x0000000090100000, 0x0000000090100000|100%| O|  |TAMS 0x0000000090100000| PB 0x0000000090000000| Untracked 
| 257|0x0000000090100000, 0x0000000090200000, 0x0000000090200000|100%| O|  |TAMS 0x0000000090200000| PB 0x0000000090100000| Untracked 
| 258|0x0000000090200000, 0x0000000090300000, 0x0000000090300000|100%| O|  |TAMS 0x0000000090300000| PB 0x0000000090200000| Untracked 
| 259|0x0000000090300000, 0x0000000090400000, 0x0000000090400000|100%| O|  |TAMS 0x0000000090400000| PB 0x0000000090300000| Untracked 
| 260|0x0000000090400000, 0x0000000090500000, 0x0000000090500000|100%| O|  |TAMS 0x0000000090500000| PB 0x0000000090400000| Untracked 
| 261|0x0000000090500000, 0x0000000090600000, 0x0000000090600000|100%| O|  |TAMS 0x0000000090600000| PB 0x0000000090500000| Untracked 
| 262|0x0000000090600000, 0x0000000090700000, 0x0000000090700000|100%| O|  |TAMS 0x0000000090700000| PB 0x0000000090600000| Untracked 
| 263|0x0000000090700000, 0x0000000090800000, 0x0000000090800000|100%| O|  |TAMS 0x0000000090800000| PB 0x0000000090700000| Untracked 
| 264|0x0000000090800000, 0x0000000090900000, 0x0000000090900000|100%| O|  |TAMS 0x0000000090900000| PB 0x0000000090800000| Untracked 
| 265|0x0000000090900000, 0x0000000090a00000, 0x0000000090a00000|100%| O|  |TAMS 0x0000000090a00000| PB 0x0000000090900000| Untracked 
| 266|0x0000000090a00000, 0x0000000090b00000, 0x0000000090b00000|100%| O|  |TAMS 0x0000000090b00000| PB 0x0000000090a00000| Untracked 
| 267|0x0000000090b00000, 0x0000000090c00000, 0x0000000090c00000|100%| O|  |TAMS 0x0000000090c00000| PB 0x0000000090b00000| Untracked 
| 268|0x0000000090c00000, 0x0000000090d00000, 0x0000000090d00000|100%| O|  |TAMS 0x0000000090d00000| PB 0x0000000090c00000| Untracked 
| 269|0x0000000090d00000, 0x0000000090e00000, 0x0000000090e00000|100%| O|  |TAMS 0x0000000090e00000| PB 0x0000000090d00000| Untracked 
| 270|0x0000000090e00000, 0x0000000090f00000, 0x0000000090f00000|100%| O|  |TAMS 0x0000000090f00000| PB 0x0000000090e00000| Untracked 
| 271|0x0000000090f00000, 0x0000000091000000, 0x0000000091000000|100%|HS|  |TAMS 0x0000000091000000| PB 0x0000000090f00000| Complete 
| 272|0x0000000091000000, 0x0000000091100000, 0x0000000091100000|100%| O|  |TAMS 0x0000000091100000| PB 0x0000000091000000| Untracked 
| 273|0x0000000091100000, 0x0000000091200000, 0x0000000091200000|100%| O|  |TAMS 0x0000000091200000| PB 0x0000000091100000| Updating 
| 274|0x0000000091200000, 0x0000000091300000, 0x0000000091300000|100%| O|  |TAMS 0x0000000091300000| PB 0x0000000091200000| Untracked 
| 275|0x0000000091300000, 0x0000000091400000, 0x0000000091400000|100%| O|  |TAMS 0x0000000091400000| PB 0x0000000091300000| Untracked 
| 276|0x0000000091400000, 0x0000000091500000, 0x0000000091500000|100%| O|  |TAMS 0x0000000091500000| PB 0x0000000091400000| Untracked 
| 277|0x0000000091500000, 0x0000000091600000, 0x0000000091600000|100%| O|  |TAMS 0x0000000091600000| PB 0x0000000091500000| Untracked 
| 278|0x0000000091600000, 0x0000000091700000, 0x0000000091700000|100%| O|  |TAMS 0x0000000091700000| PB 0x0000000091600000| Untracked 
| 279|0x0000000091700000, 0x0000000091800000, 0x0000000091800000|100%| O|  |TAMS 0x0000000091800000| PB 0x0000000091700000| Untracked 
| 280|0x0000000091800000, 0x0000000091900000, 0x0000000091900000|100%| O|  |TAMS 0x0000000091900000| PB 0x0000000091800000| Untracked 
| 281|0x0000000091900000, 0x0000000091a00000, 0x0000000091a00000|100%| O|  |TAMS 0x0000000091a00000| PB 0x0000000091900000| Untracked 
| 282|0x0000000091a00000, 0x0000000091b00000, 0x0000000091b00000|100%| O|  |TAMS 0x0000000091b00000| PB 0x0000000091a00000| Untracked 
| 283|0x0000000091b00000, 0x0000000091c00000, 0x0000000091c00000|100%| O|  |TAMS 0x0000000091c00000| PB 0x0000000091b00000| Untracked 
| 284|0x0000000091c00000, 0x0000000091d00000, 0x0000000091d00000|100%| O|  |TAMS 0x0000000091d00000| PB 0x0000000091c00000| Untracked 
| 285|0x0000000091d00000, 0x0000000091e00000, 0x0000000091e00000|100%| O|  |TAMS 0x0000000091e00000| PB 0x0000000091d00000| Untracked 
| 286|0x0000000091e00000, 0x0000000091f00000, 0x0000000091f00000|100%| O|  |TAMS 0x0000000091f00000| PB 0x0000000091e00000| Updating 
| 287|0x0000000091f00000, 0x0000000092000000, 0x0000000092000000|100%| O|  |TAMS 0x0000000092000000| PB 0x0000000091f00000| Untracked 
| 288|0x0000000092000000, 0x0000000092100000, 0x0000000092100000|100%| O|  |TAMS 0x0000000092100000| PB 0x0000000092000000| Untracked 
| 289|0x0000000092100000, 0x0000000092200000, 0x0000000092200000|100%| O|  |TAMS 0x0000000092200000| PB 0x0000000092100000| Updating 
| 290|0x0000000092200000, 0x0000000092300000, 0x0000000092300000|100%| O|  |TAMS 0x0000000092300000| PB 0x0000000092200000| Untracked 
| 291|0x0000000092300000, 0x0000000092400000, 0x0000000092400000|100%| O|  |TAMS 0x0000000092400000| PB 0x0000000092300000| Untracked 
| 292|0x0000000092400000, 0x0000000092500000, 0x0000000092500000|100%| O|  |TAMS 0x0000000092500000| PB 0x0000000092400000| Untracked 
| 293|0x0000000092500000, 0x0000000092600000, 0x0000000092600000|100%| O|  |TAMS 0x0000000092600000| PB 0x0000000092500000| Untracked 
| 294|0x0000000092600000, 0x0000000092700000, 0x0000000092700000|100%| O|  |TAMS 0x0000000092700000| PB 0x0000000092600000| Untracked 
| 295|0x0000000092700000, 0x0000000092800000, 0x0000000092800000|100%| O|  |TAMS 0x0000000092800000| PB 0x0000000092700000| Untracked 
| 296|0x0000000092800000, 0x0000000092900000, 0x0000000092900000|100%| O|  |TAMS 0x0000000092900000| PB 0x0000000092800000| Untracked 
| 297|0x0000000092900000, 0x0000000092a00000, 0x0000000092a00000|100%| O|  |TAMS 0x0000000092a00000| PB 0x0000000092900000| Untracked 
| 298|0x0000000092a00000, 0x0000000092b00000, 0x0000000092b00000|100%| O|  |TAMS 0x0000000092b00000| PB 0x0000000092a00000| Untracked 
| 299|0x0000000092b00000, 0x0000000092c00000, 0x0000000092c00000|100%| O|  |TAMS 0x0000000092c00000| PB 0x0000000092c00000| Untracked 
| 300|0x0000000092c00000, 0x0000000092d00000, 0x0000000092d00000|100%| O|  |TAMS 0x0000000092d00000| PB 0x0000000092d00000| Untracked 
| 301|0x0000000092d00000, 0x0000000092e00000, 0x0000000092e00000|100%| O|  |TAMS 0x0000000092e00000| PB 0x0000000092e00000| Untracked 
| 302|0x0000000092e00000, 0x0000000092f00000, 0x0000000092f00000|100%| O|  |TAMS 0x0000000092f00000| PB 0x0000000092f00000| Untracked 
| 303|0x0000000092f00000, 0x0000000093000000, 0x0000000093000000|100%| O|  |TAMS 0x0000000093000000| PB 0x0000000093000000| Untracked 
| 304|0x0000000093000000, 0x0000000093100000, 0x0000000093100000|100%| O|  |TAMS 0x0000000093100000| PB 0x0000000093100000| Untracked 
| 305|0x0000000093100000, 0x0000000093200000, 0x0000000093200000|100%| O|  |TAMS 0x0000000093200000| PB 0x0000000093200000| Untracked 
| 306|0x0000000093200000, 0x0000000093300000, 0x0000000093300000|100%| O|  |TAMS 0x0000000093300000| PB 0x0000000093300000| Untracked 
| 307|0x0000000093300000, 0x0000000093400000, 0x0000000093400000|100%| O|  |TAMS 0x0000000093400000| PB 0x0000000093400000| Untracked 
| 308|0x0000000093400000, 0x0000000093500000, 0x0000000093500000|100%| O|  |TAMS 0x0000000093500000| PB 0x0000000093500000| Untracked 
| 309|0x0000000093500000, 0x0000000093600000, 0x0000000093600000|100%| O|  |TAMS 0x0000000093600000| PB 0x0000000093600000| Updating 
| 310|0x0000000093600000, 0x0000000093700000, 0x0000000093700000|100%| O|  |TAMS 0x0000000093700000| PB 0x0000000093700000| Untracked 
| 311|0x0000000093700000, 0x0000000093800000, 0x0000000093800000|100%| O|  |TAMS 0x0000000093800000| PB 0x0000000093800000| Untracked 
| 312|0x0000000093800000, 0x0000000093900000, 0x0000000093900000|100%| O|  |TAMS 0x0000000093900000| PB 0x0000000093900000| Untracked 
| 313|0x0000000093900000, 0x0000000093a00000, 0x0000000093a00000|100%| O|  |TAMS 0x0000000093a00000| PB 0x0000000093a00000| Untracked 
| 314|0x0000000093a00000, 0x0000000093b00000, 0x0000000093b00000|100%| O|  |TAMS 0x0000000093b00000| PB 0x0000000093b00000| Untracked 
| 315|0x0000000093b00000, 0x0000000093c00000, 0x0000000093c00000|100%| O|  |TAMS 0x0000000093c00000| PB 0x0000000093c00000| Untracked 
| 316|0x0000000093c00000, 0x0000000093d00000, 0x0000000093d00000|100%| O|  |TAMS 0x0000000093d00000| PB 0x0000000093d00000| Untracked 
| 317|0x0000000093d00000, 0x0000000093e00000, 0x0000000093e00000|100%| O|  |TAMS 0x0000000093e00000| PB 0x0000000093e00000| Untracked 
| 318|0x0000000093e00000, 0x0000000093f00000, 0x0000000093f00000|100%|HS|  |TAMS 0x0000000093f00000| PB 0x0000000093e00000| Complete 
| 319|0x0000000093f00000, 0x0000000094000000, 0x0000000094000000|100%|HS|  |TAMS 0x0000000094000000| PB 0x0000000093f00000| Complete 
| 320|0x0000000094000000, 0x0000000094100000, 0x0000000094100000|100%|HS|  |TAMS 0x0000000094100000| PB 0x0000000094000000| Complete 
| 321|0x0000000094100000, 0x0000000094200000, 0x0000000094200000|100%| O|  |TAMS 0x0000000094200000| PB 0x0000000094200000| Untracked 
| 322|0x0000000094200000, 0x0000000094300000, 0x0000000094300000|100%| O|  |TAMS 0x0000000094300000| PB 0x0000000094300000| Untracked 
| 323|0x0000000094300000, 0x0000000094400000, 0x0000000094400000|100%| O|  |TAMS 0x0000000094400000| PB 0x0000000094400000| Untracked 
| 324|0x0000000094400000, 0x0000000094500000, 0x0000000094500000|100%| O|  |TAMS 0x0000000094500000| PB 0x0000000094500000| Untracked 
| 325|0x0000000094500000, 0x0000000094600000, 0x0000000094600000|100%| O|  |TAMS 0x0000000094600000| PB 0x0000000094600000| Untracked 
| 326|0x0000000094600000, 0x0000000094700000, 0x0000000094700000|100%| O|  |TAMS 0x0000000094700000| PB 0x0000000094700000| Untracked 
| 327|0x0000000094700000, 0x0000000094800000, 0x0000000094800000|100%| O|  |TAMS 0x0000000094800000| PB 0x0000000094800000| Untracked 
| 328|0x0000000094800000, 0x0000000094900000, 0x0000000094900000|100%| O|  |TAMS 0x0000000094900000| PB 0x0000000094900000| Untracked 
| 329|0x0000000094900000, 0x0000000094a00000, 0x0000000094a00000|100%| O|  |TAMS 0x0000000094a00000| PB 0x0000000094a00000| Untracked 
| 330|0x0000000094a00000, 0x0000000094b00000, 0x0000000094b00000|100%| O|  |TAMS 0x0000000094b00000| PB 0x0000000094b00000| Untracked 
| 331|0x0000000094b00000, 0x0000000094c00000, 0x0000000094c00000|100%| O|  |TAMS 0x0000000094c00000| PB 0x0000000094c00000| Untracked 
| 332|0x0000000094c00000, 0x0000000094d00000, 0x0000000094d00000|100%| O|  |TAMS 0x0000000094d00000| PB 0x0000000094d00000| Untracked 
| 333|0x0000000094d00000, 0x0000000094e00000, 0x0000000094e00000|100%| O|  |TAMS 0x0000000094e00000| PB 0x0000000094e00000| Untracked 
| 334|0x0000000094e00000, 0x0000000094f00000, 0x0000000094f00000|100%| O|  |TAMS 0x0000000094f00000| PB 0x0000000094f00000| Untracked 
| 335|0x0000000094f00000, 0x0000000095000000, 0x0000000095000000|100%| O|  |TAMS 0x0000000095000000| PB 0x0000000095000000| Untracked 
| 336|0x0000000095000000, 0x0000000095100000, 0x0000000095100000|100%| O|  |TAMS 0x0000000095100000| PB 0x0000000095100000| Untracked 
| 337|0x0000000095100000, 0x0000000095200000, 0x0000000095200000|100%| O|  |TAMS 0x0000000095200000| PB 0x0000000095200000| Untracked 
| 338|0x0000000095200000, 0x0000000095300000, 0x0000000095300000|100%| O|  |TAMS 0x0000000095300000| PB 0x0000000095300000| Untracked 
| 339|0x0000000095300000, 0x0000000095400000, 0x0000000095400000|100%| O|  |TAMS 0x0000000095400000| PB 0x0000000095400000| Untracked 
| 340|0x0000000095400000, 0x0000000095500000, 0x0000000095500000|100%| O|  |TAMS 0x0000000095500000| PB 0x0000000095500000| Updating 
| 341|0x0000000095500000, 0x0000000095600000, 0x0000000095600000|100%| O|  |TAMS 0x0000000095600000| PB 0x0000000095600000| Updating 
| 342|0x0000000095600000, 0x0000000095700000, 0x0000000095700000|100%| O|  |TAMS 0x0000000095700000| PB 0x0000000095700000| Untracked 
| 343|0x0000000095700000, 0x0000000095800000, 0x0000000095800000|100%| O|  |TAMS 0x0000000095800000| PB 0x0000000095800000| Untracked 
| 344|0x0000000095800000, 0x0000000095900000, 0x0000000095900000|100%| O|  |TAMS 0x0000000095900000| PB 0x0000000095900000| Untracked 
| 345|0x0000000095900000, 0x0000000095a00000, 0x0000000095a00000|100%| O|  |TAMS 0x0000000095a00000| PB 0x0000000095a00000| Untracked 
| 346|0x0000000095a00000, 0x0000000095b00000, 0x0000000095b00000|100%| O|  |TAMS 0x0000000095b00000| PB 0x0000000095b00000| Untracked 
| 347|0x0000000095b00000, 0x0000000095c00000, 0x0000000095c00000|100%| O|  |TAMS 0x0000000095c00000| PB 0x0000000095c00000| Untracked 
| 348|0x0000000095c00000, 0x0000000095d00000, 0x0000000095d00000|100%| O|  |TAMS 0x0000000095d00000| PB 0x0000000095d00000| Untracked 
| 349|0x0000000095d00000, 0x0000000095e00000, 0x0000000095e00000|100%| O|  |TAMS 0x0000000095e00000| PB 0x0000000095e00000| Untracked 
| 350|0x0000000095e00000, 0x0000000095f00000, 0x0000000095f00000|100%| O|  |TAMS 0x0000000095f00000| PB 0x0000000095f00000| Untracked 
| 351|0x0000000095f00000, 0x0000000096000000, 0x0000000096000000|100%| O|  |TAMS 0x0000000096000000| PB 0x0000000096000000| Untracked 
| 352|0x0000000096000000, 0x0000000096100000, 0x0000000096100000|100%| O|  |TAMS 0x0000000096100000| PB 0x0000000096100000| Untracked 
| 353|0x0000000096100000, 0x0000000096200000, 0x0000000096200000|100%| O|  |TAMS 0x0000000096200000| PB 0x0000000096200000| Untracked 
| 354|0x0000000096200000, 0x0000000096300000, 0x0000000096300000|100%| O|  |TAMS 0x0000000096300000| PB 0x0000000096300000| Untracked 
| 355|0x0000000096300000, 0x0000000096400000, 0x0000000096400000|100%| O|  |TAMS 0x0000000096400000| PB 0x0000000096400000| Updating 
| 356|0x0000000096400000, 0x0000000096500000, 0x0000000096500000|100%| O|  |TAMS 0x0000000096500000| PB 0x0000000096500000| Updating 
| 357|0x0000000096500000, 0x0000000096600000, 0x0000000096600000|100%| O|  |TAMS 0x0000000096600000| PB 0x0000000096600000| Untracked 
| 358|0x0000000096600000, 0x0000000096700000, 0x0000000096700000|100%| O|  |TAMS 0x0000000096700000| PB 0x0000000096700000| Untracked 
| 359|0x0000000096700000, 0x0000000096800000, 0x0000000096800000|100%| O|  |TAMS 0x0000000096800000| PB 0x0000000096800000| Untracked 
| 360|0x0000000096800000, 0x0000000096900000, 0x0000000096900000|100%| O|  |TAMS 0x0000000096900000| PB 0x0000000096900000| Untracked 
| 361|0x0000000096900000, 0x0000000096a00000, 0x0000000096a00000|100%| O|  |TAMS 0x0000000096a00000| PB 0x0000000096a00000| Updating 
| 362|0x0000000096a00000, 0x0000000096b00000, 0x0000000096b00000|100%| O|  |TAMS 0x0000000096b00000| PB 0x0000000096b00000| Untracked 
| 363|0x0000000096b00000, 0x0000000096c00000, 0x0000000096c00000|100%| O|  |TAMS 0x0000000096c00000| PB 0x0000000096c00000| Untracked 
| 364|0x0000000096c00000, 0x0000000096d00000, 0x0000000096d00000|100%| O|  |TAMS 0x0000000096d00000| PB 0x0000000096d00000| Untracked 
| 365|0x0000000096d00000, 0x0000000096e00000, 0x0000000096e00000|100%| O|  |TAMS 0x0000000096e00000| PB 0x0000000096e00000| Untracked 
| 366|0x0000000096e00000, 0x0000000096f00000, 0x0000000096f00000|100%| O|  |TAMS 0x0000000096f00000| PB 0x0000000096f00000| Updating 
| 367|0x0000000096f00000, 0x0000000097000000, 0x0000000097000000|100%| O|  |TAMS 0x0000000097000000| PB 0x0000000097000000| Untracked 
| 368|0x0000000097000000, 0x0000000097100000, 0x0000000097100000|100%| O|  |TAMS 0x0000000097100000| PB 0x0000000097100000| Untracked 
| 369|0x0000000097100000, 0x0000000097200000, 0x0000000097200000|100%| O|  |TAMS 0x0000000097200000| PB 0x0000000097200000| Updating 
| 370|0x0000000097200000, 0x0000000097300000, 0x0000000097300000|100%| O|  |TAMS 0x0000000097300000| PB 0x0000000097300000| Untracked 
| 371|0x0000000097300000, 0x0000000097400000, 0x0000000097400000|100%| O|  |TAMS 0x0000000097400000| PB 0x0000000097400000| Updating 
| 372|0x0000000097400000, 0x0000000097500000, 0x0000000097500000|100%| O|  |TAMS 0x0000000097500000| PB 0x0000000097500000| Untracked 
| 373|0x0000000097500000, 0x0000000097600000, 0x0000000097600000|100%| O|  |TAMS 0x0000000097600000| PB 0x0000000097600000| Untracked 
| 374|0x0000000097600000, 0x0000000097700000, 0x0000000097700000|100%| O|  |TAMS 0x0000000097700000| PB 0x0000000097700000| Untracked 
| 375|0x0000000097700000, 0x0000000097800000, 0x0000000097800000|100%| O|  |TAMS 0x0000000097800000| PB 0x0000000097800000| Untracked 
| 376|0x0000000097800000, 0x0000000097900000, 0x0000000097900000|100%| O|  |TAMS 0x0000000097900000| PB 0x0000000097900000| Untracked 
| 377|0x0000000097900000, 0x0000000097a00000, 0x0000000097a00000|100%| O|  |TAMS 0x0000000097a00000| PB 0x0000000097a00000| Untracked 
| 378|0x0000000097a00000, 0x0000000097b00000, 0x0000000097b00000|100%| O|  |TAMS 0x0000000097b00000| PB 0x0000000097b00000| Untracked 
| 379|0x0000000097b00000, 0x0000000097c00000, 0x0000000097c00000|100%| O|  |TAMS 0x0000000097c00000| PB 0x0000000097c00000| Untracked 
| 380|0x0000000097c00000, 0x0000000097d00000, 0x0000000097d00000|100%| O|  |TAMS 0x0000000097d00000| PB 0x0000000097d00000| Untracked 
| 381|0x0000000097d00000, 0x0000000097e00000, 0x0000000097e00000|100%| O|  |TAMS 0x0000000097e00000| PB 0x0000000097e00000| Untracked 
| 382|0x0000000097e00000, 0x0000000097f00000, 0x0000000097f00000|100%| O|  |TAMS 0x0000000097f00000| PB 0x0000000097f00000| Untracked 
| 383|0x0000000097f00000, 0x0000000098000000, 0x0000000098000000|100%| O|  |TAMS 0x0000000098000000| PB 0x0000000098000000| Untracked 
| 384|0x0000000098000000, 0x0000000098100000, 0x0000000098100000|100%| O|  |TAMS 0x0000000098100000| PB 0x0000000098100000| Untracked 
| 385|0x0000000098100000, 0x0000000098200000, 0x0000000098200000|100%| O|  |TAMS 0x0000000098200000| PB 0x0000000098200000| Untracked 
| 386|0x0000000098200000, 0x0000000098300000, 0x0000000098300000|100%| O|  |TAMS 0x0000000098300000| PB 0x0000000098300000| Untracked 
| 387|0x0000000098300000, 0x0000000098400000, 0x0000000098400000|100%| O|  |TAMS 0x0000000098400000| PB 0x0000000098400000| Untracked 
| 388|0x0000000098400000, 0x0000000098500000, 0x0000000098500000|100%| O|  |TAMS 0x0000000098500000| PB 0x0000000098500000| Untracked 
| 389|0x0000000098500000, 0x0000000098600000, 0x0000000098600000|100%| O|  |TAMS 0x0000000098600000| PB 0x0000000098600000| Updating 
| 390|0x0000000098600000, 0x0000000098700000, 0x0000000098700000|100%| O|  |TAMS 0x0000000098700000| PB 0x0000000098700000| Untracked 
| 391|0x0000000098700000, 0x0000000098800000, 0x0000000098800000|100%| O|  |TAMS 0x0000000098800000| PB 0x0000000098800000| Untracked 
| 392|0x0000000098800000, 0x0000000098900000, 0x0000000098900000|100%| O|  |TAMS 0x0000000098900000| PB 0x0000000098900000| Untracked 
| 393|0x0000000098900000, 0x0000000098a00000, 0x0000000098a00000|100%| O|  |TAMS 0x0000000098a00000| PB 0x0000000098a00000| Untracked 
| 394|0x0000000098a00000, 0x0000000098b00000, 0x0000000098b00000|100%| O|  |TAMS 0x0000000098b00000| PB 0x0000000098b00000| Untracked 
| 395|0x0000000098b00000, 0x0000000098c00000, 0x0000000098c00000|100%| O|  |TAMS 0x0000000098c00000| PB 0x0000000098c00000| Untracked 
| 396|0x0000000098c00000, 0x0000000098d00000, 0x0000000098d00000|100%| O|  |TAMS 0x0000000098d00000| PB 0x0000000098d00000| Updating 
| 397|0x0000000098d00000, 0x0000000098e00000, 0x0000000098e00000|100%| O|  |TAMS 0x0000000098e00000| PB 0x0000000098e00000| Updating 
| 398|0x0000000098e00000, 0x0000000098f00000, 0x0000000098f00000|100%| O|  |TAMS 0x0000000098f00000| PB 0x0000000098f00000| Untracked 
| 399|0x0000000098f00000, 0x0000000099000000, 0x0000000099000000|100%| O|  |TAMS 0x0000000099000000| PB 0x0000000099000000| Updating 
| 400|0x0000000099000000, 0x0000000099100000, 0x0000000099100000|100%| O|  |TAMS 0x0000000099100000| PB 0x0000000099100000| Updating 
| 401|0x0000000099100000, 0x0000000099200000, 0x0000000099200000|100%| O|  |TAMS 0x0000000099200000| PB 0x0000000099200000| Untracked 
| 402|0x0000000099200000, 0x0000000099300000, 0x0000000099300000|100%| O|  |TAMS 0x0000000099300000| PB 0x0000000099300000| Untracked 
| 403|0x0000000099300000, 0x0000000099400000, 0x0000000099400000|100%| O|  |TAMS 0x0000000099400000| PB 0x0000000099400000| Untracked 
| 404|0x0000000099400000, 0x0000000099500000, 0x0000000099500000|100%| O|  |TAMS 0x0000000099500000| PB 0x0000000099500000| Untracked 
| 405|0x0000000099500000, 0x0000000099600000, 0x0000000099600000|100%| O|  |TAMS 0x0000000099500000| PB 0x0000000099500000| Untracked 
| 406|0x0000000099600000, 0x0000000099700000, 0x0000000099700000|100%| O|  |TAMS 0x0000000099700000| PB 0x0000000099700000| Updating 
| 407|0x0000000099700000, 0x0000000099800000, 0x0000000099800000|100%| O|  |TAMS 0x0000000099800000| PB 0x0000000099800000| Untracked 
| 408|0x0000000099800000, 0x0000000099900000, 0x0000000099900000|100%| O|  |TAMS 0x0000000099900000| PB 0x0000000099900000| Untracked 
| 409|0x0000000099900000, 0x0000000099a00000, 0x0000000099a00000|100%| O|  |TAMS 0x0000000099a00000| PB 0x0000000099a00000| Untracked 
| 410|0x0000000099a00000, 0x0000000099b00000, 0x0000000099b00000|100%| O|  |TAMS 0x0000000099b00000| PB 0x0000000099b00000| Untracked 
| 411|0x0000000099b00000, 0x0000000099c00000, 0x0000000099c00000|100%| O|  |TAMS 0x0000000099c00000| PB 0x0000000099c00000| Untracked 
| 412|0x0000000099c00000, 0x0000000099d00000, 0x0000000099d00000|100%| O|  |TAMS 0x0000000099d00000| PB 0x0000000099d00000| Untracked 
| 413|0x0000000099d00000, 0x0000000099e00000, 0x0000000099e00000|100%| O|  |TAMS 0x0000000099e00000| PB 0x0000000099e00000| Updating 
| 414|0x0000000099e00000, 0x0000000099f00000, 0x0000000099f00000|100%| O|  |TAMS 0x0000000099f00000| PB 0x0000000099f00000| Untracked 
| 415|0x0000000099f00000, 0x000000009a000000, 0x000000009a000000|100%| O|  |TAMS 0x000000009a000000| PB 0x000000009a000000| Untracked 
| 416|0x000000009a000000, 0x000000009a100000, 0x000000009a100000|100%| O|  |TAMS 0x000000009a100000| PB 0x000000009a100000| Updating 
| 417|0x000000009a100000, 0x000000009a200000, 0x000000009a200000|100%| O|  |TAMS 0x000000009a200000| PB 0x000000009a200000| Updating 
| 418|0x000000009a200000, 0x000000009a300000, 0x000000009a300000|100%| O|  |TAMS 0x000000009a300000| PB 0x000000009a300000| Untracked 
| 419|0x000000009a300000, 0x000000009a400000, 0x000000009a400000|100%| O|  |TAMS 0x000000009a400000| PB 0x000000009a400000| Updating 
| 420|0x000000009a400000, 0x000000009a500000, 0x000000009a500000|100%| O|  |TAMS 0x000000009a500000| PB 0x000000009a500000| Untracked 
| 421|0x000000009a500000, 0x000000009a600000, 0x000000009a600000|100%| O|  |TAMS 0x000000009a600000| PB 0x000000009a600000| Updating 
| 422|0x000000009a600000, 0x000000009a700000, 0x000000009a700000|100%| O|  |TAMS 0x000000009a700000| PB 0x000000009a700000| Updating 
| 423|0x000000009a700000, 0x000000009a800000, 0x000000009a800000|100%| O|  |TAMS 0x000000009a800000| PB 0x000000009a800000| Untracked 
| 424|0x000000009a800000, 0x000000009a900000, 0x000000009a900000|100%| O|  |TAMS 0x000000009a900000| PB 0x000000009a900000| Updating 
| 425|0x000000009a900000, 0x000000009aa00000, 0x000000009aa00000|100%| O|  |TAMS 0x000000009a900000| PB 0x000000009a900000| Untracked 
| 426|0x000000009aa00000, 0x000000009ab00000, 0x000000009ab00000|100%| O|  |TAMS 0x000000009aa00000| PB 0x000000009aa00000| Untracked 
| 427|0x000000009ab00000, 0x000000009ac00000, 0x000000009ac00000|100%| O|  |TAMS 0x000000009ac00000| PB 0x000000009ac00000| Untracked 
| 428|0x000000009ac00000, 0x000000009ad00000, 0x000000009ad00000|100%| O|  |TAMS 0x000000009ad00000| PB 0x000000009ad00000| Untracked 
| 429|0x000000009ad00000, 0x000000009ae00000, 0x000000009ae00000|100%| O|  |TAMS 0x000000009ae00000| PB 0x000000009ae00000| Untracked 
| 430|0x000000009ae00000, 0x000000009af00000, 0x000000009af00000|100%| O|  |TAMS 0x000000009af00000| PB 0x000000009af00000| Untracked 
| 431|0x000000009af00000, 0x000000009b000000, 0x000000009b000000|100%| O|  |TAMS 0x000000009b000000| PB 0x000000009b000000| Untracked 
| 432|0x000000009b000000, 0x000000009b100000, 0x000000009b100000|100%| O|  |TAMS 0x000000009b100000| PB 0x000000009b100000| Untracked 
| 433|0x000000009b100000, 0x000000009b200000, 0x000000009b200000|100%| O|  |TAMS 0x000000009b200000| PB 0x000000009b200000| Untracked 
| 434|0x000000009b200000, 0x000000009b300000, 0x000000009b300000|100%| O|  |TAMS 0x000000009b200000| PB 0x000000009b200000| Untracked 
| 435|0x000000009b300000, 0x000000009b400000, 0x000000009b400000|100%| O|  |TAMS 0x000000009b400000| PB 0x000000009b400000| Updating 
| 436|0x000000009b400000, 0x000000009b500000, 0x000000009b500000|100%| O|  |TAMS 0x000000009b500000| PB 0x000000009b500000| Untracked 
| 437|0x000000009b500000, 0x000000009b600000, 0x000000009b600000|100%| O|  |TAMS 0x000000009b600000| PB 0x000000009b600000| Untracked 
| 438|0x000000009b600000, 0x000000009b700000, 0x000000009b700000|100%| O|  |TAMS 0x000000009b700000| PB 0x000000009b700000| Untracked 
| 439|0x000000009b700000, 0x000000009b800000, 0x000000009b800000|100%| O|  |TAMS 0x000000009b800000| PB 0x000000009b800000| Untracked 
| 440|0x000000009b800000, 0x000000009b900000, 0x000000009b900000|100%| O|  |TAMS 0x000000009b900000| PB 0x000000009b900000| Untracked 
| 441|0x000000009b900000, 0x000000009ba00000, 0x000000009ba00000|100%| O|  |TAMS 0x000000009ba00000| PB 0x000000009ba00000| Updating 
| 442|0x000000009ba00000, 0x000000009bb00000, 0x000000009bb00000|100%| O|  |TAMS 0x000000009bb00000| PB 0x000000009bb00000| Untracked 
| 443|0x000000009bb00000, 0x000000009bc00000, 0x000000009bc00000|100%| O|  |TAMS 0x000000009bc00000| PB 0x000000009bc00000| Untracked 
| 444|0x000000009bc00000, 0x000000009bd00000, 0x000000009bd00000|100%| O|  |TAMS 0x000000009bc00000| PB 0x000000009bc00000| Untracked 
| 445|0x000000009bd00000, 0x000000009be00000, 0x000000009be00000|100%| O|  |TAMS 0x000000009be00000| PB 0x000000009be00000| Untracked 
| 446|0x000000009be00000, 0x000000009bf00000, 0x000000009bf00000|100%| O|  |TAMS 0x000000009bf00000| PB 0x000000009bf00000| Untracked 
| 447|0x000000009bf00000, 0x000000009c000000, 0x000000009c000000|100%| O|  |TAMS 0x000000009c000000| PB 0x000000009c000000| Untracked 
| 448|0x000000009c000000, 0x000000009c100000, 0x000000009c100000|100%| O|  |TAMS 0x000000009c100000| PB 0x000000009c100000| Updating 
| 449|0x000000009c100000, 0x000000009c200000, 0x000000009c200000|100%| O|  |TAMS 0x000000009c200000| PB 0x000000009c200000| Untracked 
| 450|0x000000009c200000, 0x000000009c300000, 0x000000009c300000|100%| O|  |TAMS 0x000000009c300000| PB 0x000000009c300000| Untracked 
| 451|0x000000009c300000, 0x000000009c400000, 0x000000009c400000|100%| O|  |TAMS 0x000000009c300000| PB 0x000000009c300000| Untracked 
| 452|0x000000009c400000, 0x000000009c500000, 0x000000009c500000|100%| O|  |TAMS 0x000000009c500000| PB 0x000000009c500000| Updating 
| 453|0x000000009c500000, 0x000000009c600000, 0x000000009c600000|100%| O|  |TAMS 0x000000009c600000| PB 0x000000009c600000| Untracked 
| 454|0x000000009c600000, 0x000000009c700000, 0x000000009c700000|100%| O|  |TAMS 0x000000009c700000| PB 0x000000009c700000| Untracked 
| 455|0x000000009c700000, 0x000000009c800000, 0x000000009c800000|100%| O|  |TAMS 0x000000009c800000| PB 0x000000009c800000| Updating 
| 456|0x000000009c800000, 0x000000009c900000, 0x000000009c900000|100%| O|  |TAMS 0x000000009c900000| PB 0x000000009c900000| Untracked 
| 457|0x000000009c900000, 0x000000009ca00000, 0x000000009ca00000|100%| O|  |TAMS 0x000000009ca00000| PB 0x000000009ca00000| Updating 
| 458|0x000000009ca00000, 0x000000009cb00000, 0x000000009cb00000|100%| O|  |TAMS 0x000000009cb00000| PB 0x000000009cb00000| Updating 
| 459|0x000000009cb00000, 0x000000009cc00000, 0x000000009cc00000|100%| O|  |TAMS 0x000000009cc00000| PB 0x000000009cc00000| Untracked 
| 460|0x000000009cc00000, 0x000000009cd00000, 0x000000009cd00000|100%| O|  |TAMS 0x000000009cd00000| PB 0x000000009cd00000| Untracked 
| 461|0x000000009cd00000, 0x000000009ce00000, 0x000000009ce00000|100%| O|  |TAMS 0x000000009ce00000| PB 0x000000009ce00000| Untracked 
| 462|0x000000009ce00000, 0x000000009cf00000, 0x000000009cf00000|100%| O|  |TAMS 0x000000009cf00000| PB 0x000000009cf00000| Updating 
| 463|0x000000009cf00000, 0x000000009d000000, 0x000000009d000000|100%| O|  |TAMS 0x000000009d000000| PB 0x000000009d000000| Updating 
| 464|0x000000009d000000, 0x000000009d100000, 0x000000009d100000|100%| O|  |TAMS 0x000000009d100000| PB 0x000000009d100000| Updating 
| 465|0x000000009d100000, 0x000000009d200000, 0x000000009d200000|100%|HS|  |TAMS 0x000000009d200000| PB 0x000000009d100000| Complete 
| 466|0x000000009d200000, 0x000000009d300000, 0x000000009d300000|100%|HC|  |TAMS 0x000000009d300000| PB 0x000000009d200000| Complete 
| 467|0x000000009d300000, 0x000000009d400000, 0x000000009d400000|100%|HC|  |TAMS 0x000000009d400000| PB 0x000000009d300000| Complete 
| 468|0x000000009d400000, 0x000000009d500000, 0x000000009d500000|100%| O|  |TAMS 0x000000009d500000| PB 0x000000009d500000| Untracked 
| 469|0x000000009d500000, 0x000000009d600000, 0x000000009d600000|100%| O|  |TAMS 0x000000009d600000| PB 0x000000009d600000| Untracked 
| 470|0x000000009d600000, 0x000000009d700000, 0x000000009d700000|100%| O|  |TAMS 0x000000009d700000| PB 0x000000009d700000| Untracked 
| 471|0x000000009d700000, 0x000000009d800000, 0x000000009d800000|100%| O|  |TAMS 0x000000009d800000| PB 0x000000009d800000| Untracked 
| 472|0x000000009d800000, 0x000000009d900000, 0x000000009d900000|100%| O|  |TAMS 0x000000009d900000| PB 0x000000009d900000| Untracked 
| 473|0x000000009d900000, 0x000000009da00000, 0x000000009da00000|100%| O|  |TAMS 0x000000009da00000| PB 0x000000009da00000| Untracked 
| 474|0x000000009da00000, 0x000000009db00000, 0x000000009db00000|100%| O|  |TAMS 0x000000009db00000| PB 0x000000009db00000| Untracked 
| 475|0x000000009db00000, 0x000000009dc00000, 0x000000009dc00000|100%| O|  |TAMS 0x000000009dc00000| PB 0x000000009dc00000| Untracked 
| 476|0x000000009dc00000, 0x000000009dd00000, 0x000000009dd00000|100%| O|  |TAMS 0x000000009dd00000| PB 0x000000009dd00000| Updating 
| 477|0x000000009dd00000, 0x000000009de00000, 0x000000009de00000|100%| O|  |TAMS 0x000000009de00000| PB 0x000000009de00000| Untracked 
| 478|0x000000009de00000, 0x000000009df00000, 0x000000009df00000|100%| O|  |TAMS 0x000000009df00000| PB 0x000000009df00000| Untracked 
| 479|0x000000009df00000, 0x000000009e000000, 0x000000009e000000|100%| O|  |TAMS 0x000000009e000000| PB 0x000000009e000000| Untracked 
| 480|0x000000009e000000, 0x000000009e100000, 0x000000009e100000|100%| O|  |TAMS 0x000000009e100000| PB 0x000000009e100000| Untracked 
| 481|0x000000009e100000, 0x000000009e200000, 0x000000009e200000|100%| O|  |TAMS 0x000000009e200000| PB 0x000000009e200000| Untracked 
| 482|0x000000009e200000, 0x000000009e300000, 0x000000009e300000|100%| O|  |TAMS 0x000000009e300000| PB 0x000000009e300000| Untracked 
| 483|0x000000009e300000, 0x000000009e400000, 0x000000009e400000|100%| O|  |TAMS 0x000000009e400000| PB 0x000000009e400000| Untracked 
| 484|0x000000009e400000, 0x000000009e500000, 0x000000009e500000|100%| O|  |TAMS 0x000000009e500000| PB 0x000000009e500000| Untracked 
| 485|0x000000009e500000, 0x000000009e600000, 0x000000009e600000|100%| O|  |TAMS 0x000000009e500000| PB 0x000000009e500000| Untracked 
| 486|0x000000009e600000, 0x000000009e700000, 0x000000009e700000|100%| O|  |TAMS 0x000000009e700000| PB 0x000000009e700000| Untracked 
| 487|0x000000009e700000, 0x000000009e800000, 0x000000009e800000|100%| O|  |TAMS 0x000000009e800000| PB 0x000000009e800000| Updating 
| 488|0x000000009e800000, 0x000000009e900000, 0x000000009e900000|100%|HS|  |TAMS 0x000000009e900000| PB 0x000000009e800000| Complete 
| 489|0x000000009e900000, 0x000000009ea00000, 0x000000009ea00000|100%|HC|  |TAMS 0x000000009ea00000| PB 0x000000009e900000| Complete 
| 490|0x000000009ea00000, 0x000000009eb00000, 0x000000009eb00000|100%|HC|  |TAMS 0x000000009eb00000| PB 0x000000009ea00000| Complete 
| 491|0x000000009eb00000, 0x000000009ec00000, 0x000000009ec00000|100%|HC|  |TAMS 0x000000009ec00000| PB 0x000000009eb00000| Complete 
| 492|0x000000009ec00000, 0x000000009ed00000, 0x000000009ed00000|100%| O|  |TAMS 0x000000009ed00000| PB 0x000000009ed00000| Untracked 
| 493|0x000000009ed00000, 0x000000009ee00000, 0x000000009ee00000|100%| O|  |TAMS 0x000000009ee00000| PB 0x000000009ee00000| Untracked 
| 494|0x000000009ee00000, 0x000000009ef00000, 0x000000009ef00000|100%| O|  |TAMS 0x000000009ef00000| PB 0x000000009ef00000| Untracked 
| 495|0x000000009ef00000, 0x000000009f000000, 0x000000009f000000|100%| O|  |TAMS 0x000000009f000000| PB 0x000000009f000000| Untracked 
| 496|0x000000009f000000, 0x000000009f100000, 0x000000009f100000|100%| O|  |TAMS 0x000000009f100000| PB 0x000000009f100000| Untracked 
| 497|0x000000009f100000, 0x000000009f200000, 0x000000009f200000|100%| O|  |TAMS 0x000000009f200000| PB 0x000000009f200000| Untracked 
| 498|0x000000009f200000, 0x000000009f300000, 0x000000009f300000|100%| O|  |TAMS 0x000000009f300000| PB 0x000000009f300000| Untracked 
| 499|0x000000009f300000, 0x000000009f400000, 0x000000009f400000|100%| O|  |TAMS 0x000000009f400000| PB 0x000000009f400000| Untracked 
| 500|0x000000009f400000, 0x000000009f500000, 0x000000009f500000|100%| O|  |TAMS 0x000000009f500000| PB 0x000000009f500000| Untracked 
| 501|0x000000009f500000, 0x000000009f600000, 0x000000009f600000|100%| O|  |TAMS 0x000000009f600000| PB 0x000000009f600000| Untracked 
| 502|0x000000009f600000, 0x000000009f700000, 0x000000009f700000|100%| O|  |TAMS 0x000000009f600000| PB 0x000000009f600000| Untracked 
| 503|0x000000009f700000, 0x000000009f800000, 0x000000009f800000|100%| O|  |TAMS 0x000000009f700000| PB 0x000000009f700000| Untracked 
| 504|0x000000009f800000, 0x000000009f866c60, 0x000000009f900000| 40%| O|  |TAMS 0x000000009f800000| PB 0x000000009f800000| Updating 
| 505|0x000000009f900000, 0x000000009fa00000, 0x000000009fa00000|100%|HS|  |TAMS 0x000000009f900000| PB 0x000000009f900000| Complete 
| 506|0x000000009fa00000, 0x000000009fb00000, 0x000000009fb00000|100%| O|  |TAMS 0x000000009fb00000| PB 0x000000009fb00000| Untracked 
| 507|0x000000009fb00000, 0x000000009fc00000, 0x000000009fc00000|100%| O|  |TAMS 0x000000009fc00000| PB 0x000000009fc00000| Untracked 
| 508|0x000000009fc00000, 0x000000009fd00000, 0x000000009fd00000|100%| O|  |TAMS 0x000000009fd00000| PB 0x000000009fd00000| Untracked 
| 509|0x000000009fd00000, 0x000000009fe00000, 0x000000009fe00000|100%| O|  |TAMS 0x000000009fe00000| PB 0x000000009fe00000| Untracked 
| 510|0x000000009fe00000, 0x000000009ff00000, 0x000000009ff00000|100%| O|  |TAMS 0x000000009ff00000| PB 0x000000009ff00000| Untracked 
| 511|0x000000009ff00000, 0x00000000a0000000, 0x00000000a0000000|100%| O|  |TAMS 0x00000000a0000000| PB 0x00000000a0000000| Untracked 
| 512|0x00000000a0000000, 0x00000000a0100000, 0x00000000a0100000|100%| O|  |TAMS 0x00000000a0100000| PB 0x00000000a0100000| Untracked 
| 513|0x00000000a0100000, 0x00000000a0200000, 0x00000000a0200000|100%| O|  |TAMS 0x00000000a0200000| PB 0x00000000a0200000| Untracked 
| 514|0x00000000a0200000, 0x00000000a0300000, 0x00000000a0300000|100%| O|  |TAMS 0x00000000a0300000| PB 0x00000000a0300000| Untracked 
| 515|0x00000000a0300000, 0x00000000a0400000, 0x00000000a0400000|100%| O|  |TAMS 0x00000000a0400000| PB 0x00000000a0400000| Untracked 
| 516|0x00000000a0400000, 0x00000000a0500000, 0x00000000a0500000|100%| O|  |TAMS 0x00000000a0500000| PB 0x00000000a0500000| Untracked 
| 517|0x00000000a0500000, 0x00000000a0600000, 0x00000000a0600000|100%| O|  |TAMS 0x00000000a0600000| PB 0x00000000a0600000| Untracked 
| 518|0x00000000a0600000, 0x00000000a0700000, 0x00000000a0700000|100%| O|  |TAMS 0x00000000a0700000| PB 0x00000000a0700000| Untracked 
| 519|0x00000000a0700000, 0x00000000a0800000, 0x00000000a0800000|100%| O|  |TAMS 0x00000000a0800000| PB 0x00000000a0800000| Untracked 
| 520|0x00000000a0800000, 0x00000000a0900000, 0x00000000a0900000|100%| O|  |TAMS 0x00000000a0900000| PB 0x00000000a0900000| Untracked 
| 521|0x00000000a0900000, 0x00000000a0a00000, 0x00000000a0a00000|100%| O|  |TAMS 0x00000000a0a00000| PB 0x00000000a0a00000| Untracked 
| 522|0x00000000a0a00000, 0x00000000a0b00000, 0x00000000a0b00000|100%| O|  |TAMS 0x00000000a0b00000| PB 0x00000000a0b00000| Untracked 
| 523|0x00000000a0b00000, 0x00000000a0c00000, 0x00000000a0c00000|100%| O|  |TAMS 0x00000000a0c00000| PB 0x00000000a0c00000| Untracked 
| 524|0x00000000a0c00000, 0x00000000a0d00000, 0x00000000a0d00000|100%| O|  |TAMS 0x00000000a0d00000| PB 0x00000000a0d00000| Updating 
| 525|0x00000000a0d00000, 0x00000000a0e00000, 0x00000000a0e00000|100%| O|  |TAMS 0x00000000a0e00000| PB 0x00000000a0e00000| Updating 
| 526|0x00000000a0e00000, 0x00000000a0f00000, 0x00000000a0f00000|100%| O|  |TAMS 0x00000000a0f00000| PB 0x00000000a0f00000| Updating 
| 527|0x00000000a0f00000, 0x00000000a1000000, 0x00000000a1000000|100%| O|  |TAMS 0x00000000a1000000| PB 0x00000000a1000000| Updating 
| 528|0x00000000a1000000, 0x00000000a1100000, 0x00000000a1100000|100%| O|  |TAMS 0x00000000a1100000| PB 0x00000000a1100000| Updating 
| 529|0x00000000a1100000, 0x00000000a1200000, 0x00000000a1200000|100%| O|  |TAMS 0x00000000a1200000| PB 0x00000000a1200000| Untracked 
| 530|0x00000000a1200000, 0x00000000a1300000, 0x00000000a1300000|100%| O|  |TAMS 0x00000000a1300000| PB 0x00000000a1300000| Updating 
| 531|0x00000000a1300000, 0x00000000a1300000, 0x00000000a1400000|  0%| F|  |TAMS 0x00000000a1300000| PB 0x00000000a1300000| Untracked 
| 532|0x00000000a1400000, 0x00000000a1400000, 0x00000000a1500000|  0%| F|  |TAMS 0x00000000a1400000| PB 0x00000000a1400000| Untracked 
| 533|0x00000000a1500000, 0x00000000a1500000, 0x00000000a1600000|  0%| F|  |TAMS 0x00000000a1500000| PB 0x00000000a1500000| Untracked 
| 534|0x00000000a1600000, 0x00000000a1600000, 0x00000000a1700000|  0%| F|  |TAMS 0x00000000a1600000| PB 0x00000000a1600000| Untracked 
| 535|0x00000000a1700000, 0x00000000a1700000, 0x00000000a1800000|  0%| F|  |TAMS 0x00000000a1700000| PB 0x00000000a1700000| Untracked 
| 536|0x00000000a1800000, 0x00000000a1900000, 0x00000000a1900000|100%| O|  |TAMS 0x00000000a1900000| PB 0x00000000a1900000| Untracked 
| 537|0x00000000a1900000, 0x00000000a1a00000, 0x00000000a1a00000|100%| O|  |TAMS 0x00000000a1a00000| PB 0x00000000a1a00000| Untracked 
| 538|0x00000000a1a00000, 0x00000000a1b00000, 0x00000000a1b00000|100%| O|  |TAMS 0x00000000a1b00000| PB 0x00000000a1b00000| Untracked 
| 539|0x00000000a1b00000, 0x00000000a1c00000, 0x00000000a1c00000|100%| O|  |TAMS 0x00000000a1c00000| PB 0x00000000a1c00000| Untracked 
| 540|0x00000000a1c00000, 0x00000000a1c00000, 0x00000000a1d00000|  0%| F|  |TAMS 0x00000000a1c00000| PB 0x00000000a1c00000| Untracked 
| 541|0x00000000a1d00000, 0x00000000a1d00000, 0x00000000a1e00000|  0%| F|  |TAMS 0x00000000a1d00000| PB 0x00000000a1d00000| Untracked 
| 542|0x00000000a1e00000, 0x00000000a1e00000, 0x00000000a1f00000|  0%| F|  |TAMS 0x00000000a1e00000| PB 0x00000000a1e00000| Untracked 
| 543|0x00000000a1f00000, 0x00000000a1f00000, 0x00000000a2000000|  0%| F|  |TAMS 0x00000000a1f00000| PB 0x00000000a1f00000| Untracked 
| 544|0x00000000a2000000, 0x00000000a2000000, 0x00000000a2100000|  0%| F|  |TAMS 0x00000000a2000000| PB 0x00000000a2000000| Untracked 
| 545|0x00000000a2100000, 0x00000000a2100000, 0x00000000a2200000|  0%| F|  |TAMS 0x00000000a2100000| PB 0x00000000a2100000| Untracked 
| 546|0x00000000a2200000, 0x00000000a2200000, 0x00000000a2300000|  0%| F|  |TAMS 0x00000000a2200000| PB 0x00000000a2200000| Untracked 
| 547|0x00000000a2300000, 0x00000000a2300000, 0x00000000a2400000|  0%| F|  |TAMS 0x00000000a2300000| PB 0x00000000a2300000| Untracked 
| 548|0x00000000a2400000, 0x00000000a2400000, 0x00000000a2500000|  0%| F|  |TAMS 0x00000000a2400000| PB 0x00000000a2400000| Untracked 
| 549|0x00000000a2500000, 0x00000000a2500000, 0x00000000a2600000|  0%| F|  |TAMS 0x00000000a2500000| PB 0x00000000a2500000| Untracked 
| 550|0x00000000a2600000, 0x00000000a2600000, 0x00000000a2700000|  0%| F|  |TAMS 0x00000000a2600000| PB 0x00000000a2600000| Untracked 
| 551|0x00000000a2700000, 0x00000000a2700000, 0x00000000a2800000|  0%| F|  |TAMS 0x00000000a2700000| PB 0x00000000a2700000| Untracked 
| 552|0x00000000a2800000, 0x00000000a2800000, 0x00000000a2900000|  0%| F|  |TAMS 0x00000000a2800000| PB 0x00000000a2800000| Untracked 
| 553|0x00000000a2900000, 0x00000000a2900000, 0x00000000a2a00000|  0%| F|  |TAMS 0x00000000a2900000| PB 0x00000000a2900000| Untracked 
| 554|0x00000000a2a00000, 0x00000000a2a00000, 0x00000000a2b00000|  0%| F|  |TAMS 0x00000000a2a00000| PB 0x00000000a2a00000| Untracked 
| 555|0x00000000a2b00000, 0x00000000a2b00000, 0x00000000a2c00000|  0%| F|  |TAMS 0x00000000a2b00000| PB 0x00000000a2b00000| Untracked 
| 556|0x00000000a2c00000, 0x00000000a2c00000, 0x00000000a2d00000|  0%| F|  |TAMS 0x00000000a2c00000| PB 0x00000000a2c00000| Untracked 
| 557|0x00000000a2d00000, 0x00000000a2d00000, 0x00000000a2e00000|  0%| F|  |TAMS 0x00000000a2d00000| PB 0x00000000a2d00000| Untracked 
| 558|0x00000000a2e00000, 0x00000000a2e00000, 0x00000000a2f00000|  0%| F|  |TAMS 0x00000000a2e00000| PB 0x00000000a2e00000| Untracked 
| 559|0x00000000a2f00000, 0x00000000a2f00000, 0x00000000a3000000|  0%| F|  |TAMS 0x00000000a2f00000| PB 0x00000000a2f00000| Untracked 
| 560|0x00000000a3000000, 0x00000000a3100000, 0x00000000a3100000|100%| O|  |TAMS 0x00000000a3100000| PB 0x00000000a3100000| Untracked 
| 561|0x00000000a3100000, 0x00000000a3200000, 0x00000000a3200000|100%| O|  |TAMS 0x00000000a3200000| PB 0x00000000a3200000| Untracked 
| 562|0x00000000a3200000, 0x00000000a3300000, 0x00000000a3300000|100%| O|  |TAMS 0x00000000a3300000| PB 0x00000000a3300000| Updating 
| 563|0x00000000a3300000, 0x00000000a3400000, 0x00000000a3400000|100%| O|  |TAMS 0x00000000a3400000| PB 0x00000000a3400000| Untracked 
| 564|0x00000000a3400000, 0x00000000a3500000, 0x00000000a3500000|100%| O|  |TAMS 0x00000000a3500000| PB 0x00000000a3500000| Updating 
| 565|0x00000000a3500000, 0x00000000a3600000, 0x00000000a3600000|100%| O|  |TAMS 0x00000000a3600000| PB 0x00000000a3600000| Untracked 
| 566|0x00000000a3600000, 0x00000000a3700000, 0x00000000a3700000|100%| O|  |TAMS 0x00000000a3700000| PB 0x00000000a3700000| Untracked 
| 567|0x00000000a3700000, 0x00000000a3800000, 0x00000000a3800000|100%| O|  |TAMS 0x00000000a3800000| PB 0x00000000a3800000| Untracked 
| 568|0x00000000a3800000, 0x00000000a3900000, 0x00000000a3900000|100%| O|  |TAMS 0x00000000a3900000| PB 0x00000000a3900000| Untracked 
| 569|0x00000000a3900000, 0x00000000a3a00000, 0x00000000a3a00000|100%| O|  |TAMS 0x00000000a3a00000| PB 0x00000000a3a00000| Updating 
| 570|0x00000000a3a00000, 0x00000000a3b00000, 0x00000000a3b00000|100%| O|  |TAMS 0x00000000a3b00000| PB 0x00000000a3b00000| Untracked 
| 571|0x00000000a3b00000, 0x00000000a3c00000, 0x00000000a3c00000|100%| O|  |TAMS 0x00000000a3c00000| PB 0x00000000a3c00000| Untracked 
| 572|0x00000000a3c00000, 0x00000000a3d00000, 0x00000000a3d00000|100%| O|  |TAMS 0x00000000a3d00000| PB 0x00000000a3d00000| Untracked 
| 573|0x00000000a3d00000, 0x00000000a3e00000, 0x00000000a3e00000|100%| O|  |TAMS 0x00000000a3e00000| PB 0x00000000a3e00000| Updating 
| 574|0x00000000a3e00000, 0x00000000a3f00000, 0x00000000a3f00000|100%| O|  |TAMS 0x00000000a3f00000| PB 0x00000000a3f00000| Updating 
| 575|0x00000000a3f00000, 0x00000000a3f00000, 0x00000000a4000000|  0%| F|  |TAMS 0x00000000a3f00000| PB 0x00000000a3f00000| Untracked 
| 576|0x00000000a4000000, 0x00000000a4000000, 0x00000000a4100000|  0%| F|  |TAMS 0x00000000a4000000| PB 0x00000000a4000000| Untracked 
| 577|0x00000000a4100000, 0x00000000a4100000, 0x00000000a4200000|  0%| F|  |TAMS 0x00000000a4100000| PB 0x00000000a4100000| Untracked 
| 578|0x00000000a4200000, 0x00000000a4200000, 0x00000000a4300000|  0%| F|  |TAMS 0x00000000a4200000| PB 0x00000000a4200000| Untracked 
| 579|0x00000000a4300000, 0x00000000a4400000, 0x00000000a4400000|100%| O|  |TAMS 0x00000000a4400000| PB 0x00000000a4400000| Untracked 
| 580|0x00000000a4400000, 0x00000000a4500000, 0x00000000a4500000|100%| O|  |TAMS 0x00000000a4500000| PB 0x00000000a4500000| Untracked 
| 581|0x00000000a4500000, 0x00000000a4600000, 0x00000000a4600000|100%| O|  |TAMS 0x00000000a4600000| PB 0x00000000a4600000| Untracked 
| 582|0x00000000a4600000, 0x00000000a4700000, 0x00000000a4700000|100%| O|  |TAMS 0x00000000a4700000| PB 0x00000000a4700000| Untracked 
| 583|0x00000000a4700000, 0x00000000a4800000, 0x00000000a4800000|100%| O|  |TAMS 0x00000000a4800000| PB 0x00000000a4800000| Untracked 
| 584|0x00000000a4800000, 0x00000000a4900000, 0x00000000a4900000|100%| O|  |TAMS 0x00000000a4900000| PB 0x00000000a4900000| Untracked 
| 585|0x00000000a4900000, 0x00000000a4a00000, 0x00000000a4a00000|100%|HS|  |TAMS 0x00000000a4a00000| PB 0x00000000a4900000| Complete 
| 586|0x00000000a4a00000, 0x00000000a4b00000, 0x00000000a4b00000|100%|HC|  |TAMS 0x00000000a4b00000| PB 0x00000000a4a00000| Complete 
| 587|0x00000000a4b00000, 0x00000000a4b00000, 0x00000000a4c00000|  0%| F|  |TAMS 0x00000000a4b00000| PB 0x00000000a4b00000| Untracked 
| 588|0x00000000a4c00000, 0x00000000a4c00000, 0x00000000a4d00000|  0%| F|  |TAMS 0x00000000a4c00000| PB 0x00000000a4c00000| Untracked 
| 589|0x00000000a4d00000, 0x00000000a4e00000, 0x00000000a4e00000|100%| O|  |TAMS 0x00000000a4e00000| PB 0x00000000a4e00000| Updating 
| 590|0x00000000a4e00000, 0x00000000a4f00000, 0x00000000a4f00000|100%| O|  |TAMS 0x00000000a4f00000| PB 0x00000000a4f00000| Untracked 
| 591|0x00000000a4f00000, 0x00000000a5000000, 0x00000000a5000000|100%| O|  |TAMS 0x00000000a5000000| PB 0x00000000a5000000| Untracked 
| 592|0x00000000a5000000, 0x00000000a5100000, 0x00000000a5100000|100%| O|  |TAMS 0x00000000a5100000| PB 0x00000000a5100000| Untracked 
| 593|0x00000000a5100000, 0x00000000a5200000, 0x00000000a5200000|100%| O|  |TAMS 0x00000000a5200000| PB 0x00000000a5200000| Untracked 
| 594|0x00000000a5200000, 0x00000000a5300000, 0x00000000a5300000|100%| O|  |TAMS 0x00000000a5300000| PB 0x00000000a5300000| Untracked 
| 595|0x00000000a5300000, 0x00000000a5400000, 0x00000000a5400000|100%| O|  |TAMS 0x00000000a5400000| PB 0x00000000a5400000| Untracked 
| 596|0x00000000a5400000, 0x00000000a5500000, 0x00000000a5500000|100%| O|  |TAMS 0x00000000a5500000| PB 0x00000000a5500000| Untracked 
| 597|0x00000000a5500000, 0x00000000a5600000, 0x00000000a5600000|100%| O|  |TAMS 0x00000000a5600000| PB 0x00000000a5600000| Untracked 
| 598|0x00000000a5600000, 0x00000000a5700000, 0x00000000a5700000|100%| O|  |TAMS 0x00000000a5700000| PB 0x00000000a5700000| Untracked 
| 599|0x00000000a5700000, 0x00000000a5800000, 0x00000000a5800000|100%| O|  |TAMS 0x00000000a5800000| PB 0x00000000a5800000| Untracked 
| 600|0x00000000a5800000, 0x00000000a5900000, 0x00000000a5900000|100%| O|  |TAMS 0x00000000a5900000| PB 0x00000000a5900000| Untracked 
| 601|0x00000000a5900000, 0x00000000a5a00000, 0x00000000a5a00000|100%| O|  |TAMS 0x00000000a5a00000| PB 0x00000000a5a00000| Untracked 
| 602|0x00000000a5a00000, 0x00000000a5b00000, 0x00000000a5b00000|100%| O|  |TAMS 0x00000000a5b00000| PB 0x00000000a5b00000| Untracked 
| 603|0x00000000a5b00000, 0x00000000a5c00000, 0x00000000a5c00000|100%| O|  |TAMS 0x00000000a5c00000| PB 0x00000000a5c00000| Updating 
| 604|0x00000000a5c00000, 0x00000000a5d00000, 0x00000000a5d00000|100%| O|  |TAMS 0x00000000a5d00000| PB 0x00000000a5d00000| Updating 
| 605|0x00000000a5d00000, 0x00000000a5e00000, 0x00000000a5e00000|100%| O|  |TAMS 0x00000000a5e00000| PB 0x00000000a5e00000| Untracked 
| 606|0x00000000a5e00000, 0x00000000a5f00000, 0x00000000a5f00000|100%| O|  |TAMS 0x00000000a5f00000| PB 0x00000000a5f00000| Untracked 
| 607|0x00000000a5f00000, 0x00000000a6000000, 0x00000000a6000000|100%| O|  |TAMS 0x00000000a6000000| PB 0x00000000a6000000| Updating 
| 608|0x00000000a6000000, 0x00000000a6100000, 0x00000000a6100000|100%| O|  |TAMS 0x00000000a6100000| PB 0x00000000a6100000| Untracked 
| 609|0x00000000a6100000, 0x00000000a6200000, 0x00000000a6200000|100%| O|  |TAMS 0x00000000a6200000| PB 0x00000000a6200000| Untracked 
| 610|0x00000000a6200000, 0x00000000a6300000, 0x00000000a6300000|100%| O|  |TAMS 0x00000000a6300000| PB 0x00000000a6300000| Untracked 
| 611|0x00000000a6300000, 0x00000000a6400000, 0x00000000a6400000|100%| O|  |TAMS 0x00000000a6400000| PB 0x00000000a6400000| Untracked 
| 612|0x00000000a6400000, 0x00000000a6500000, 0x00000000a6500000|100%| O|  |TAMS 0x00000000a6500000| PB 0x00000000a6500000| Updating 
| 613|0x00000000a6500000, 0x00000000a6600000, 0x00000000a6600000|100%| O|  |TAMS 0x00000000a6571ae0| PB 0x00000000a6571ae0| Updating 
| 614|0x00000000a6600000, 0x00000000a6600000, 0x00000000a6700000|  0%| F|  |TAMS 0x00000000a6600000| PB 0x00000000a6600000| Untracked 
| 615|0x00000000a6700000, 0x00000000a6700000, 0x00000000a6800000|  0%| F|  |TAMS 0x00000000a6700000| PB 0x00000000a6700000| Untracked 
| 616|0x00000000a6800000, 0x00000000a6800000, 0x00000000a6900000|  0%| F|  |TAMS 0x00000000a6800000| PB 0x00000000a6800000| Untracked 
| 617|0x00000000a6900000, 0x00000000a6900000, 0x00000000a6a00000|  0%| F|  |TAMS 0x00000000a6900000| PB 0x00000000a6900000| Untracked 
| 618|0x00000000a6a00000, 0x00000000a6a00000, 0x00000000a6b00000|  0%| F|  |TAMS 0x00000000a6a00000| PB 0x00000000a6a00000| Untracked 
| 619|0x00000000a6b00000, 0x00000000a6b00000, 0x00000000a6c00000|  0%| F|  |TAMS 0x00000000a6b00000| PB 0x00000000a6b00000| Untracked 
| 620|0x00000000a6c00000, 0x00000000a6c00000, 0x00000000a6d00000|  0%| F|  |TAMS 0x00000000a6c00000| PB 0x00000000a6c00000| Untracked 
| 621|0x00000000a6d00000, 0x00000000a6d00000, 0x00000000a6e00000|  0%| F|  |TAMS 0x00000000a6d00000| PB 0x00000000a6d00000| Untracked 
| 622|0x00000000a6e00000, 0x00000000a6e00000, 0x00000000a6f00000|  0%| F|  |TAMS 0x00000000a6e00000| PB 0x00000000a6e00000| Untracked 
| 623|0x00000000a6f00000, 0x00000000a6f00000, 0x00000000a7000000|  0%| F|  |TAMS 0x00000000a6f00000| PB 0x00000000a6f00000| Untracked 
| 624|0x00000000a7000000, 0x00000000a7000000, 0x00000000a7100000|  0%| F|  |TAMS 0x00000000a7000000| PB 0x00000000a7000000| Untracked 
| 625|0x00000000a7100000, 0x00000000a7100000, 0x00000000a7200000|  0%| F|  |TAMS 0x00000000a7100000| PB 0x00000000a7100000| Untracked 
| 626|0x00000000a7200000, 0x00000000a7200000, 0x00000000a7300000|  0%| F|  |TAMS 0x00000000a7200000| PB 0x00000000a7200000| Untracked 
| 627|0x00000000a7300000, 0x00000000a7300000, 0x00000000a7400000|  0%| F|  |TAMS 0x00000000a7300000| PB 0x00000000a7300000| Untracked 
| 628|0x00000000a7400000, 0x00000000a7400000, 0x00000000a7500000|  0%| F|  |TAMS 0x00000000a7400000| PB 0x00000000a7400000| Untracked 
| 629|0x00000000a7500000, 0x00000000a7500000, 0x00000000a7600000|  0%| F|  |TAMS 0x00000000a7500000| PB 0x00000000a7500000| Untracked 
| 630|0x00000000a7600000, 0x00000000a7600000, 0x00000000a7700000|  0%| F|  |TAMS 0x00000000a7600000| PB 0x00000000a7600000| Untracked 
| 631|0x00000000a7700000, 0x00000000a7800000, 0x00000000a7800000|100%| O|  |TAMS 0x00000000a7800000| PB 0x00000000a7800000| Untracked 
| 632|0x00000000a7800000, 0x00000000a7900000, 0x00000000a7900000|100%| O|  |TAMS 0x00000000a7900000| PB 0x00000000a7900000| Untracked 
| 633|0x00000000a7900000, 0x00000000a7a00000, 0x00000000a7a00000|100%| O|  |TAMS 0x00000000a7a00000| PB 0x00000000a7a00000| Untracked 
| 634|0x00000000a7a00000, 0x00000000a7b00000, 0x00000000a7b00000|100%| O|  |TAMS 0x00000000a7b00000| PB 0x00000000a7b00000| Untracked 
| 635|0x00000000a7b00000, 0x00000000a7c00000, 0x00000000a7c00000|100%| O|  |TAMS 0x00000000a7c00000| PB 0x00000000a7c00000| Untracked 
| 636|0x00000000a7c00000, 0x00000000a7d00000, 0x00000000a7d00000|100%| O|  |TAMS 0x00000000a7d00000| PB 0x00000000a7d00000| Untracked 
| 637|0x00000000a7d00000, 0x00000000a7e00000, 0x00000000a7e00000|100%| O|  |TAMS 0x00000000a7e00000| PB 0x00000000a7e00000| Untracked 
| 638|0x00000000a7e00000, 0x00000000a7f00000, 0x00000000a7f00000|100%| O|  |TAMS 0x00000000a7f00000| PB 0x00000000a7f00000| Untracked 
| 639|0x00000000a7f00000, 0x00000000a8000000, 0x00000000a8000000|100%| O|  |TAMS 0x00000000a8000000| PB 0x00000000a8000000| Untracked 
| 640|0x00000000a8000000, 0x00000000a8100000, 0x00000000a8100000|100%| O|  |TAMS 0x00000000a8100000| PB 0x00000000a8100000| Updating 
| 641|0x00000000a8100000, 0x00000000a8200000, 0x00000000a8200000|100%| O|  |TAMS 0x00000000a8200000| PB 0x00000000a8200000| Updating 
| 642|0x00000000a8200000, 0x00000000a8300000, 0x00000000a8300000|100%| O|  |TAMS 0x00000000a8300000| PB 0x00000000a8300000| Updating 
| 643|0x00000000a8300000, 0x00000000a8300000, 0x00000000a8400000|  0%| F|  |TAMS 0x00000000a8300000| PB 0x00000000a8300000| Untracked 
| 644|0x00000000a8400000, 0x00000000a8400000, 0x00000000a8500000|  0%| F|  |TAMS 0x00000000a8400000| PB 0x00000000a8400000| Untracked 
| 645|0x00000000a8500000, 0x00000000a8500000, 0x00000000a8600000|  0%| F|  |TAMS 0x00000000a8500000| PB 0x00000000a8500000| Untracked 
| 646|0x00000000a8600000, 0x00000000a8600000, 0x00000000a8700000|  0%| F|  |TAMS 0x00000000a8600000| PB 0x00000000a8600000| Untracked 
| 647|0x00000000a8700000, 0x00000000a8700000, 0x00000000a8800000|  0%| F|  |TAMS 0x00000000a8700000| PB 0x00000000a8700000| Untracked 
| 648|0x00000000a8800000, 0x00000000a8800000, 0x00000000a8900000|  0%| F|  |TAMS 0x00000000a8800000| PB 0x00000000a8800000| Untracked 
| 649|0x00000000a8900000, 0x00000000a8900000, 0x00000000a8a00000|  0%| F|  |TAMS 0x00000000a8900000| PB 0x00000000a8900000| Untracked 
| 650|0x00000000a8a00000, 0x00000000a8a00000, 0x00000000a8b00000|  0%| F|  |TAMS 0x00000000a8a00000| PB 0x00000000a8a00000| Untracked 
| 651|0x00000000a8b00000, 0x00000000a8b00000, 0x00000000a8c00000|  0%| F|  |TAMS 0x00000000a8b00000| PB 0x00000000a8b00000| Untracked 
| 652|0x00000000a8c00000, 0x00000000a8c00000, 0x00000000a8d00000|  0%| F|  |TAMS 0x00000000a8c00000| PB 0x00000000a8c00000| Untracked 
| 653|0x00000000a8d00000, 0x00000000a8d00000, 0x00000000a8e00000|  0%| F|  |TAMS 0x00000000a8d00000| PB 0x00000000a8d00000| Untracked 
| 654|0x00000000a8e00000, 0x00000000a8e00000, 0x00000000a8f00000|  0%| F|  |TAMS 0x00000000a8e00000| PB 0x00000000a8e00000| Untracked 
| 655|0x00000000a8f00000, 0x00000000a8f00000, 0x00000000a9000000|  0%| F|  |TAMS 0x00000000a8f00000| PB 0x00000000a8f00000| Untracked 
| 656|0x00000000a9000000, 0x00000000a9000000, 0x00000000a9100000|  0%| F|  |TAMS 0x00000000a9000000| PB 0x00000000a9000000| Untracked 
| 657|0x00000000a9100000, 0x00000000a9100000, 0x00000000a9200000|  0%| F|  |TAMS 0x00000000a9100000| PB 0x00000000a9100000| Untracked 
| 658|0x00000000a9200000, 0x00000000a9200000, 0x00000000a9300000|  0%| F|  |TAMS 0x00000000a9200000| PB 0x00000000a9200000| Untracked 
| 659|0x00000000a9300000, 0x00000000a9300000, 0x00000000a9400000|  0%| F|  |TAMS 0x00000000a9300000| PB 0x00000000a9300000| Untracked 
| 660|0x00000000a9400000, 0x00000000a9400000, 0x00000000a9500000|  0%| F|  |TAMS 0x00000000a9400000| PB 0x00000000a9400000| Untracked 
| 661|0x00000000a9500000, 0x00000000a9500000, 0x00000000a9600000|  0%| F|  |TAMS 0x00000000a9500000| PB 0x00000000a9500000| Untracked 
| 662|0x00000000a9600000, 0x00000000a9600000, 0x00000000a9700000|  0%| F|  |TAMS 0x00000000a9600000| PB 0x00000000a9600000| Untracked 
| 663|0x00000000a9700000, 0x00000000a9700000, 0x00000000a9800000|  0%| F|  |TAMS 0x00000000a9700000| PB 0x00000000a9700000| Untracked 
| 664|0x00000000a9800000, 0x00000000a9800000, 0x00000000a9900000|  0%| F|  |TAMS 0x00000000a9800000| PB 0x00000000a9800000| Untracked 
| 665|0x00000000a9900000, 0x00000000a9900000, 0x00000000a9a00000|  0%| F|  |TAMS 0x00000000a9900000| PB 0x00000000a9900000| Untracked 
| 666|0x00000000a9a00000, 0x00000000a9a00000, 0x00000000a9b00000|  0%| F|  |TAMS 0x00000000a9a00000| PB 0x00000000a9a00000| Untracked 
| 667|0x00000000a9b00000, 0x00000000a9b00000, 0x00000000a9c00000|  0%| F|  |TAMS 0x00000000a9b00000| PB 0x00000000a9b00000| Untracked 
| 668|0x00000000a9c00000, 0x00000000a9c00000, 0x00000000a9d00000|  0%| F|  |TAMS 0x00000000a9c00000| PB 0x00000000a9c00000| Untracked 
| 669|0x00000000a9d00000, 0x00000000a9d00000, 0x00000000a9e00000|  0%| F|  |TAMS 0x00000000a9d00000| PB 0x00000000a9d00000| Untracked 
| 670|0x00000000a9e00000, 0x00000000a9e00000, 0x00000000a9f00000|  0%| F|  |TAMS 0x00000000a9e00000| PB 0x00000000a9e00000| Untracked 
| 671|0x00000000a9f00000, 0x00000000a9f00000, 0x00000000aa000000|  0%| F|  |TAMS 0x00000000a9f00000| PB 0x00000000a9f00000| Untracked 
| 672|0x00000000aa000000, 0x00000000aa000000, 0x00000000aa100000|  0%| F|  |TAMS 0x00000000aa000000| PB 0x00000000aa000000| Untracked 
| 673|0x00000000aa100000, 0x00000000aa100000, 0x00000000aa200000|  0%| F|  |TAMS 0x00000000aa100000| PB 0x00000000aa100000| Untracked 
| 674|0x00000000aa200000, 0x00000000aa200000, 0x00000000aa300000|  0%| F|  |TAMS 0x00000000aa200000| PB 0x00000000aa200000| Untracked 
| 675|0x00000000aa300000, 0x00000000aa300000, 0x00000000aa400000|  0%| F|  |TAMS 0x00000000aa300000| PB 0x00000000aa300000| Untracked 
| 676|0x00000000aa400000, 0x00000000aa400000, 0x00000000aa500000|  0%| F|  |TAMS 0x00000000aa400000| PB 0x00000000aa400000| Untracked 
| 677|0x00000000aa500000, 0x00000000aa500000, 0x00000000aa600000|  0%| F|  |TAMS 0x00000000aa500000| PB 0x00000000aa500000| Untracked 
| 678|0x00000000aa600000, 0x00000000aa600000, 0x00000000aa700000|  0%| F|  |TAMS 0x00000000aa600000| PB 0x00000000aa600000| Untracked 
| 679|0x00000000aa700000, 0x00000000aa700000, 0x00000000aa800000|  0%| F|  |TAMS 0x00000000aa700000| PB 0x00000000aa700000| Untracked 
| 680|0x00000000aa800000, 0x00000000aa800000, 0x00000000aa900000|  0%| F|  |TAMS 0x00000000aa800000| PB 0x00000000aa800000| Untracked 
| 681|0x00000000aa900000, 0x00000000aa900000, 0x00000000aaa00000|  0%| F|  |TAMS 0x00000000aa900000| PB 0x00000000aa900000| Untracked 
| 682|0x00000000aaa00000, 0x00000000aaa00000, 0x00000000aab00000|  0%| F|  |TAMS 0x00000000aaa00000| PB 0x00000000aaa00000| Untracked 
| 683|0x00000000aab00000, 0x00000000aab00000, 0x00000000aac00000|  0%| F|  |TAMS 0x00000000aab00000| PB 0x00000000aab00000| Untracked 
| 684|0x00000000aac00000, 0x00000000aac00000, 0x00000000aad00000|  0%| F|  |TAMS 0x00000000aac00000| PB 0x00000000aac00000| Untracked 
| 685|0x00000000aad00000, 0x00000000aad00000, 0x00000000aae00000|  0%| F|  |TAMS 0x00000000aad00000| PB 0x00000000aad00000| Untracked 
| 686|0x00000000aae00000, 0x00000000aae00000, 0x00000000aaf00000|  0%| F|  |TAMS 0x00000000aae00000| PB 0x00000000aae00000| Untracked 
| 687|0x00000000aaf00000, 0x00000000aaf00000, 0x00000000ab000000|  0%| F|  |TAMS 0x00000000aaf00000| PB 0x00000000aaf00000| Untracked 
| 688|0x00000000ab000000, 0x00000000ab000000, 0x00000000ab100000|  0%| F|  |TAMS 0x00000000ab000000| PB 0x00000000ab000000| Untracked 
| 689|0x00000000ab100000, 0x00000000ab100000, 0x00000000ab200000|  0%| F|  |TAMS 0x00000000ab100000| PB 0x00000000ab100000| Untracked 
| 690|0x00000000ab200000, 0x00000000ab200000, 0x00000000ab300000|  0%| F|  |TAMS 0x00000000ab200000| PB 0x00000000ab200000| Untracked 
| 691|0x00000000ab300000, 0x00000000ab300000, 0x00000000ab400000|  0%| F|  |TAMS 0x00000000ab300000| PB 0x00000000ab300000| Untracked 
| 692|0x00000000ab400000, 0x00000000ab400000, 0x00000000ab500000|  0%| F|  |TAMS 0x00000000ab400000| PB 0x00000000ab400000| Untracked 
| 693|0x00000000ab500000, 0x00000000ab500000, 0x00000000ab600000|  0%| F|  |TAMS 0x00000000ab500000| PB 0x00000000ab500000| Untracked 
| 694|0x00000000ab600000, 0x00000000ab600000, 0x00000000ab700000|  0%| F|  |TAMS 0x00000000ab600000| PB 0x00000000ab600000| Untracked 
| 695|0x00000000ab700000, 0x00000000ab700000, 0x00000000ab800000|  0%| F|  |TAMS 0x00000000ab700000| PB 0x00000000ab700000| Untracked 
| 696|0x00000000ab800000, 0x00000000ab800000, 0x00000000ab900000|  0%| F|  |TAMS 0x00000000ab800000| PB 0x00000000ab800000| Untracked 
| 697|0x00000000ab900000, 0x00000000ab900000, 0x00000000aba00000|  0%| F|  |TAMS 0x00000000ab900000| PB 0x00000000ab900000| Untracked 
| 698|0x00000000aba00000, 0x00000000aba00000, 0x00000000abb00000|  0%| F|  |TAMS 0x00000000aba00000| PB 0x00000000aba00000| Untracked 
| 699|0x00000000abb00000, 0x00000000abb00000, 0x00000000abc00000|  0%| F|  |TAMS 0x00000000abb00000| PB 0x00000000abb00000| Untracked 
| 700|0x00000000abc00000, 0x00000000abc00000, 0x00000000abd00000|  0%| F|  |TAMS 0x00000000abc00000| PB 0x00000000abc00000| Untracked 
| 701|0x00000000abd00000, 0x00000000abd00000, 0x00000000abe00000|  0%| F|  |TAMS 0x00000000abd00000| PB 0x00000000abd00000| Untracked 
| 702|0x00000000abe00000, 0x00000000abe00000, 0x00000000abf00000|  0%| F|  |TAMS 0x00000000abe00000| PB 0x00000000abe00000| Untracked 
| 703|0x00000000abf00000, 0x00000000abf00000, 0x00000000ac000000|  0%| F|  |TAMS 0x00000000abf00000| PB 0x00000000abf00000| Untracked 
| 704|0x00000000ac000000, 0x00000000ac000000, 0x00000000ac100000|  0%| F|  |TAMS 0x00000000ac000000| PB 0x00000000ac000000| Untracked 
| 705|0x00000000ac100000, 0x00000000ac100000, 0x00000000ac200000|  0%| F|  |TAMS 0x00000000ac100000| PB 0x00000000ac100000| Untracked 
| 706|0x00000000ac200000, 0x00000000ac200000, 0x00000000ac300000|  0%| F|  |TAMS 0x00000000ac200000| PB 0x00000000ac200000| Untracked 
| 707|0x00000000ac300000, 0x00000000ac300000, 0x00000000ac400000|  0%| F|  |TAMS 0x00000000ac300000| PB 0x00000000ac300000| Untracked 
| 708|0x00000000ac400000, 0x00000000ac400000, 0x00000000ac500000|  0%| F|  |TAMS 0x00000000ac400000| PB 0x00000000ac400000| Untracked 
| 709|0x00000000ac500000, 0x00000000ac500000, 0x00000000ac600000|  0%| F|  |TAMS 0x00000000ac500000| PB 0x00000000ac500000| Untracked 
| 710|0x00000000ac600000, 0x00000000ac600000, 0x00000000ac700000|  0%| F|  |TAMS 0x00000000ac600000| PB 0x00000000ac600000| Untracked 
| 711|0x00000000ac700000, 0x00000000ac700000, 0x00000000ac800000|  0%| F|  |TAMS 0x00000000ac700000| PB 0x00000000ac700000| Untracked 
| 712|0x00000000ac800000, 0x00000000ac800000, 0x00000000ac900000|  0%| F|  |TAMS 0x00000000ac800000| PB 0x00000000ac800000| Untracked 
| 713|0x00000000ac900000, 0x00000000ac900000, 0x00000000aca00000|  0%| F|  |TAMS 0x00000000ac900000| PB 0x00000000ac900000| Untracked 
| 714|0x00000000aca00000, 0x00000000aca00000, 0x00000000acb00000|  0%| F|  |TAMS 0x00000000aca00000| PB 0x00000000aca00000| Untracked 
| 715|0x00000000acb00000, 0x00000000acb00000, 0x00000000acc00000|  0%| F|  |TAMS 0x00000000acb00000| PB 0x00000000acb00000| Untracked 
| 716|0x00000000acc00000, 0x00000000acc00000, 0x00000000acd00000|  0%| F|  |TAMS 0x00000000acc00000| PB 0x00000000acc00000| Untracked 
| 717|0x00000000acd00000, 0x00000000acd00000, 0x00000000ace00000|  0%| F|  |TAMS 0x00000000acd00000| PB 0x00000000acd00000| Untracked 
| 718|0x00000000ace00000, 0x00000000ace00000, 0x00000000acf00000|  0%| F|  |TAMS 0x00000000ace00000| PB 0x00000000ace00000| Untracked 
| 719|0x00000000acf00000, 0x00000000acf00000, 0x00000000ad000000|  0%| F|  |TAMS 0x00000000acf00000| PB 0x00000000acf00000| Untracked 
| 720|0x00000000ad000000, 0x00000000ad000000, 0x00000000ad100000|  0%| F|  |TAMS 0x00000000ad000000| PB 0x00000000ad000000| Untracked 
| 721|0x00000000ad100000, 0x00000000ad100000, 0x00000000ad200000|  0%| F|  |TAMS 0x00000000ad100000| PB 0x00000000ad100000| Untracked 
| 722|0x00000000ad200000, 0x00000000ad200000, 0x00000000ad300000|  0%| F|  |TAMS 0x00000000ad200000| PB 0x00000000ad200000| Untracked 
| 723|0x00000000ad300000, 0x00000000ad300000, 0x00000000ad400000|  0%| F|  |TAMS 0x00000000ad300000| PB 0x00000000ad300000| Untracked 
| 724|0x00000000ad400000, 0x00000000ad400000, 0x00000000ad500000|  0%| F|  |TAMS 0x00000000ad400000| PB 0x00000000ad400000| Untracked 
| 725|0x00000000ad500000, 0x00000000ad500000, 0x00000000ad600000|  0%| F|  |TAMS 0x00000000ad500000| PB 0x00000000ad500000| Untracked 
| 726|0x00000000ad600000, 0x00000000ad600000, 0x00000000ad700000|  0%| F|  |TAMS 0x00000000ad600000| PB 0x00000000ad600000| Untracked 
| 727|0x00000000ad700000, 0x00000000ad700000, 0x00000000ad800000|  0%| F|  |TAMS 0x00000000ad700000| PB 0x00000000ad700000| Untracked 
| 728|0x00000000ad800000, 0x00000000ad800000, 0x00000000ad900000|  0%| F|  |TAMS 0x00000000ad800000| PB 0x00000000ad800000| Untracked 
| 729|0x00000000ad900000, 0x00000000ad900000, 0x00000000ada00000|  0%| F|  |TAMS 0x00000000ad900000| PB 0x00000000ad900000| Untracked 
| 730|0x00000000ada00000, 0x00000000ada00000, 0x00000000adb00000|  0%| F|  |TAMS 0x00000000ada00000| PB 0x00000000ada00000| Untracked 
| 731|0x00000000adb00000, 0x00000000adb00000, 0x00000000adc00000|  0%| F|  |TAMS 0x00000000adb00000| PB 0x00000000adb00000| Untracked 
| 732|0x00000000adc00000, 0x00000000adc00000, 0x00000000add00000|  0%| F|  |TAMS 0x00000000adc00000| PB 0x00000000adc00000| Untracked 
| 733|0x00000000add00000, 0x00000000add00000, 0x00000000ade00000|  0%| F|  |TAMS 0x00000000add00000| PB 0x00000000add00000| Untracked 
| 734|0x00000000ade00000, 0x00000000ade00000, 0x00000000adf00000|  0%| F|  |TAMS 0x00000000ade00000| PB 0x00000000ade00000| Untracked 
| 735|0x00000000adf00000, 0x00000000adf00000, 0x00000000ae000000|  0%| F|  |TAMS 0x00000000adf00000| PB 0x00000000adf00000| Untracked 
| 736|0x00000000ae000000, 0x00000000ae000000, 0x00000000ae100000|  0%| F|  |TAMS 0x00000000ae000000| PB 0x00000000ae000000| Untracked 
| 737|0x00000000ae100000, 0x00000000ae100000, 0x00000000ae200000|  0%| F|  |TAMS 0x00000000ae100000| PB 0x00000000ae100000| Untracked 
| 738|0x00000000ae200000, 0x00000000ae200000, 0x00000000ae300000|  0%| F|  |TAMS 0x00000000ae200000| PB 0x00000000ae200000| Untracked 
| 739|0x00000000ae300000, 0x00000000ae300000, 0x00000000ae400000|  0%| F|  |TAMS 0x00000000ae300000| PB 0x00000000ae300000| Untracked 
| 740|0x00000000ae400000, 0x00000000ae400000, 0x00000000ae500000|  0%| F|  |TAMS 0x00000000ae400000| PB 0x00000000ae400000| Untracked 
| 741|0x00000000ae500000, 0x00000000ae500000, 0x00000000ae600000|  0%| F|  |TAMS 0x00000000ae500000| PB 0x00000000ae500000| Untracked 
| 742|0x00000000ae600000, 0x00000000ae600000, 0x00000000ae700000|  0%| F|  |TAMS 0x00000000ae600000| PB 0x00000000ae600000| Untracked 
| 743|0x00000000ae700000, 0x00000000ae700000, 0x00000000ae800000|  0%| F|  |TAMS 0x00000000ae700000| PB 0x00000000ae700000| Untracked 
| 744|0x00000000ae800000, 0x00000000ae800000, 0x00000000ae900000|  0%| F|  |TAMS 0x00000000ae800000| PB 0x00000000ae800000| Untracked 
| 745|0x00000000ae900000, 0x00000000ae900000, 0x00000000aea00000|  0%| F|  |TAMS 0x00000000ae900000| PB 0x00000000ae900000| Untracked 
| 746|0x00000000aea00000, 0x00000000aea00000, 0x00000000aeb00000|  0%| F|  |TAMS 0x00000000aea00000| PB 0x00000000aea00000| Untracked 
| 747|0x00000000aeb00000, 0x00000000aeb00000, 0x00000000aec00000|  0%| F|  |TAMS 0x00000000aeb00000| PB 0x00000000aeb00000| Untracked 
| 748|0x00000000aec00000, 0x00000000aec00000, 0x00000000aed00000|  0%| F|  |TAMS 0x00000000aec00000| PB 0x00000000aec00000| Untracked 
| 749|0x00000000aed00000, 0x00000000aed00000, 0x00000000aee00000|  0%| F|  |TAMS 0x00000000aed00000| PB 0x00000000aed00000| Untracked 
| 750|0x00000000aee00000, 0x00000000aee00000, 0x00000000aef00000|  0%| F|  |TAMS 0x00000000aee00000| PB 0x00000000aee00000| Untracked 
| 751|0x00000000aef00000, 0x00000000aef00000, 0x00000000af000000|  0%| F|  |TAMS 0x00000000aef00000| PB 0x00000000aef00000| Untracked 
| 752|0x00000000af000000, 0x00000000af000000, 0x00000000af100000|  0%| F|  |TAMS 0x00000000af000000| PB 0x00000000af000000| Untracked 
| 753|0x00000000af100000, 0x00000000af100000, 0x00000000af200000|  0%| F|  |TAMS 0x00000000af100000| PB 0x00000000af100000| Untracked 
| 754|0x00000000af200000, 0x00000000af200000, 0x00000000af300000|  0%| F|  |TAMS 0x00000000af200000| PB 0x00000000af200000| Untracked 
| 755|0x00000000af300000, 0x00000000af300000, 0x00000000af400000|  0%| F|  |TAMS 0x00000000af300000| PB 0x00000000af300000| Untracked 
| 756|0x00000000af400000, 0x00000000af400000, 0x00000000af500000|  0%| F|  |TAMS 0x00000000af400000| PB 0x00000000af400000| Untracked 
| 757|0x00000000af500000, 0x00000000af500000, 0x00000000af600000|  0%| F|  |TAMS 0x00000000af500000| PB 0x00000000af500000| Untracked 
| 758|0x00000000af600000, 0x00000000af600000, 0x00000000af700000|  0%| F|  |TAMS 0x00000000af600000| PB 0x00000000af600000| Untracked 
| 759|0x00000000af700000, 0x00000000af700000, 0x00000000af800000|  0%| F|  |TAMS 0x00000000af700000| PB 0x00000000af700000| Untracked 
| 760|0x00000000af800000, 0x00000000af800000, 0x00000000af900000|  0%| F|  |TAMS 0x00000000af800000| PB 0x00000000af800000| Untracked 
| 761|0x00000000af900000, 0x00000000af900000, 0x00000000afa00000|  0%| F|  |TAMS 0x00000000af900000| PB 0x00000000af900000| Untracked 
| 762|0x00000000afa00000, 0x00000000afa00000, 0x00000000afb00000|  0%| F|  |TAMS 0x00000000afa00000| PB 0x00000000afa00000| Untracked 
| 763|0x00000000afb00000, 0x00000000afb00000, 0x00000000afc00000|  0%| F|  |TAMS 0x00000000afb00000| PB 0x00000000afb00000| Untracked 
| 764|0x00000000afc00000, 0x00000000afc00000, 0x00000000afd00000|  0%| F|  |TAMS 0x00000000afc00000| PB 0x00000000afc00000| Untracked 
| 765|0x00000000afd00000, 0x00000000afd00000, 0x00000000afe00000|  0%| F|  |TAMS 0x00000000afd00000| PB 0x00000000afd00000| Untracked 
| 766|0x00000000afe00000, 0x00000000afe00000, 0x00000000aff00000|  0%| F|  |TAMS 0x00000000afe00000| PB 0x00000000afe00000| Untracked 
| 767|0x00000000aff00000, 0x00000000aff00000, 0x00000000b0000000|  0%| F|  |TAMS 0x00000000aff00000| PB 0x00000000aff00000| Untracked 
| 768|0x00000000b0000000, 0x00000000b0000000, 0x00000000b0100000|  0%| F|  |TAMS 0x00000000b0000000| PB 0x00000000b0000000| Untracked 
| 769|0x00000000b0100000, 0x00000000b0100000, 0x00000000b0200000|  0%| F|  |TAMS 0x00000000b0100000| PB 0x00000000b0100000| Untracked 
| 770|0x00000000b0200000, 0x00000000b0200000, 0x00000000b0300000|  0%| F|  |TAMS 0x00000000b0200000| PB 0x00000000b0200000| Untracked 
| 771|0x00000000b0300000, 0x00000000b0300000, 0x00000000b0400000|  0%| F|  |TAMS 0x00000000b0300000| PB 0x00000000b0300000| Untracked 
| 772|0x00000000b0400000, 0x00000000b0400000, 0x00000000b0500000|  0%| F|  |TAMS 0x00000000b0400000| PB 0x00000000b0400000| Untracked 
| 773|0x00000000b0500000, 0x00000000b0500000, 0x00000000b0600000|  0%| F|  |TAMS 0x00000000b0500000| PB 0x00000000b0500000| Untracked 
| 774|0x00000000b0600000, 0x00000000b0600000, 0x00000000b0700000|  0%| F|  |TAMS 0x00000000b0600000| PB 0x00000000b0600000| Untracked 
| 775|0x00000000b0700000, 0x00000000b0700000, 0x00000000b0800000|  0%| F|  |TAMS 0x00000000b0700000| PB 0x00000000b0700000| Untracked 
| 776|0x00000000b0800000, 0x00000000b0800000, 0x00000000b0900000|  0%| F|  |TAMS 0x00000000b0800000| PB 0x00000000b0800000| Untracked 
| 777|0x00000000b0900000, 0x00000000b0900000, 0x00000000b0a00000|  0%| F|  |TAMS 0x00000000b0900000| PB 0x00000000b0900000| Untracked 
| 778|0x00000000b0a00000, 0x00000000b0a00000, 0x00000000b0b00000|  0%| F|  |TAMS 0x00000000b0a00000| PB 0x00000000b0a00000| Untracked 
| 779|0x00000000b0b00000, 0x00000000b0b00000, 0x00000000b0c00000|  0%| F|  |TAMS 0x00000000b0b00000| PB 0x00000000b0b00000| Untracked 
| 780|0x00000000b0c00000, 0x00000000b0c00000, 0x00000000b0d00000|  0%| F|  |TAMS 0x00000000b0c00000| PB 0x00000000b0c00000| Untracked 
| 781|0x00000000b0d00000, 0x00000000b0d00000, 0x00000000b0e00000|  0%| F|  |TAMS 0x00000000b0d00000| PB 0x00000000b0d00000| Untracked 
| 782|0x00000000b0e00000, 0x00000000b0e00000, 0x00000000b0f00000|  0%| F|  |TAMS 0x00000000b0e00000| PB 0x00000000b0e00000| Untracked 
| 783|0x00000000b0f00000, 0x00000000b0f00000, 0x00000000b1000000|  0%| F|  |TAMS 0x00000000b0f00000| PB 0x00000000b0f00000| Untracked 
| 784|0x00000000b1000000, 0x00000000b1000000, 0x00000000b1100000|  0%| F|  |TAMS 0x00000000b1000000| PB 0x00000000b1000000| Untracked 
| 785|0x00000000b1100000, 0x00000000b1100000, 0x00000000b1200000|  0%| F|  |TAMS 0x00000000b1100000| PB 0x00000000b1100000| Untracked 
| 786|0x00000000b1200000, 0x00000000b1200000, 0x00000000b1300000|  0%| F|  |TAMS 0x00000000b1200000| PB 0x00000000b1200000| Untracked 
| 787|0x00000000b1300000, 0x00000000b1300000, 0x00000000b1400000|  0%| F|  |TAMS 0x00000000b1300000| PB 0x00000000b1300000| Untracked 
| 788|0x00000000b1400000, 0x00000000b1400000, 0x00000000b1500000|  0%| F|  |TAMS 0x00000000b1400000| PB 0x00000000b1400000| Untracked 
| 789|0x00000000b1500000, 0x00000000b1500000, 0x00000000b1600000|  0%| F|  |TAMS 0x00000000b1500000| PB 0x00000000b1500000| Untracked 
| 790|0x00000000b1600000, 0x00000000b1600000, 0x00000000b1700000|  0%| F|  |TAMS 0x00000000b1600000| PB 0x00000000b1600000| Untracked 
| 791|0x00000000b1700000, 0x00000000b1700000, 0x00000000b1800000|  0%| F|  |TAMS 0x00000000b1700000| PB 0x00000000b1700000| Untracked 
| 792|0x00000000b1800000, 0x00000000b1800000, 0x00000000b1900000|  0%| F|  |TAMS 0x00000000b1800000| PB 0x00000000b1800000| Untracked 
| 793|0x00000000b1900000, 0x00000000b1900000, 0x00000000b1a00000|  0%| F|  |TAMS 0x00000000b1900000| PB 0x00000000b1900000| Untracked 
| 794|0x00000000b1a00000, 0x00000000b1a00000, 0x00000000b1b00000|  0%| F|  |TAMS 0x00000000b1a00000| PB 0x00000000b1a00000| Untracked 
| 795|0x00000000b1b00000, 0x00000000b1b00000, 0x00000000b1c00000|  0%| F|  |TAMS 0x00000000b1b00000| PB 0x00000000b1b00000| Untracked 
| 796|0x00000000b1c00000, 0x00000000b1c00000, 0x00000000b1d00000|  0%| F|  |TAMS 0x00000000b1c00000| PB 0x00000000b1c00000| Untracked 
| 797|0x00000000b1d00000, 0x00000000b1d00000, 0x00000000b1e00000|  0%| F|  |TAMS 0x00000000b1d00000| PB 0x00000000b1d00000| Untracked 
| 798|0x00000000b1e00000, 0x00000000b1e00000, 0x00000000b1f00000|  0%| F|  |TAMS 0x00000000b1e00000| PB 0x00000000b1e00000| Untracked 
| 799|0x00000000b1f00000, 0x00000000b1f00000, 0x00000000b2000000|  0%| F|  |TAMS 0x00000000b1f00000| PB 0x00000000b1f00000| Untracked 
| 800|0x00000000b2000000, 0x00000000b2000000, 0x00000000b2100000|  0%| F|  |TAMS 0x00000000b2000000| PB 0x00000000b2000000| Untracked 
| 801|0x00000000b2100000, 0x00000000b2100000, 0x00000000b2200000|  0%| F|  |TAMS 0x00000000b2100000| PB 0x00000000b2100000| Untracked 
| 802|0x00000000b2200000, 0x00000000b2200000, 0x00000000b2300000|  0%| F|  |TAMS 0x00000000b2200000| PB 0x00000000b2200000| Untracked 
| 803|0x00000000b2300000, 0x00000000b2300000, 0x00000000b2400000|  0%| F|  |TAMS 0x00000000b2300000| PB 0x00000000b2300000| Untracked 
| 804|0x00000000b2400000, 0x00000000b2400000, 0x00000000b2500000|  0%| F|  |TAMS 0x00000000b2400000| PB 0x00000000b2400000| Untracked 
| 805|0x00000000b2500000, 0x00000000b2500000, 0x00000000b2600000|  0%| F|  |TAMS 0x00000000b2500000| PB 0x00000000b2500000| Untracked 
| 806|0x00000000b2600000, 0x00000000b2600000, 0x00000000b2700000|  0%| F|  |TAMS 0x00000000b2600000| PB 0x00000000b2600000| Untracked 
| 807|0x00000000b2700000, 0x00000000b2700000, 0x00000000b2800000|  0%| F|  |TAMS 0x00000000b2700000| PB 0x00000000b2700000| Untracked 
| 808|0x00000000b2800000, 0x00000000b2800000, 0x00000000b2900000|  0%| F|  |TAMS 0x00000000b2800000| PB 0x00000000b2800000| Untracked 
| 809|0x00000000b2900000, 0x00000000b2900000, 0x00000000b2a00000|  0%| F|  |TAMS 0x00000000b2900000| PB 0x00000000b2900000| Untracked 
| 810|0x00000000b2a00000, 0x00000000b2a00000, 0x00000000b2b00000|  0%| F|  |TAMS 0x00000000b2a00000| PB 0x00000000b2a00000| Untracked 
| 811|0x00000000b2b00000, 0x00000000b2b00000, 0x00000000b2c00000|  0%| F|  |TAMS 0x00000000b2b00000| PB 0x00000000b2b00000| Untracked 
| 812|0x00000000b2c00000, 0x00000000b2c00000, 0x00000000b2d00000|  0%| F|  |TAMS 0x00000000b2c00000| PB 0x00000000b2c00000| Untracked 
| 813|0x00000000b2d00000, 0x00000000b2d00000, 0x00000000b2e00000|  0%| F|  |TAMS 0x00000000b2d00000| PB 0x00000000b2d00000| Untracked 
| 814|0x00000000b2e00000, 0x00000000b2e00000, 0x00000000b2f00000|  0%| F|  |TAMS 0x00000000b2e00000| PB 0x00000000b2e00000| Untracked 
| 815|0x00000000b2f00000, 0x00000000b2f00000, 0x00000000b3000000|  0%| F|  |TAMS 0x00000000b2f00000| PB 0x00000000b2f00000| Untracked 
| 816|0x00000000b3000000, 0x00000000b3000000, 0x00000000b3100000|  0%| F|  |TAMS 0x00000000b3000000| PB 0x00000000b3000000| Untracked 
| 817|0x00000000b3100000, 0x00000000b3100000, 0x00000000b3200000|  0%| F|  |TAMS 0x00000000b3100000| PB 0x00000000b3100000| Untracked 
| 818|0x00000000b3200000, 0x00000000b3200000, 0x00000000b3300000|  0%| F|  |TAMS 0x00000000b3200000| PB 0x00000000b3200000| Untracked 
| 819|0x00000000b3300000, 0x00000000b3300000, 0x00000000b3400000|  0%| F|  |TAMS 0x00000000b3300000| PB 0x00000000b3300000| Untracked 
| 820|0x00000000b3400000, 0x00000000b3400000, 0x00000000b3500000|  0%| F|  |TAMS 0x00000000b3400000| PB 0x00000000b3400000| Untracked 
| 821|0x00000000b3500000, 0x00000000b3500000, 0x00000000b3600000|  0%| F|  |TAMS 0x00000000b3500000| PB 0x00000000b3500000| Untracked 
| 822|0x00000000b3600000, 0x00000000b3600000, 0x00000000b3700000|  0%| F|  |TAMS 0x00000000b3600000| PB 0x00000000b3600000| Untracked 
| 823|0x00000000b3700000, 0x00000000b3700000, 0x00000000b3800000|  0%| F|  |TAMS 0x00000000b3700000| PB 0x00000000b3700000| Untracked 
| 824|0x00000000b3800000, 0x00000000b3800000, 0x00000000b3900000|  0%| F|  |TAMS 0x00000000b3800000| PB 0x00000000b3800000| Untracked 
| 825|0x00000000b3900000, 0x00000000b3900000, 0x00000000b3a00000|  0%| F|  |TAMS 0x00000000b3900000| PB 0x00000000b3900000| Untracked 
| 826|0x00000000b3a00000, 0x00000000b3a00000, 0x00000000b3b00000|  0%| F|  |TAMS 0x00000000b3a00000| PB 0x00000000b3a00000| Untracked 
| 827|0x00000000b3b00000, 0x00000000b3b00000, 0x00000000b3c00000|  0%| F|  |TAMS 0x00000000b3b00000| PB 0x00000000b3b00000| Untracked 
| 828|0x00000000b3c00000, 0x00000000b3c00000, 0x00000000b3d00000|  0%| F|  |TAMS 0x00000000b3c00000| PB 0x00000000b3c00000| Untracked 
| 829|0x00000000b3d00000, 0x00000000b3d00000, 0x00000000b3e00000|  0%| F|  |TAMS 0x00000000b3d00000| PB 0x00000000b3d00000| Untracked 
| 830|0x00000000b3e00000, 0x00000000b3e00000, 0x00000000b3f00000|  0%| F|  |TAMS 0x00000000b3e00000| PB 0x00000000b3e00000| Untracked 
| 831|0x00000000b3f00000, 0x00000000b3f00000, 0x00000000b4000000|  0%| F|  |TAMS 0x00000000b3f00000| PB 0x00000000b3f00000| Untracked 
| 832|0x00000000b4000000, 0x00000000b4000000, 0x00000000b4100000|  0%| F|  |TAMS 0x00000000b4000000| PB 0x00000000b4000000| Untracked 
| 833|0x00000000b4100000, 0x00000000b4100000, 0x00000000b4200000|  0%| F|  |TAMS 0x00000000b4100000| PB 0x00000000b4100000| Untracked 
| 834|0x00000000b4200000, 0x00000000b4200000, 0x00000000b4300000|  0%| F|  |TAMS 0x00000000b4200000| PB 0x00000000b4200000| Untracked 
| 835|0x00000000b4300000, 0x00000000b4300000, 0x00000000b4400000|  0%| F|  |TAMS 0x00000000b4300000| PB 0x00000000b4300000| Untracked 
| 836|0x00000000b4400000, 0x00000000b4400000, 0x00000000b4500000|  0%| F|  |TAMS 0x00000000b4400000| PB 0x00000000b4400000| Untracked 
| 837|0x00000000b4500000, 0x00000000b4500000, 0x00000000b4600000|  0%| F|  |TAMS 0x00000000b4500000| PB 0x00000000b4500000| Untracked 
| 838|0x00000000b4600000, 0x00000000b4600000, 0x00000000b4700000|  0%| F|  |TAMS 0x00000000b4600000| PB 0x00000000b4600000| Untracked 
| 839|0x00000000b4700000, 0x00000000b4700000, 0x00000000b4800000|  0%| F|  |TAMS 0x00000000b4700000| PB 0x00000000b4700000| Untracked 
| 840|0x00000000b4800000, 0x00000000b4800000, 0x00000000b4900000|  0%| F|  |TAMS 0x00000000b4800000| PB 0x00000000b4800000| Untracked 
| 841|0x00000000b4900000, 0x00000000b4900000, 0x00000000b4a00000|  0%| F|  |TAMS 0x00000000b4900000| PB 0x00000000b4900000| Untracked 
| 842|0x00000000b4a00000, 0x00000000b4a00000, 0x00000000b4b00000|  0%| F|  |TAMS 0x00000000b4a00000| PB 0x00000000b4a00000| Untracked 
| 843|0x00000000b4b00000, 0x00000000b4b00000, 0x00000000b4c00000|  0%| F|  |TAMS 0x00000000b4b00000| PB 0x00000000b4b00000| Untracked 
| 844|0x00000000b4c00000, 0x00000000b4c00000, 0x00000000b4d00000|  0%| F|  |TAMS 0x00000000b4c00000| PB 0x00000000b4c00000| Untracked 
| 845|0x00000000b4d00000, 0x00000000b4d00000, 0x00000000b4e00000|  0%| F|  |TAMS 0x00000000b4d00000| PB 0x00000000b4d00000| Untracked 
| 846|0x00000000b4e00000, 0x00000000b4e00000, 0x00000000b4f00000|  0%| F|  |TAMS 0x00000000b4e00000| PB 0x00000000b4e00000| Untracked 
| 847|0x00000000b4f00000, 0x00000000b4f00000, 0x00000000b5000000|  0%| F|  |TAMS 0x00000000b4f00000| PB 0x00000000b4f00000| Untracked 
| 848|0x00000000b5000000, 0x00000000b5000000, 0x00000000b5100000|  0%| F|  |TAMS 0x00000000b5000000| PB 0x00000000b5000000| Untracked 
| 849|0x00000000b5100000, 0x00000000b5100000, 0x00000000b5200000|  0%| F|  |TAMS 0x00000000b5100000| PB 0x00000000b5100000| Untracked 
| 850|0x00000000b5200000, 0x00000000b5200000, 0x00000000b5300000|  0%| F|  |TAMS 0x00000000b5200000| PB 0x00000000b5200000| Untracked 
| 851|0x00000000b5300000, 0x00000000b5300000, 0x00000000b5400000|  0%| F|  |TAMS 0x00000000b5300000| PB 0x00000000b5300000| Untracked 
| 852|0x00000000b5400000, 0x00000000b5400000, 0x00000000b5500000|  0%| F|  |TAMS 0x00000000b5400000| PB 0x00000000b5400000| Untracked 
| 853|0x00000000b5500000, 0x00000000b5500000, 0x00000000b5600000|  0%| F|  |TAMS 0x00000000b5500000| PB 0x00000000b5500000| Untracked 
| 854|0x00000000b5600000, 0x00000000b5600000, 0x00000000b5700000|  0%| F|  |TAMS 0x00000000b5600000| PB 0x00000000b5600000| Untracked 
| 855|0x00000000b5700000, 0x00000000b5700000, 0x00000000b5800000|  0%| F|  |TAMS 0x00000000b5700000| PB 0x00000000b5700000| Untracked 
| 856|0x00000000b5800000, 0x00000000b5800000, 0x00000000b5900000|  0%| F|  |TAMS 0x00000000b5800000| PB 0x00000000b5800000| Untracked 
| 857|0x00000000b5900000, 0x00000000b5900000, 0x00000000b5a00000|  0%| F|  |TAMS 0x00000000b5900000| PB 0x00000000b5900000| Untracked 
| 858|0x00000000b5a00000, 0x00000000b5a00000, 0x00000000b5b00000|  0%| F|  |TAMS 0x00000000b5a00000| PB 0x00000000b5a00000| Untracked 
| 859|0x00000000b5b00000, 0x00000000b5b00000, 0x00000000b5c00000|  0%| F|  |TAMS 0x00000000b5b00000| PB 0x00000000b5b00000| Untracked 
| 860|0x00000000b5c00000, 0x00000000b5c00000, 0x00000000b5d00000|  0%| F|  |TAMS 0x00000000b5c00000| PB 0x00000000b5c00000| Untracked 
| 861|0x00000000b5d00000, 0x00000000b5d00000, 0x00000000b5e00000|  0%| F|  |TAMS 0x00000000b5d00000| PB 0x00000000b5d00000| Untracked 
| 862|0x00000000b5e00000, 0x00000000b5e00000, 0x00000000b5f00000|  0%| F|  |TAMS 0x00000000b5e00000| PB 0x00000000b5e00000| Untracked 
| 863|0x00000000b5f00000, 0x00000000b5f00000, 0x00000000b6000000|  0%| F|  |TAMS 0x00000000b5f00000| PB 0x00000000b5f00000| Untracked 
| 864|0x00000000b6000000, 0x00000000b6000000, 0x00000000b6100000|  0%| F|  |TAMS 0x00000000b6000000| PB 0x00000000b6000000| Untracked 
| 865|0x00000000b6100000, 0x00000000b6100000, 0x00000000b6200000|  0%| F|  |TAMS 0x00000000b6100000| PB 0x00000000b6100000| Untracked 
| 866|0x00000000b6200000, 0x00000000b6200000, 0x00000000b6300000|  0%| F|  |TAMS 0x00000000b6200000| PB 0x00000000b6200000| Untracked 
| 867|0x00000000b6300000, 0x00000000b6300000, 0x00000000b6400000|  0%| F|  |TAMS 0x00000000b6300000| PB 0x00000000b6300000| Untracked 
| 868|0x00000000b6400000, 0x00000000b6400000, 0x00000000b6500000|  0%| F|  |TAMS 0x00000000b6400000| PB 0x00000000b6400000| Untracked 
| 869|0x00000000b6500000, 0x00000000b6500000, 0x00000000b6600000|  0%| F|  |TAMS 0x00000000b6500000| PB 0x00000000b6500000| Untracked 
| 870|0x00000000b6600000, 0x00000000b6600000, 0x00000000b6700000|  0%| F|  |TAMS 0x00000000b6600000| PB 0x00000000b6600000| Untracked 
| 871|0x00000000b6700000, 0x00000000b6700000, 0x00000000b6800000|  0%| F|  |TAMS 0x00000000b6700000| PB 0x00000000b6700000| Untracked 
| 872|0x00000000b6800000, 0x00000000b6800000, 0x00000000b6900000|  0%| F|  |TAMS 0x00000000b6800000| PB 0x00000000b6800000| Untracked 
| 873|0x00000000b6900000, 0x00000000b6900000, 0x00000000b6a00000|  0%| F|  |TAMS 0x00000000b6900000| PB 0x00000000b6900000| Untracked 
| 874|0x00000000b6a00000, 0x00000000b6a00000, 0x00000000b6b00000|  0%| F|  |TAMS 0x00000000b6a00000| PB 0x00000000b6a00000| Untracked 
| 875|0x00000000b6b00000, 0x00000000b6b00000, 0x00000000b6c00000|  0%| F|  |TAMS 0x00000000b6b00000| PB 0x00000000b6b00000| Untracked 
| 876|0x00000000b6c00000, 0x00000000b6c00000, 0x00000000b6d00000|  0%| F|  |TAMS 0x00000000b6c00000| PB 0x00000000b6c00000| Untracked 
| 877|0x00000000b6d00000, 0x00000000b6d00000, 0x00000000b6e00000|  0%| F|  |TAMS 0x00000000b6d00000| PB 0x00000000b6d00000| Untracked 
| 878|0x00000000b6e00000, 0x00000000b6e00000, 0x00000000b6f00000|  0%| F|  |TAMS 0x00000000b6e00000| PB 0x00000000b6e00000| Untracked 
| 879|0x00000000b6f00000, 0x00000000b6f00000, 0x00000000b7000000|  0%| F|  |TAMS 0x00000000b6f00000| PB 0x00000000b6f00000| Untracked 
| 880|0x00000000b7000000, 0x00000000b7000000, 0x00000000b7100000|  0%| F|  |TAMS 0x00000000b7000000| PB 0x00000000b7000000| Untracked 
| 881|0x00000000b7100000, 0x00000000b7100000, 0x00000000b7200000|  0%| F|  |TAMS 0x00000000b7100000| PB 0x00000000b7100000| Untracked 
| 882|0x00000000b7200000, 0x00000000b7200000, 0x00000000b7300000|  0%| F|  |TAMS 0x00000000b7200000| PB 0x00000000b7200000| Untracked 
| 883|0x00000000b7300000, 0x00000000b7300000, 0x00000000b7400000|  0%| F|  |TAMS 0x00000000b7300000| PB 0x00000000b7300000| Untracked 
| 884|0x00000000b7400000, 0x00000000b7400000, 0x00000000b7500000|  0%| F|  |TAMS 0x00000000b7400000| PB 0x00000000b7400000| Untracked 
| 885|0x00000000b7500000, 0x00000000b7500000, 0x00000000b7600000|  0%| F|  |TAMS 0x00000000b7500000| PB 0x00000000b7500000| Untracked 
| 886|0x00000000b7600000, 0x00000000b7600000, 0x00000000b7700000|  0%| F|  |TAMS 0x00000000b7600000| PB 0x00000000b7600000| Untracked 
| 887|0x00000000b7700000, 0x00000000b7700000, 0x00000000b7800000|  0%| F|  |TAMS 0x00000000b7700000| PB 0x00000000b7700000| Untracked 
| 888|0x00000000b7800000, 0x00000000b7800000, 0x00000000b7900000|  0%| F|  |TAMS 0x00000000b7800000| PB 0x00000000b7800000| Untracked 
| 889|0x00000000b7900000, 0x00000000b7900000, 0x00000000b7a00000|  0%| F|  |TAMS 0x00000000b7900000| PB 0x00000000b7900000| Untracked 
| 890|0x00000000b7a00000, 0x00000000b7a00000, 0x00000000b7b00000|  0%| F|  |TAMS 0x00000000b7a00000| PB 0x00000000b7a00000| Untracked 
| 891|0x00000000b7b00000, 0x00000000b7b00000, 0x00000000b7c00000|  0%| F|  |TAMS 0x00000000b7b00000| PB 0x00000000b7b00000| Untracked 
| 892|0x00000000b7c00000, 0x00000000b7c00000, 0x00000000b7d00000|  0%| F|  |TAMS 0x00000000b7c00000| PB 0x00000000b7c00000| Untracked 
| 893|0x00000000b7d00000, 0x00000000b7d00000, 0x00000000b7e00000|  0%| F|  |TAMS 0x00000000b7d00000| PB 0x00000000b7d00000| Untracked 
| 894|0x00000000b7e00000, 0x00000000b7e00000, 0x00000000b7f00000|  0%| F|  |TAMS 0x00000000b7e00000| PB 0x00000000b7e00000| Untracked 
| 895|0x00000000b7f00000, 0x00000000b7f00000, 0x00000000b8000000|  0%| F|  |TAMS 0x00000000b7f00000| PB 0x00000000b7f00000| Untracked 
| 896|0x00000000b8000000, 0x00000000b8000000, 0x00000000b8100000|  0%| F|  |TAMS 0x00000000b8000000| PB 0x00000000b8000000| Untracked 
| 897|0x00000000b8100000, 0x00000000b8100000, 0x00000000b8200000|  0%| F|  |TAMS 0x00000000b8100000| PB 0x00000000b8100000| Untracked 
| 898|0x00000000b8200000, 0x00000000b8200000, 0x00000000b8300000|  0%| F|  |TAMS 0x00000000b8200000| PB 0x00000000b8200000| Untracked 
| 899|0x00000000b8300000, 0x00000000b8300000, 0x00000000b8400000|  0%| F|  |TAMS 0x00000000b8300000| PB 0x00000000b8300000| Untracked 
| 900|0x00000000b8400000, 0x00000000b8400000, 0x00000000b8500000|  0%| F|  |TAMS 0x00000000b8400000| PB 0x00000000b8400000| Untracked 
| 901|0x00000000b8500000, 0x00000000b8500000, 0x00000000b8600000|  0%| F|  |TAMS 0x00000000b8500000| PB 0x00000000b8500000| Untracked 
| 902|0x00000000b8600000, 0x00000000b8600000, 0x00000000b8700000|  0%| F|  |TAMS 0x00000000b8600000| PB 0x00000000b8600000| Untracked 
| 903|0x00000000b8700000, 0x00000000b8700000, 0x00000000b8800000|  0%| F|  |TAMS 0x00000000b8700000| PB 0x00000000b8700000| Untracked 
| 904|0x00000000b8800000, 0x00000000b8800000, 0x00000000b8900000|  0%| F|  |TAMS 0x00000000b8800000| PB 0x00000000b8800000| Untracked 
| 905|0x00000000b8900000, 0x00000000b8900000, 0x00000000b8a00000|  0%| F|  |TAMS 0x00000000b8900000| PB 0x00000000b8900000| Untracked 
| 906|0x00000000b8a00000, 0x00000000b8a00000, 0x00000000b8b00000|  0%| F|  |TAMS 0x00000000b8a00000| PB 0x00000000b8a00000| Untracked 
| 907|0x00000000b8b00000, 0x00000000b8b00000, 0x00000000b8c00000|  0%| F|  |TAMS 0x00000000b8b00000| PB 0x00000000b8b00000| Untracked 
| 908|0x00000000b8c00000, 0x00000000b8c00000, 0x00000000b8d00000|  0%| F|  |TAMS 0x00000000b8c00000| PB 0x00000000b8c00000| Untracked 
| 909|0x00000000b8d00000, 0x00000000b8d00000, 0x00000000b8e00000|  0%| F|  |TAMS 0x00000000b8d00000| PB 0x00000000b8d00000| Untracked 
| 910|0x00000000b8e00000, 0x00000000b8e00000, 0x00000000b8f00000|  0%| F|  |TAMS 0x00000000b8e00000| PB 0x00000000b8e00000| Untracked 
| 911|0x00000000b8f00000, 0x00000000b8f00000, 0x00000000b9000000|  0%| F|  |TAMS 0x00000000b8f00000| PB 0x00000000b8f00000| Untracked 
| 912|0x00000000b9000000, 0x00000000b9000000, 0x00000000b9100000|  0%| F|  |TAMS 0x00000000b9000000| PB 0x00000000b9000000| Untracked 
| 913|0x00000000b9100000, 0x00000000b9100000, 0x00000000b9200000|  0%| F|  |TAMS 0x00000000b9100000| PB 0x00000000b9100000| Untracked 
| 914|0x00000000b9200000, 0x00000000b9200000, 0x00000000b9300000|  0%| F|  |TAMS 0x00000000b9200000| PB 0x00000000b9200000| Untracked 
| 915|0x00000000b9300000, 0x00000000b9300000, 0x00000000b9400000|  0%| F|  |TAMS 0x00000000b9300000| PB 0x00000000b9300000| Untracked 
| 916|0x00000000b9400000, 0x00000000b9400000, 0x00000000b9500000|  0%| F|  |TAMS 0x00000000b9400000| PB 0x00000000b9400000| Untracked 
| 917|0x00000000b9500000, 0x00000000b9600000, 0x00000000b9600000|100%| E|CS|TAMS 0x00000000b9500000| PB 0x00000000b9500000| Complete 
| 918|0x00000000b9600000, 0x00000000b9700000, 0x00000000b9700000|100%| E|CS|TAMS 0x00000000b9600000| PB 0x00000000b9600000| Complete 
| 919|0x00000000b9700000, 0x00000000b9800000, 0x00000000b9800000|100%| E|CS|TAMS 0x00000000b9700000| PB 0x00000000b9700000| Complete 
| 920|0x00000000b9800000, 0x00000000b9900000, 0x00000000b9900000|100%| E|CS|TAMS 0x00000000b9800000| PB 0x00000000b9800000| Complete 
| 921|0x00000000b9900000, 0x00000000b9a00000, 0x00000000b9a00000|100%| E|CS|TAMS 0x00000000b9900000| PB 0x00000000b9900000| Complete 
| 922|0x00000000b9a00000, 0x00000000b9b00000, 0x00000000b9b00000|100%| E|CS|TAMS 0x00000000b9a00000| PB 0x00000000b9a00000| Complete 
| 923|0x00000000b9b00000, 0x00000000b9c00000, 0x00000000b9c00000|100%| E|CS|TAMS 0x00000000b9b00000| PB 0x00000000b9b00000| Complete 
| 924|0x00000000b9c00000, 0x00000000b9d00000, 0x00000000b9d00000|100%| E|CS|TAMS 0x00000000b9c00000| PB 0x00000000b9c00000| Complete 
| 925|0x00000000b9d00000, 0x00000000b9e00000, 0x00000000b9e00000|100%| E|CS|TAMS 0x00000000b9d00000| PB 0x00000000b9d00000| Complete 
| 926|0x00000000b9e00000, 0x00000000b9f00000, 0x00000000b9f00000|100%| E|CS|TAMS 0x00000000b9e00000| PB 0x00000000b9e00000| Complete 
| 927|0x00000000b9f00000, 0x00000000ba000000, 0x00000000ba000000|100%| E|CS|TAMS 0x00000000b9f00000| PB 0x00000000b9f00000| Complete 
| 928|0x00000000ba000000, 0x00000000ba100000, 0x00000000ba100000|100%| E|CS|TAMS 0x00000000ba000000| PB 0x00000000ba000000| Complete 
| 929|0x00000000ba100000, 0x00000000ba200000, 0x00000000ba200000|100%| E|CS|TAMS 0x00000000ba100000| PB 0x00000000ba100000| Complete 
| 930|0x00000000ba200000, 0x00000000ba300000, 0x00000000ba300000|100%| E|CS|TAMS 0x00000000ba200000| PB 0x00000000ba200000| Complete 
| 931|0x00000000ba300000, 0x00000000ba400000, 0x00000000ba400000|100%| E|CS|TAMS 0x00000000ba300000| PB 0x00000000ba300000| Complete 
| 932|0x00000000ba400000, 0x00000000ba500000, 0x00000000ba500000|100%| E|CS|TAMS 0x00000000ba400000| PB 0x00000000ba400000| Complete 
| 933|0x00000000ba500000, 0x00000000ba600000, 0x00000000ba600000|100%| E|CS|TAMS 0x00000000ba500000| PB 0x00000000ba500000| Complete 
| 934|0x00000000ba600000, 0x00000000ba700000, 0x00000000ba700000|100%| E|CS|TAMS 0x00000000ba600000| PB 0x00000000ba600000| Complete 
| 935|0x00000000ba700000, 0x00000000ba800000, 0x00000000ba800000|100%| E|CS|TAMS 0x00000000ba700000| PB 0x00000000ba700000| Complete 
| 936|0x00000000ba800000, 0x00000000ba900000, 0x00000000ba900000|100%| E|CS|TAMS 0x00000000ba800000| PB 0x00000000ba800000| Complete 
| 937|0x00000000ba900000, 0x00000000baa00000, 0x00000000baa00000|100%| E|CS|TAMS 0x00000000ba900000| PB 0x00000000ba900000| Complete 
| 938|0x00000000baa00000, 0x00000000bab00000, 0x00000000bab00000|100%| E|CS|TAMS 0x00000000baa00000| PB 0x00000000baa00000| Complete 
| 939|0x00000000bab00000, 0x00000000bac00000, 0x00000000bac00000|100%| E|CS|TAMS 0x00000000bab00000| PB 0x00000000bab00000| Complete 
| 940|0x00000000bac00000, 0x00000000bad00000, 0x00000000bad00000|100%| E|CS|TAMS 0x00000000bac00000| PB 0x00000000bac00000| Complete 
| 941|0x00000000bad00000, 0x00000000bae00000, 0x00000000bae00000|100%| E|CS|TAMS 0x00000000bad00000| PB 0x00000000bad00000| Complete 
| 942|0x00000000bae00000, 0x00000000baf00000, 0x00000000baf00000|100%| E|CS|TAMS 0x00000000bae00000| PB 0x00000000bae00000| Complete 
| 943|0x00000000baf00000, 0x00000000bb000000, 0x00000000bb000000|100%| E|CS|TAMS 0x00000000baf00000| PB 0x00000000baf00000| Complete 
| 944|0x00000000bb000000, 0x00000000bb100000, 0x00000000bb100000|100%| E|CS|TAMS 0x00000000bb000000| PB 0x00000000bb000000| Complete 
| 945|0x00000000bb100000, 0x00000000bb200000, 0x00000000bb200000|100%| E|CS|TAMS 0x00000000bb100000| PB 0x00000000bb100000| Complete 
| 946|0x00000000bb200000, 0x00000000bb300000, 0x00000000bb300000|100%| E|CS|TAMS 0x00000000bb200000| PB 0x00000000bb200000| Complete 
| 947|0x00000000bb300000, 0x00000000bb400000, 0x00000000bb400000|100%| E|CS|TAMS 0x00000000bb300000| PB 0x00000000bb300000| Complete 
| 948|0x00000000bb400000, 0x00000000bb500000, 0x00000000bb500000|100%| E|CS|TAMS 0x00000000bb400000| PB 0x00000000bb400000| Complete 
| 949|0x00000000bb500000, 0x00000000bb5e0a78, 0x00000000bb600000| 87%| S|CS|TAMS 0x00000000bb500000| PB 0x00000000bb500000| Complete 
| 950|0x00000000bb600000, 0x00000000bb700000, 0x00000000bb700000|100%| S|CS|TAMS 0x00000000bb600000| PB 0x00000000bb600000| Complete 
| 951|0x00000000bb700000, 0x00000000bb800000, 0x00000000bb800000|100%| S|CS|TAMS 0x00000000bb700000| PB 0x00000000bb700000| Complete 
| 952|0x00000000bb800000, 0x00000000bb900000, 0x00000000bb900000|100%| S|CS|TAMS 0x00000000bb800000| PB 0x00000000bb800000| Complete 
| 953|0x00000000bb900000, 0x00000000bba00000, 0x00000000bba00000|100%| S|CS|TAMS 0x00000000bb900000| PB 0x00000000bb900000| Complete 
| 954|0x00000000bba00000, 0x00000000bbb00000, 0x00000000bbb00000|100%| S|CS|TAMS 0x00000000bba00000| PB 0x00000000bba00000| Complete 
| 955|0x00000000bbb00000, 0x00000000bbc00000, 0x00000000bbc00000|100%| S|CS|TAMS 0x00000000bbb00000| PB 0x00000000bbb00000| Complete 
| 956|0x00000000bbc00000, 0x00000000bbd00000, 0x00000000bbd00000|100%| S|CS|TAMS 0x00000000bbc00000| PB 0x00000000bbc00000| Complete 
| 957|0x00000000bbd00000, 0x00000000bbe00000, 0x00000000bbe00000|100%| S|CS|TAMS 0x00000000bbd00000| PB 0x00000000bbd00000| Complete 
| 958|0x00000000bbe00000, 0x00000000bbf00000, 0x00000000bbf00000|100%| S|CS|TAMS 0x00000000bbe00000| PB 0x00000000bbe00000| Complete 
| 959|0x00000000bbf00000, 0x00000000bc000000, 0x00000000bc000000|100%| S|CS|TAMS 0x00000000bbf00000| PB 0x00000000bbf00000| Complete 
| 960|0x00000000bc000000, 0x00000000bc100000, 0x00000000bc100000|100%| S|CS|TAMS 0x00000000bc000000| PB 0x00000000bc000000| Complete 
| 961|0x00000000bc100000, 0x00000000bc200000, 0x00000000bc200000|100%| S|CS|TAMS 0x00000000bc100000| PB 0x00000000bc100000| Complete 
| 962|0x00000000bc200000, 0x00000000bc300000, 0x00000000bc300000|100%| S|CS|TAMS 0x00000000bc200000| PB 0x00000000bc200000| Complete 
| 963|0x00000000bc300000, 0x00000000bc400000, 0x00000000bc400000|100%| S|CS|TAMS 0x00000000bc300000| PB 0x00000000bc300000| Complete 
| 964|0x00000000bc400000, 0x00000000bc500000, 0x00000000bc500000|100%| S|CS|TAMS 0x00000000bc400000| PB 0x00000000bc400000| Complete 
| 965|0x00000000bc500000, 0x00000000bc600000, 0x00000000bc600000|100%| S|CS|TAMS 0x00000000bc500000| PB 0x00000000bc500000| Complete 
| 966|0x00000000bc600000, 0x00000000bc700000, 0x00000000bc700000|100%| S|CS|TAMS 0x00000000bc600000| PB 0x00000000bc600000| Complete 
| 967|0x00000000bc700000, 0x00000000bc800000, 0x00000000bc800000|100%| S|CS|TAMS 0x00000000bc700000| PB 0x00000000bc700000| Complete 
| 968|0x00000000bc800000, 0x00000000bc900000, 0x00000000bc900000|100%| S|CS|TAMS 0x00000000bc800000| PB 0x00000000bc800000| Complete 
| 969|0x00000000bc900000, 0x00000000bca00000, 0x00000000bca00000|100%| E|CS|TAMS 0x00000000bc900000| PB 0x00000000bc900000| Complete 
| 970|0x00000000bca00000, 0x00000000bcb00000, 0x00000000bcb00000|100%| E|CS|TAMS 0x00000000bca00000| PB 0x00000000bca00000| Complete 
| 971|0x00000000bcb00000, 0x00000000bcc00000, 0x00000000bcc00000|100%| E|CS|TAMS 0x00000000bcb00000| PB 0x00000000bcb00000| Complete 
| 972|0x00000000bcc00000, 0x00000000bcd00000, 0x00000000bcd00000|100%| E|CS|TAMS 0x00000000bcc00000| PB 0x00000000bcc00000| Complete 
| 973|0x00000000bcd00000, 0x00000000bce00000, 0x00000000bce00000|100%| E|CS|TAMS 0x00000000bcd00000| PB 0x00000000bcd00000| Complete 
| 974|0x00000000bce00000, 0x00000000bce00000, 0x00000000bcf00000|  0%| F|  |TAMS 0x00000000bce00000| PB 0x00000000bce00000| Untracked 
| 975|0x00000000bcf00000, 0x00000000bcf00000, 0x00000000bd000000|  0%| F|  |TAMS 0x00000000bcf00000| PB 0x00000000bcf00000| Untracked 
| 976|0x00000000bd000000, 0x00000000bd000000, 0x00000000bd100000|  0%| F|  |TAMS 0x00000000bd000000| PB 0x00000000bd000000| Untracked 
| 977|0x00000000bd100000, 0x00000000bd100000, 0x00000000bd200000|  0%| F|  |TAMS 0x00000000bd100000| PB 0x00000000bd100000| Untracked 
| 978|0x00000000bd200000, 0x00000000bd200000, 0x00000000bd300000|  0%| F|  |TAMS 0x00000000bd200000| PB 0x00000000bd200000| Untracked 
| 979|0x00000000bd300000, 0x00000000bd300000, 0x00000000bd400000|  0%| F|  |TAMS 0x00000000bd300000| PB 0x00000000bd300000| Untracked 
| 980|0x00000000bd400000, 0x00000000bd400000, 0x00000000bd500000|  0%| F|  |TAMS 0x00000000bd400000| PB 0x00000000bd400000| Untracked 
| 981|0x00000000bd500000, 0x00000000bd500000, 0x00000000bd600000|  0%| F|  |TAMS 0x00000000bd500000| PB 0x00000000bd500000| Untracked 
| 982|0x00000000bd600000, 0x00000000bd600000, 0x00000000bd700000|  0%| F|  |TAMS 0x00000000bd600000| PB 0x00000000bd600000| Untracked 
| 983|0x00000000bd700000, 0x00000000bd700000, 0x00000000bd800000|  0%| F|  |TAMS 0x00000000bd700000| PB 0x00000000bd700000| Untracked 
| 984|0x00000000bd800000, 0x00000000bd800000, 0x00000000bd900000|  0%| F|  |TAMS 0x00000000bd800000| PB 0x00000000bd800000| Untracked 
| 985|0x00000000bd900000, 0x00000000bd900000, 0x00000000bda00000|  0%| F|  |TAMS 0x00000000bd900000| PB 0x00000000bd900000| Untracked 
| 986|0x00000000bda00000, 0x00000000bda00000, 0x00000000bdb00000|  0%| F|  |TAMS 0x00000000bda00000| PB 0x00000000bda00000| Untracked 
| 987|0x00000000bdb00000, 0x00000000bdb00000, 0x00000000bdc00000|  0%| F|  |TAMS 0x00000000bdb00000| PB 0x00000000bdb00000| Untracked 
| 988|0x00000000bdc00000, 0x00000000bdc00000, 0x00000000bdd00000|  0%| F|  |TAMS 0x00000000bdc00000| PB 0x00000000bdc00000| Untracked 
| 989|0x00000000bdd00000, 0x00000000bdd00000, 0x00000000bde00000|  0%| F|  |TAMS 0x00000000bdd00000| PB 0x00000000bdd00000| Untracked 
| 990|0x00000000bde00000, 0x00000000bde00000, 0x00000000bdf00000|  0%| F|  |TAMS 0x00000000bde00000| PB 0x00000000bde00000| Untracked 
| 991|0x00000000bdf00000, 0x00000000bdf00000, 0x00000000be000000|  0%| F|  |TAMS 0x00000000bdf00000| PB 0x00000000bdf00000| Untracked 
| 992|0x00000000be000000, 0x00000000be000000, 0x00000000be100000|  0%| F|  |TAMS 0x00000000be000000| PB 0x00000000be000000| Untracked 
| 993|0x00000000be100000, 0x00000000be100000, 0x00000000be200000|  0%| F|  |TAMS 0x00000000be100000| PB 0x00000000be100000| Untracked 
| 994|0x00000000be200000, 0x00000000be200000, 0x00000000be300000|  0%| F|  |TAMS 0x00000000be200000| PB 0x00000000be200000| Untracked 
| 995|0x00000000be300000, 0x00000000be300000, 0x00000000be400000|  0%| F|  |TAMS 0x00000000be300000| PB 0x00000000be300000| Untracked 
| 996|0x00000000be400000, 0x00000000be400000, 0x00000000be500000|  0%| F|  |TAMS 0x00000000be400000| PB 0x00000000be400000| Untracked 
| 997|0x00000000be500000, 0x00000000be500000, 0x00000000be600000|  0%| F|  |TAMS 0x00000000be500000| PB 0x00000000be500000| Untracked 
| 998|0x00000000be600000, 0x00000000be600000, 0x00000000be700000|  0%| F|  |TAMS 0x00000000be600000| PB 0x00000000be600000| Untracked 
| 999|0x00000000be700000, 0x00000000be700000, 0x00000000be800000|  0%| F|  |TAMS 0x00000000be700000| PB 0x00000000be700000| Untracked 
|1000|0x00000000be800000, 0x00000000be800000, 0x00000000be900000|  0%| F|  |TAMS 0x00000000be800000| PB 0x00000000be800000| Untracked 
|1001|0x00000000be900000, 0x00000000be900000, 0x00000000bea00000|  0%| F|  |TAMS 0x00000000be900000| PB 0x00000000be900000| Untracked 
|1002|0x00000000bea00000, 0x00000000bea00000, 0x00000000beb00000|  0%| F|  |TAMS 0x00000000bea00000| PB 0x00000000bea00000| Untracked 
|1003|0x00000000beb00000, 0x00000000beb00000, 0x00000000bec00000|  0%| F|  |TAMS 0x00000000beb00000| PB 0x00000000beb00000| Untracked 
|1004|0x00000000bec00000, 0x00000000bec00000, 0x00000000bed00000|  0%| F|  |TAMS 0x00000000bec00000| PB 0x00000000bec00000| Untracked 
|1005|0x00000000bed00000, 0x00000000bed00000, 0x00000000bee00000|  0%| F|  |TAMS 0x00000000bed00000| PB 0x00000000bed00000| Untracked 
|1006|0x00000000bee00000, 0x00000000bee00000, 0x00000000bef00000|  0%| F|  |TAMS 0x00000000bee00000| PB 0x00000000bee00000| Untracked 
|1007|0x00000000bef00000, 0x00000000bf000000, 0x00000000bf000000|100%| E|  |TAMS 0x00000000bef00000| PB 0x00000000bef00000| Complete 
|1008|0x00000000bf000000, 0x00000000bf100000, 0x00000000bf100000|100%| E|CS|TAMS 0x00000000bf000000| PB 0x00000000bf000000| Complete 
|1009|0x00000000bf100000, 0x00000000bf200000, 0x00000000bf200000|100%| E|CS|TAMS 0x00000000bf100000| PB 0x00000000bf100000| Complete 
|1010|0x00000000bf200000, 0x00000000bf300000, 0x00000000bf300000|100%| E|CS|TAMS 0x00000000bf200000| PB 0x00000000bf200000| Complete 
|1011|0x00000000bf300000, 0x00000000bf400000, 0x00000000bf400000|100%| E|CS|TAMS 0x00000000bf300000| PB 0x00000000bf300000| Complete 
|1012|0x00000000bf400000, 0x00000000bf500000, 0x00000000bf500000|100%| E|CS|TAMS 0x00000000bf400000| PB 0x00000000bf400000| Complete 
|1013|0x00000000bf500000, 0x00000000bf600000, 0x00000000bf600000|100%| E|CS|TAMS 0x00000000bf500000| PB 0x00000000bf500000| Complete 
|1014|0x00000000bf600000, 0x00000000bf700000, 0x00000000bf700000|100%| E|CS|TAMS 0x00000000bf600000| PB 0x00000000bf600000| Complete 
|1015|0x00000000bf700000, 0x00000000bf800000, 0x00000000bf800000|100%| E|CS|TAMS 0x00000000bf700000| PB 0x00000000bf700000| Complete 
|1016|0x00000000bf800000, 0x00000000bf900000, 0x00000000bf900000|100%| E|CS|TAMS 0x00000000bf800000| PB 0x00000000bf800000| Complete 
|1017|0x00000000bf900000, 0x00000000bfa00000, 0x00000000bfa00000|100%| E|CS|TAMS 0x00000000bf900000| PB 0x00000000bf900000| Complete 
|1018|0x00000000bfa00000, 0x00000000bfb00000, 0x00000000bfb00000|100%| E|CS|TAMS 0x00000000bfa00000| PB 0x00000000bfa00000| Complete 
|1019|0x00000000bfb00000, 0x00000000bfc00000, 0x00000000bfc00000|100%| E|CS|TAMS 0x00000000bfb00000| PB 0x00000000bfb00000| Complete 
|1020|0x00000000bfc00000, 0x00000000bfd00000, 0x00000000bfd00000|100%| E|CS|TAMS 0x00000000bfc00000| PB 0x00000000bfc00000| Complete 
|1021|0x00000000bfd00000, 0x00000000bfe00000, 0x00000000bfe00000|100%| E|CS|TAMS 0x00000000bfd00000| PB 0x00000000bfd00000| Complete 
|1022|0x00000000bfe00000, 0x00000000bff00000, 0x00000000bff00000|100%| E|CS|TAMS 0x00000000bfe00000| PB 0x00000000bfe00000| Complete 
|2032|0x00000000ff000000, 0x00000000ff100000, 0x00000000ff100000|100%| O|  |TAMS 0x00000000ff100000| PB 0x00000000ff000000| Untracked 
|2033|0x00000000ff100000, 0x00000000ff200000, 0x00000000ff200000|100%| E|CS|TAMS 0x00000000ff100000| PB 0x00000000ff100000| Complete 
|2034|0x00000000ff200000, 0x00000000ff300000, 0x00000000ff300000|100%| E|CS|TAMS 0x00000000ff200000| PB 0x00000000ff200000| Complete 
|2035|0x00000000ff300000, 0x00000000ff400000, 0x00000000ff400000|100%| E|CS|TAMS 0x00000000ff300000| PB 0x00000000ff300000| Complete 
|2036|0x00000000ff400000, 0x00000000ff500000, 0x00000000ff500000|100%| O|  |TAMS 0x00000000ff500000| PB 0x00000000ff400000| Updating 
|2037|0x00000000ff500000, 0x00000000ff600000, 0x00000000ff600000|100%| O|  |TAMS 0x00000000ff600000| PB 0x00000000ff500000| Untracked 
|2038|0x00000000ff600000, 0x00000000ff700000, 0x00000000ff700000|100%| E|CS|TAMS 0x00000000ff600000| PB 0x00000000ff600000| Complete 
|2039|0x00000000ff700000, 0x00000000ff800000, 0x00000000ff800000|100%| E|CS|TAMS 0x00000000ff700000| PB 0x00000000ff700000| Complete 
|2040|0x00000000ff800000, 0x00000000ff900000, 0x00000000ff900000|100%| E|CS|TAMS 0x00000000ff800000| PB 0x00000000ff800000| Complete 
|2041|0x00000000ff900000, 0x00000000ffa00000, 0x00000000ffa00000|100%| O|  |TAMS 0x00000000ffa00000| PB 0x00000000ff900000| Untracked 
|2042|0x00000000ffa00000, 0x00000000ffb00000, 0x00000000ffb00000|100%| E|CS|TAMS 0x00000000ffa00000| PB 0x00000000ffa00000| Complete 
|2043|0x00000000ffb00000, 0x00000000ffc00000, 0x00000000ffc00000|100%| E|CS|TAMS 0x00000000ffb00000| PB 0x00000000ffb00000| Complete 
|2044|0x00000000ffc00000, 0x00000000ffd00000, 0x00000000ffd00000|100%| O|  |TAMS 0x00000000ffd00000| PB 0x00000000ffc00000| Updating 
|2045|0x00000000ffd00000, 0x00000000ffe00000, 0x00000000ffe00000|100%| O|  |TAMS 0x00000000ffe00000| PB 0x00000000ffd00000| Updating 
|2046|0x00000000ffe00000, 0x00000000fff00000, 0x00000000fff00000|100%| O|  |TAMS 0x00000000fff00000| PB 0x00000000ffe00000| Updating 
|2047|0x00000000fff00000, 0x0000000100000000, 0x0000000100000000|100%| O|  |TAMS 0x0000000100000000| PB 0x00000000fff00000| Updating 

Card table byte_map: [0x000001ed786d0000,0x000001ed78ad0000] _byte_map_base: 0x000001ed782d0000

Marking Bits: (CMBitMap*) 0x000001ed603c44b0
 Bits: [0x000001ed78ad0000, 0x000001ed7aad0000)

Polling page: 0x000001ed5e180000

Metaspace:

Usage:
  Non-class:    177.73 MB used.
      Class:     26.52 MB used.
       Both:    204.24 MB used.

Virtual space:
  Non-class space:      192.00 MB reserved,     180.19 MB ( 94%) committed,  3 nodes.
      Class space:      416.00 MB reserved,      28.62 MB (  7%) committed,  1 nodes.
             Both:      608.00 MB reserved,     208.81 MB ( 34%) committed. 

Chunk freelists:
   Non-Class:  11.76 MB
       Class:  3.40 MB
        Both:  15.16 MB

MaxMetaspaceSize: 512.00 MB
CompressedClassSpaceSize: 416.00 MB
Initial GC threshold: 21.00 MB
Current GC threshold: 347.94 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 14.
num_arena_births: 9562.
num_arena_deaths: 2956.
num_vsnodes_births: 4.
num_vsnodes_deaths: 0.
num_space_committed: 3338.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 3471.
num_chunks_taken_from_freelist: 21458.
num_chunk_merges: 1276.
num_chunk_splits: 13013.
num_chunks_enlarged: 7401.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=119168Kb used=16714Kb max_used=20191Kb free=102453Kb
 bounds [0x000001ed705f0000, 0x000001ed719b0000, 0x000001ed77a50000]
CodeHeap 'profiled nmethods': size=119104Kb used=27667Kb max_used=35846Kb free=91436Kb
 bounds [0x000001ed68a50000, 0x000001ed6ad60000, 0x000001ed6fea0000]
CodeHeap 'non-nmethods': size=7488Kb used=4923Kb max_used=4968Kb free=2564Kb
 bounds [0x000001ed6fea0000, 0x000001ed703c0000, 0x000001ed705f0000]
 total_blobs=15576 nmethods=14329 adapters=1146
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 10336.960 Thread 0x000001ed2ba945d0 38563       1       org.jetbrains.kotlin.resolve.lazy.LazyImportResolver::getTraceForImportResolve (5 bytes)
Event: 10336.960 Thread 0x000001ed7da8b000 nmethod 38562 0x000001ed70651290 code [0x000001ed70651420, 0x000001ed706514e8]
Event: 10336.960 Thread 0x000001ed2ba945d0 nmethod 38563 0x000001ed70656510 code [0x000001ed706566a0, 0x000001ed70656768]
Event: 10336.960 Thread 0x000001ed23e2dac0 38564       1       org.jetbrains.kotlin.resolve.lazy.LazyImportResolver::getExcludedImportNames (5 bytes)
Event: 10336.960 Thread 0x000001ed7da8b000 38565       1       org.jetbrains.kotlin.resolve.lazy.LazyImportResolver::getPackageFragment (5 bytes)
Event: 10336.961 Thread 0x000001ed23e2dac0 nmethod 38564 0x000001ed70662d90 code [0x000001ed70662f20, 0x000001ed70662fe8]
Event: 10336.961 Thread 0x000001ed7da8b000 nmethod 38565 0x000001ed70666b90 code [0x000001ed70666d20, 0x000001ed70666de8]
Event: 10336.966 Thread 0x000001ed7da8b000 38566       1       org.jetbrains.kotlin.descriptors.impl.FunctionDescriptorImpl::getExtensionReceiverParameter (5 bytes)
Event: 10336.966 Thread 0x000001ed7da8b000 nmethod 38566 0x000001ed7067bf10 code [0x000001ed7067c0a0, 0x000001ed7067c168]
Event: 10336.967 Thread 0x000001ed7da8b000 38567       3       org.jetbrains.kotlin.name.FqName::shortNameOrSpecial (17 bytes)
Event: 10336.967 Thread 0x000001ed23e2dac0 38568       3       org.jetbrains.kotlin.name.FqNameUnsafe::shortNameOrSpecial (34 bytes)
Event: 10336.968 Thread 0x000001ed7da8b000 nmethod 38567 0x000001ed68b7cb10 code [0x000001ed68b7cd00, 0x000001ed68b7d0f0]
Event: 10336.968 Thread 0x000001ed23e2dac0 nmethod 38568 0x000001ed68ac4490 code [0x000001ed68ac4680, 0x000001ed68ac49c0]
Event: 10336.975 Thread 0x000001ed7da8b000 38573       3       com.intellij.psi.impl.compiled.FirstPassData$ClassEntry::<init> (5 bytes)
Event: 10336.975 Thread 0x000001ed23e2dac0 38572       3       com.intellij.psi.impl.compiled.FirstPassData$ClassEntry::<init> (5 bytes)
Event: 10336.976 Thread 0x000001ed23e2dac0 nmethod 38572 0x000001ed68b68590 code [0x000001ed68b68740, 0x000001ed68b68908]
Event: 10336.976 Thread 0x000001ed7da8b000 nmethod 38573 0x000001ed68bf5d90 code [0x000001ed68bf5f40, 0x000001ed68bf60a8]
Event: 10336.982 Thread 0x000001ed2ba945d0 38577       3       org.jetbrains.kotlin.metadata.jvm.JvmProtoBuf$JvmPropertySignature::writeTo (113 bytes)
Event: 10336.986 Thread 0x000001ed7da8b000 38579       3       com.intellij.psi.impl.compiled.ClsModifierListImpl::hasModifierProperty (25 bytes)
Event: 10336.986 Thread 0x000001ed23e2dac0 38580       3       com.intellij.psi.impl.cache.ModifierFlags::hasModifierProperty (35 bytes)

GC Heap History (20 events):
Event: 10280.643 GC heap before
{Heap before GC invocations=93 (full 0):
 garbage-first heap   total 807936K, used 739679K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 271 young (277504K), 25 survivors (25600K)
 Metaspace       used 145801K, committed 148672K, reserved 557056K
  class space    used 19560K, committed 20864K, reserved 425984K
}
Event: 10280.676 GC heap after
{Heap after GC invocations=94 (full 0):
 garbage-first heap   total 807936K, used 500428K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 25 young (25600K), 25 survivors (25600K)
 Metaspace       used 145801K, committed 148672K, reserved 557056K
  class space    used 19560K, committed 20864K, reserved 425984K
}
Event: 10285.073 GC heap before
{Heap before GC invocations=95 (full 0):
 garbage-first heap   total 836608K, used 736972K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 257 young (263168K), 25 survivors (25600K)
 Metaspace       used 146610K, committed 149568K, reserved 557056K
  class space    used 19685K, committed 21056K, reserved 425984K
}
Event: 10285.104 GC heap after
{Heap after GC invocations=96 (full 0):
 garbage-first heap   total 836608K, used 511145K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 27 young (27648K), 27 survivors (27648K)
 Metaspace       used 146610K, committed 149568K, reserved 557056K
  class space    used 19685K, committed 21056K, reserved 425984K
}
Event: 10294.091 GC heap before
{Heap before GC invocations=96 (full 0):
 garbage-first heap   total 836608K, used 769193K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 275 young (281600K), 27 survivors (27648K)
 Metaspace       used 149939K, committed 153024K, reserved 622592K
  class space    used 19999K, committed 21376K, reserved 425984K
}
Event: 10294.130 GC heap after
{Heap after GC invocations=97 (full 0):
 garbage-first heap   total 836608K, used 514799K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 28 young (28672K), 28 survivors (28672K)
 Metaspace       used 149939K, committed 153024K, reserved 622592K
  class space    used 19999K, committed 21376K, reserved 425984K
}
Event: 10316.506 GC heap before
{Heap before GC invocations=97 (full 0):
 garbage-first heap   total 836608K, used 709359K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 220 young (225280K), 28 survivors (28672K)
 Metaspace       used 150092K, committed 153216K, reserved 622592K
  class space    used 20010K, committed 21440K, reserved 425984K
}
Event: 10316.567 GC heap after
{Heap after GC invocations=98 (full 0):
 garbage-first heap   total 836608K, used 526122K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 27 young (27648K), 27 survivors (27648K)
 Metaspace       used 150092K, committed 153216K, reserved 622592K
  class space    used 20010K, committed 21440K, reserved 425984K
}
Event: 10325.912 GC heap before
{Heap before GC invocations=99 (full 0):
 garbage-first heap   total 879616K, used 775978K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 259 young (265216K), 27 survivors (27648K)
 Metaspace       used 169914K, committed 173248K, reserved 622592K
  class space    used 22023K, committed 23552K, reserved 425984K
}
Event: 10325.949 GC heap after
{Heap after GC invocations=100 (full 0):
 garbage-first heap   total 879616K, used 551665K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 31 young (31744K), 31 survivors (31744K)
 Metaspace       used 169914K, committed 173248K, reserved 622592K
  class space    used 22023K, committed 23552K, reserved 425984K
}
Event: 10327.815 GC heap before
{Heap before GC invocations=100 (full 0):
 garbage-first heap   total 879616K, used 805617K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 276 young (282624K), 31 survivors (31744K)
 Metaspace       used 178790K, committed 182144K, reserved 622592K
  class space    used 23443K, committed 24960K, reserved 425984K
}
Event: 10327.865 GC heap after
{Heap after GC invocations=101 (full 0):
 garbage-first heap   total 879616K, used 573440K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 35 young (35840K), 35 survivors (35840K)
 Metaspace       used 178790K, committed 182144K, reserved 622592K
  class space    used 23443K, committed 24960K, reserved 425984K
}
Event: 10328.855 GC heap before
{Heap before GC invocations=101 (full 0):
 garbage-first heap   total 879616K, used 798720K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 255 young (261120K), 35 survivors (35840K)
 Metaspace       used 178828K, committed 182144K, reserved 622592K
  class space    used 23443K, committed 24960K, reserved 425984K
}
Event: 10328.911 GC heap after
{Heap after GC invocations=102 (full 0):
 garbage-first heap   total 879616K, used 608123K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 32 young (32768K), 32 survivors (32768K)
 Metaspace       used 178828K, committed 182144K, reserved 622592K
  class space    used 23443K, committed 24960K, reserved 425984K
}
Event: 10331.636 GC heap before
{Heap before GC invocations=103 (full 0):
 garbage-first heap   total 1013760K, used 820091K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 215 young (220160K), 32 survivors (32768K)
 Metaspace       used 186636K, committed 190272K, reserved 622592K
  class space    used 24328K, committed 25984K, reserved 425984K
}
Event: 10331.669 GC heap after
{Heap after GC invocations=104 (full 0):
 garbage-first heap   total 1013760K, used 636899K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 21 young (21504K), 21 survivors (21504K)
 Metaspace       used 186636K, committed 190272K, reserved 622592K
  class space    used 24328K, committed 25984K, reserved 425984K
}
Event: 10335.633 GC heap before
{Heap before GC invocations=104 (full 0):
 garbage-first heap   total 1013760K, used 952291K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 307 young (314368K), 21 survivors (21504K)
 Metaspace       used 206152K, committed 210880K, reserved 622592K
  class space    used 26717K, committed 28928K, reserved 425984K
}
Event: 10335.665 GC heap after
{Heap after GC invocations=105 (full 0):
 garbage-first heap   total 1013760K, used 635999K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 30 young (30720K), 30 survivors (30720K)
 Metaspace       used 206152K, committed 210880K, reserved 622592K
  class space    used 26717K, committed 28928K, reserved 425984K
}
Event: 10335.748 GC heap before
{Heap before GC invocations=105 (full 0):
 garbage-first heap   total 1013760K, used 648287K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 43 young (44032K), 30 survivors (30720K)
 Metaspace       used 206325K, committed 211008K, reserved 622592K
  class space    used 26729K, committed 28928K, reserved 425984K
}
Event: 10335.773 GC heap after
{Heap after GC invocations=106 (full 0):
 garbage-first heap   total 1013760K, used 636189K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 20 young (20480K), 20 survivors (20480K)
 Metaspace       used 206325K, committed 211008K, reserved 622592K
  class space    used 26729K, committed 28928K, reserved 425984K
}

Dll operation events (17 events):
Event: 0.031 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\java.dll
Event: 0.075 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\jsvml.dll
Event: 0.116 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\zip.dll
Event: 0.121 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\instrument.dll
Event: 0.130 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\net.dll
Event: 0.137 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\nio.dll
Event: 0.141 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\zip.dll
Event: 0.408 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\jimage.dll
Event: 0.586 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\verify.dll
Event: 0.756 Loaded shared library C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64\native-platform.dll
Event: 0.790 Loaded shared library C:\Users\<USER>\.gradle\native\100fb08df4bc3b14c8652ba06237920a3bd2aa13389f12d3474272988ae205f9\windows-amd64\native-platform-file-events.dll
Event: 2.249 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\management.dll
Event: 2.255 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\management_ext.dll
Event: 2.491 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\extnet.dll
Event: 2.700 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\sunmscapi.dll
Event: 30.095 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\native-platform1997863555420315592dir\native-platform.dll
Event: 31.725 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\rmi.dll

Deoptimization events (20 events):
Event: 10336.431 Thread 0x000001ed2fac3770 DEOPT PACKING pc=0x000001ed710929ac sp=0x000000cf282fb460
Event: 10336.431 Thread 0x000001ed2fac3770 DEOPT UNPACKING pc=0x000001ed6fef46a2 sp=0x000000cf282fb420 mode 2
Event: 10336.902 Thread 0x000001ed2ae4c7a0 Uncommon trap: trap_request=0xffffffbe fr.pc=0x000001ed7195abb4 relative=0x0000000000000b34
Event: 10336.902 Thread 0x000001ed2ae4c7a0 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x000001ed7195abb4 method=it.unimi.dsi.fastutil.objects.ObjectOpenHashSet.add(Ljava/lang/Object;)Z @ 73 c2
Event: 10336.902 Thread 0x000001ed2ae4c7a0 DEOPT PACKING pc=0x000001ed7195abb4 sp=0x000000cf235fb360
Event: 10336.902 Thread 0x000001ed2ae4c7a0 DEOPT UNPACKING pc=0x000001ed6fef46a2 sp=0x000000cf235fb318 mode 2
Event: 10336.902 Thread 0x000001ed2ae4c7a0 Uncommon trap: trap_request=0xffffffbe fr.pc=0x000001ed7195abb4 relative=0x0000000000000b34
Event: 10336.902 Thread 0x000001ed2ae4c7a0 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x000001ed7195abb4 method=it.unimi.dsi.fastutil.objects.ObjectOpenHashSet.add(Ljava/lang/Object;)Z @ 73 c2
Event: 10336.902 Thread 0x000001ed2ae4c7a0 DEOPT PACKING pc=0x000001ed7195abb4 sp=0x000000cf235fb360
Event: 10336.902 Thread 0x000001ed2ae4c7a0 DEOPT UNPACKING pc=0x000001ed6fef46a2 sp=0x000000cf235fb318 mode 2
Event: 10336.902 Thread 0x000001ed2ae4c7a0 Uncommon trap: trap_request=0xffffffbe fr.pc=0x000001ed7195abb4 relative=0x0000000000000b34
Event: 10336.902 Thread 0x000001ed2ae4c7a0 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x000001ed7195abb4 method=it.unimi.dsi.fastutil.objects.ObjectOpenHashSet.add(Ljava/lang/Object;)Z @ 73 c2
Event: 10336.903 Thread 0x000001ed2ae4c7a0 DEOPT PACKING pc=0x000001ed7195abb4 sp=0x000000cf235fb360
Event: 10336.903 Thread 0x000001ed2ae4c7a0 DEOPT UNPACKING pc=0x000001ed6fef46a2 sp=0x000000cf235fb318 mode 2
Event: 10336.903 Thread 0x000001ed2ae4c7a0 Uncommon trap: trap_request=0xffffffbe fr.pc=0x000001ed7195abb4 relative=0x0000000000000b34
Event: 10336.903 Thread 0x000001ed2ae4c7a0 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x000001ed7195abb4 method=it.unimi.dsi.fastutil.objects.ObjectOpenHashSet.add(Ljava/lang/Object;)Z @ 73 c2
Event: 10336.903 Thread 0x000001ed2ae4c7a0 DEOPT PACKING pc=0x000001ed7195abb4 sp=0x000000cf235fb360
Event: 10336.903 Thread 0x000001ed2ae4c7a0 DEOPT UNPACKING pc=0x000001ed6fef46a2 sp=0x000000cf235fb318 mode 2
Event: 10336.972 Thread 0x000001ed2fac3770 DEOPT PACKING pc=0x000001ed6922909d sp=0x000000cf282fa9f0
Event: 10336.972 Thread 0x000001ed2fac3770 DEOPT UNPACKING pc=0x000001ed6fef4e42 sp=0x000000cf282f9e80 mode 0

Classes loaded (20 events):
Event: 10332.412 Loading class org/xml/sax/ext/LexicalHandler done
Event: 10332.412 Loading class org/xml/sax/ext/DeclHandler
Event: 10332.412 Loading class org/xml/sax/ext/DeclHandler done
Event: 10332.413 Loading class org/xml/sax/ext/DefaultHandler2 done
Event: 10332.427 Loading class com/sun/org/apache/xerces/internal/dom/ParentNode$UserDataRecord
Event: 10332.428 Loading class com/sun/org/apache/xerces/internal/dom/ParentNode$UserDataRecord done
Event: 10332.430 Loading class com/sun/org/apache/xerces/internal/util/EntityResolver2Wrapper
Event: 10332.431 Loading class com/sun/org/apache/xerces/internal/util/EntityResolver2Wrapper done
Event: 10332.432 Loading class com/sun/org/apache/xerces/internal/dom/ElementNSImpl
Event: 10332.432 Loading class com/sun/org/apache/xerces/internal/dom/ElementNSImpl done
Event: 10332.433 Loading class com/sun/org/apache/xerces/internal/dom/AttrNSImpl
Event: 10332.434 Loading class com/sun/org/apache/xerces/internal/dom/AttrNSImpl done
Event: 10332.435 Loading class com/sun/org/apache/xerces/internal/dom/CommentImpl
Event: 10332.435 Loading class org/w3c/dom/Comment
Event: 10332.436 Loading class org/w3c/dom/Comment done
Event: 10332.436 Loading class com/sun/org/apache/xerces/internal/dom/CommentImpl done
Event: 10335.253 Loading class java/lang/Character$UnicodeBlock
Event: 10335.255 Loading class java/lang/Character$Subset
Event: 10335.256 Loading class java/lang/Character$Subset done
Event: 10335.257 Loading class java/lang/Character$UnicodeBlock done

Classes unloaded (20 events):
Event: 10336.456 Thread 0x000001ed7da65850 Unloading class 0x000001ed0254bc00 'java/lang/invoke/LambdaForm$MH+0x000001ed0254bc00'
Event: 10336.456 Thread 0x000001ed7da65850 Unloading class 0x000001ed0254b800 'java/lang/invoke/LambdaForm$MH+0x000001ed0254b800'
Event: 10336.456 Thread 0x000001ed7da65850 Unloading class 0x000001ed0254b000 'java/lang/invoke/LambdaForm$MH+0x000001ed0254b000'
Event: 10336.456 Thread 0x000001ed7da65850 Unloading class 0x000001ed0254ac00 'java/lang/invoke/LambdaForm$MH+0x000001ed0254ac00'
Event: 10336.456 Thread 0x000001ed7da65850 Unloading class 0x000001ed0254a800 'java/lang/invoke/LambdaForm$MH+0x000001ed0254a800'
Event: 10336.456 Thread 0x000001ed7da65850 Unloading class 0x000001ed02549800 'java/lang/invoke/LambdaForm$MH+0x000001ed02549800'
Event: 10336.456 Thread 0x000001ed7da65850 Unloading class 0x000001ed02549c00 'java/lang/invoke/LambdaForm$MH+0x000001ed02549c00'
Event: 10336.456 Thread 0x000001ed7da65850 Unloading class 0x000001ed02548c00 'java/lang/invoke/LambdaForm$MH+0x000001ed02548c00'
Event: 10336.456 Thread 0x000001ed7da65850 Unloading class 0x000001ed02549400 'java/lang/invoke/LambdaForm$MH+0x000001ed02549400'
Event: 10336.456 Thread 0x000001ed7da65850 Unloading class 0x000001ed02548800 'java/lang/invoke/LambdaForm$DMH+0x000001ed02548800'
Event: 10336.456 Thread 0x000001ed7da65850 Unloading class 0x000001ed02548400 'java/lang/invoke/LambdaForm$DMH+0x000001ed02548400'
Event: 10336.456 Thread 0x000001ed7da65850 Unloading class 0x000001ed02547800 'java/lang/invoke/LambdaForm$MH+0x000001ed02547800'
Event: 10336.456 Thread 0x000001ed7da65850 Unloading class 0x000001ed02547400 'java/lang/invoke/LambdaForm$MH+0x000001ed02547400'
Event: 10336.456 Thread 0x000001ed7da65850 Unloading class 0x000001ed02547000 'java/lang/invoke/LambdaForm$MH+0x000001ed02547000'
Event: 10336.456 Thread 0x000001ed7da65850 Unloading class 0x000001ed02546400 'java/lang/invoke/LambdaForm$MH+0x000001ed02546400'
Event: 10336.456 Thread 0x000001ed7da65850 Unloading class 0x000001ed02546000 'java/lang/invoke/LambdaForm$MH+0x000001ed02546000'
Event: 10336.456 Thread 0x000001ed7da65850 Unloading class 0x000001ed02545800 'java/lang/invoke/LambdaForm$MH+0x000001ed02545800'
Event: 10336.456 Thread 0x000001ed7da65850 Unloading class 0x000001ed02545000 'java/lang/invoke/LambdaForm$MH+0x000001ed02545000'
Event: 10336.456 Thread 0x000001ed7da65850 Unloading class 0x000001ed02544800 'java/lang/invoke/LambdaForm$MH+0x000001ed02544800'
Event: 10336.456 Thread 0x000001ed7da65850 Unloading class 0x000001ed02544400 'java/lang/invoke/LambdaForm$MH+0x000001ed02544400'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 10333.823 Thread 0x000001ed24292da0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000b2e7f0c8}: 'java.lang.Object java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000b2e7f0c8) 
throw
Event: 10333.823 Thread 0x000001ed2fac3770 Exception <a 'java/lang/NoSuchMethodError'{0x00000000b2ecb340}: 'java.lang.Object java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000b2ecb340) 
throw
Event: 10333.864 Thread 0x000001ed2fac3770 Exception <a 'java/lang/NoSuchMethodError'{0x00000000b2c62b10}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang
Event: 10333.864 Thread 0x000001ed24292da0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000b2b06050}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang
Event: 10333.864 Thread 0x000001ed2ae4c7a0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000b2cbd918}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang
Event: 10333.898 Thread 0x000001ed2fac3770 Exception <a 'java/lang/NoSuchMethodError'{0x00000000b286d1f8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000
Event: 10333.898 Thread 0x000001ed2ae4c7a0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000b2703fb8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000
Event: 10333.898 Thread 0x000001ed24292da0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000b28ce328}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000
Event: 10333.899 Thread 0x000001ed2fac3770 Exception <a 'java/lang/NoSuchMethodError'{0x00000000b2873550}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Ob
Event: 10333.899 Thread 0x000001ed24292da0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000b28d3f58}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Ob
Event: 10333.899 Thread 0x000001ed2ae4c7a0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000b270a6f8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Ob
Event: 10333.900 Thread 0x000001ed24292da0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000b28d90e0}: 'java.lang.Object java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000
Event: 10333.900 Thread 0x000001ed2fac3770 Exception <a 'java/lang/NoSuchMethodError'{0x00000000b2878750}: 'java.lang.Object java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000
Event: 10333.900 Thread 0x000001ed2ae4c7a0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000b270f880}: 'java.lang.Object java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000
Event: 10333.972 Thread 0x000001ed24292da0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000b2124a50}: 'java.lang.Object java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.O
Event: 10335.225 Thread 0x000001ed24292da0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000abe7bf98}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000abe7bf98) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 10335.230 Thread 0x000001ed2ae4c7a0 Implicit null exception at 0x000001ed70d4fc8a to 0x000001ed70d50000
Event: 10335.421 Thread 0x000001ed24292da0 Implicit null exception at 0x000001ed70f1e303 to 0x000001ed70f1e58c
Event: 10335.489 Thread 0x000001ed2ae4c7a0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ab49d090}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000000ab49d090) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 10336.073 Thread 0x000001ed2fac3770 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ba55a3b8}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000ba55a3b8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 10335.142 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 10335.146 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 10335.153 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 10335.154 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 10335.293 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 10335.296 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 10335.409 Executing VM operation: ICBufferFull
Event: 10335.410 Executing VM operation: ICBufferFull done
Event: 10335.632 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 10335.665 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 10335.747 Executing VM operation: G1TryInitiateConcMark (G1 Humongous Allocation)
Event: 10335.773 Executing VM operation: G1TryInitiateConcMark (G1 Humongous Allocation) done
Event: 10335.818 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 10335.819 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 10336.090 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 10336.093 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 10336.100 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 10336.101 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 10336.454 Executing VM operation: G1PauseRemark
Event: 10336.893 Executing VM operation: G1PauseRemark done

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 10336.884 Thread 0x000001ed7da65850 flushing  nmethod 0x000001ed69e2ad90
Event: 10336.884 Thread 0x000001ed7da65850 flushing  nmethod 0x000001ed69e56e10
Event: 10336.884 Thread 0x000001ed7da65850 flushing  nmethod 0x000001ed69fb4c90
Event: 10336.884 Thread 0x000001ed7da65850 flushing  nmethod 0x000001ed69fb6b90
Event: 10336.884 Thread 0x000001ed7da65850 flushing  nmethod 0x000001ed6a027d90
Event: 10336.884 Thread 0x000001ed7da65850 flushing  nmethod 0x000001ed6a030310
Event: 10336.884 Thread 0x000001ed7da65850 flushing  nmethod 0x000001ed6a284290
Event: 10336.885 Thread 0x000001ed7da65850 flushing  nmethod 0x000001ed6a44dc90
Event: 10336.885 Thread 0x000001ed7da65850 flushing  nmethod 0x000001ed6a641390
Event: 10336.885 Thread 0x000001ed7da65850 flushing osr nmethod 0x000001ed6a645990
Event: 10336.885 Thread 0x000001ed7da65850 flushing  nmethod 0x000001ed6a695790
Event: 10336.885 Thread 0x000001ed7da65850 flushing  nmethod 0x000001ed6a697e10
Event: 10336.885 Thread 0x000001ed7da65850 flushing  nmethod 0x000001ed6a9d5f10
Event: 10336.885 Thread 0x000001ed7da65850 flushing  nmethod 0x000001ed6a9d7990
Event: 10336.885 Thread 0x000001ed7da65850 flushing  nmethod 0x000001ed6ab63d10
Event: 10336.885 Thread 0x000001ed7da65850 flushing  nmethod 0x000001ed6ab6b590
Event: 10336.885 Thread 0x000001ed7da65850 flushing  nmethod 0x000001ed6abc7490
Event: 10336.885 Thread 0x000001ed7da65850 flushing  nmethod 0x000001ed6abcb710
Event: 10336.885 Thread 0x000001ed7da65850 flushing  nmethod 0x000001ed6ac23390
Event: 10336.885 Thread 0x000001ed7da65850 flushing  nmethod 0x000001ed6ac29a90

Events (20 events):
Event: 10329.344 Thread 0x000001ed7ea9cff0 Thread exited: 0x000001ed7ea9cff0
Event: 10329.475 Thread 0x000001ed2ba945d0 Thread added: 0x000001ed23e2dac0
Event: 10329.492 Thread 0x000001ed7da8b000 Thread added: 0x000001ed30f91960
Event: 10329.747 Thread 0x000001ed7e9d5a90 Thread exited: 0x000001ed7e9d5a90
Event: 10329.763 Thread 0x000001ed20292120 Thread exited: 0x000001ed20292120
Event: 10329.763 Thread 0x000001ed2db16430 Thread exited: 0x000001ed2db16430
Event: 10329.878 Thread 0x000001ed23e2dac0 Thread added: 0x000001ed30f92700
Event: 10329.996 Thread 0x000001ed23e2dac0 Thread added: 0x000001ed30f934a0
Event: 10332.460 Thread 0x000001ed30f934a0 Thread exited: 0x000001ed30f934a0
Event: 10332.667 Thread 0x000001ed30f92700 Thread exited: 0x000001ed30f92700
Event: 10332.707 Thread 0x000001ed23e2dac0 Thread added: 0x000001ed2ea8a8f0
Event: 10332.709 Thread 0x000001ed30f91960 Thread added: 0x000001ed30f92700
Event: 10333.363 Thread 0x000001ed30f92700 Thread exited: 0x000001ed30f92700
Event: 10333.366 Thread 0x000001ed2ea8a8f0 Thread exited: 0x000001ed2ea8a8f0
Event: 10333.750 Thread 0x000001ed30f91960 Thread added: 0x000001ed30f94910
Event: 10333.947 Thread 0x000001ed23e2dac0 Thread added: 0x000001ed2b0eac20
Event: 10334.376 Thread 0x000001ed2b0eac20 Thread exited: 0x000001ed2b0eac20
Event: 10334.636 Thread 0x000001ed23e2dac0 Thread added: 0x000001ed2b0eb2f0
Event: 10334.750 Thread 0x000001ed30f91960 Thread added: 0x000001ed2db14fc0
Event: 10336.953 Thread 0x000001ed30f91960 Thread exited: 0x000001ed30f91960


Dynamic libraries:
0x00007ff72e490000 - 0x00007ff72e49e000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\java.exe
0x00007ffee8da0000 - 0x00007ffee9006000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffeddbf0000 - 0x00007ffeddc0b000 	C:\Program Files\Norton\Suite\aswhook.dll
0x00007ffee7790000 - 0x00007ffee7859000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffee6560000 - 0x00007ffee692c000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffee5ff0000 - 0x00007ffee613b000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffe25770000 - 0x00007ffe25788000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\jli.dll
0x00007ffe25750000 - 0x00007ffe2576e000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\VCRUNTIME140.dll
0x00007ffee8ac0000 - 0x00007ffee8c8a000 	C:\WINDOWS\System32\USER32.dll
0x00007ffed3960000 - 0x00007ffed3bfa000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.3912_none_3e07963ce335137e\COMCTL32.dll
0x00007ffee63f0000 - 0x00007ffee6417000 	C:\WINDOWS\System32\win32u.dll
0x00007ffee8a90000 - 0x00007ffee8abb000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffee7ad0000 - 0x00007ffee7b79000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffee6420000 - 0x00007ffee6552000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffee6ab0000 - 0x00007ffee6b53000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffee7e30000 - 0x00007ffee7e60000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffe7ba20000 - 0x00007ffe7ba2c000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\vcruntime140_1.dll
0x00007ffe256c0000 - 0x00007ffe2574d000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\msvcp140.dll
0x00007ffe233a0000 - 0x00007ffe24130000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\server\jvm.dll
0x00007ffee76c0000 - 0x00007ffee7772000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffee7010000 - 0x00007ffee70b6000 	C:\WINDOWS\System32\sechost.dll
0x00007ffee6c90000 - 0x00007ffee6da6000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffee6b60000 - 0x00007ffee6bd4000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffee5bc0000 - 0x00007ffee5c1e000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffedfbb0000 - 0x00007ffedfbbb000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffedc850000 - 0x00007ffedc886000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffee5ba0000 - 0x00007ffee5bb4000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffee4cf0000 - 0x00007ffee4d0a000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffe77330000 - 0x00007ffe7733a000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\jimage.dll
0x00007ffee3700000 - 0x00007ffee3941000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffee70c0000 - 0x00007ffee7444000 	C:\WINDOWS\System32\combase.dll
0x00007ffee79e0000 - 0x00007ffee7ac0000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffeda8b0000 - 0x00007ffeda8e9000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffee6140000 - 0x00007ffee61d9000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffe75db0000 - 0x00007ffe75dbf000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\instrument.dll
0x00007ffe256a0000 - 0x00007ffe256bf000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\java.dll
0x00007ffee7e70000 - 0x00007ffee859d000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffee6270000 - 0x00007ffee63e4000 	C:\WINDOWS\System32\wintypes.dll
0x00007ffee3b70000 - 0x00007ffee43c6000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffee7860000 - 0x00007ffee794f000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffee7b80000 - 0x00007ffee7be9000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffee5e40000 - 0x00007ffee5e6f000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffe255c0000 - 0x00007ffe25697000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\jsvml.dll
0x00007ffe255a0000 - 0x00007ffe255b8000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\zip.dll
0x00007ffe75110000 - 0x00007ffe75120000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\net.dll
0x00007ffedfa80000 - 0x00007ffedfb9e000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffee5260000 - 0x00007ffee52ca000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffe25580000 - 0x00007ffe25596000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\nio.dll
0x00007ffe6b6d0000 - 0x00007ffe6b6e0000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\verify.dll
0x00007ffea7ff0000 - 0x00007ffea8017000 	C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64\native-platform.dll
0x00007ffe23250000 - 0x00007ffe23394000 	C:\Users\<USER>\.gradle\native\100fb08df4bc3b14c8652ba06237920a3bd2aa13389f12d3474272988ae205f9\windows-amd64\native-platform-file-events.dll
0x00007ffe6ae10000 - 0x00007ffe6ae1a000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\management.dll
0x00007ffe6a430000 - 0x00007ffe6a43b000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\management_ext.dll
0x00007ffee8d40000 - 0x00007ffee8d48000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffee5510000 - 0x00007ffee552c000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffee4c50000 - 0x00007ffee4c8a000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffee5300000 - 0x00007ffee532b000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffee5e10000 - 0x00007ffee5e36000 	C:\WINDOWS\SYSTEM32\bcrypt.dll
0x00007ffee5500000 - 0x00007ffee550c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffee47a0000 - 0x00007ffee47d3000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffee7780000 - 0x00007ffee778a000 	C:\WINDOWS\System32\NSI.dll
0x00007ffe69fc0000 - 0x00007ffe69fc9000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\extnet.dll
0x00007ffe69ee0000 - 0x00007ffe69eee000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\sunmscapi.dll
0x00007ffee6930000 - 0x00007ffee6aa7000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007ffee5710000 - 0x00007ffee5740000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007ffee56c0000 - 0x00007ffee56ff000 	C:\WINDOWS\SYSTEM32\NTASN1.dll
0x00007ffea4570000 - 0x00007ffea4578000 	C:\WINDOWS\system32\wshunix.dll
0x00007ffec65d0000 - 0x00007ffec65e8000 	C:\WINDOWS\system32\napinsp.dll
0x00007ffee47e0000 - 0x00007ffee4907000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x00007ffec65b0000 - 0x00007ffec65c2000 	C:\WINDOWS\System32\winrnr.dll
0x00007ffec6570000 - 0x00007ffec65a0000 	C:\WINDOWS\system32\nlansp_c.dll
0x00007ffec5b40000 - 0x00007ffec5b60000 	C:\WINDOWS\system32\wshbth.dll
0x00007ffedfbc0000 - 0x00007ffedfbcb000 	C:\Windows\System32\rasadhlp.dll
0x00007ffee06d0000 - 0x00007ffee0756000 	C:\WINDOWS\System32\fwpuclnt.dll
0x00007ffedb000000 - 0x00007ffedb01e000 	C:\Users\<USER>\AppData\Local\Temp\native-platform1997863555420315592dir\native-platform.dll
0x00007ffecae20000 - 0x00007ffecae27000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\rmi.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin;C:\WINDOWS\SYSTEM32;C:\Program Files\Norton\Suite;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.3912_none_3e07963ce335137e;C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\server;C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64;C:\Users\<USER>\.gradle\native\100fb08df4bc3b14c8652ba06237920a3bd2aa13389f12d3474272988ae205f9\windows-amd64;C:\Users\<USER>\AppData\Local\Temp\native-platform1997863555420315592dir

VM Arguments:
jvm_args: -XX:MaxMetaspaceSize=512m --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED -Xmx2048m -Dfile.encoding=UTF-8 -Duser.country=IN -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.10.2-all\7iv73wktx1xtkvlq19urqw1wm\gradle-8.10.2\lib\agents\gradle-instrumentation-agent-8.10.2.jar 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.10.2
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.10.2-all\7iv73wktx1xtkvlq19urqw1wm\gradle-8.10.2\lib\gradle-daemon-main-8.10.2.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
   size_t CompressedClassSpaceSize                 = 436207616                                 {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 13                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 125829120                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxMetaspaceSize                         = 536870912                                 {product} {command line}
   size_t MaxNewSize                               = 1287651328                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-********-hotspot
CLASSPATH=C:\Users\<USER>\quiz-bee-techs\android\\gradle\wrapper\gradle-wrapper.jar
PATH=C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin;C:\Program Files\Eclipse Adoptium\jdk-8.0.402.6-hotspot\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\dotnet\;C:\ProgramData\chocolatey\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\Program Files\MongoDB\Server\8.0\bin;C:\Program Files\Git\cmd;C:\Program Files\CMake\bin;C:\Program Files\CMake\bin;:\Users\gokul\AppData\Roaming\npm\node_modules;C:\Program Files\nodejs\;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Android\Sdk
\emulator;C:\Users\<USER>\AppData\Local\Android\Sdk
\tools;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\Users\<USER>\AppData\Local\Android\Sdk
\tools\bin;C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin;C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin;C:\Program Files\Eclipse Adoptium\jdk-8.0.402.6-hotspot\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\dotnet\;C:\ProgramData\chocolatey\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\Program Files\MongoDB\Server\8.0\bin;C:\Program Files\Git\cmd;C:\Program Files\CMake\bin;C:\Program Files\CMake\bin;:\Users\gokul\AppData\Roaming\npm\node_modules;C:\Program Files\nodejs\;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Android\Sdk
\emulator;C:\Users\<USER>\AppData\Local\Android\Sdk
\tools;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\Users\<USER>\AppData\Local\Android\Sdk
\tools\bin;C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin;;C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\node_modules\.bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand
USERNAME=gokul
LANG=en_US.UTF-8
OS=Windows_NT
PROCESSOR_IDENTIFIER=AMD64 Family 23 Model 96 Stepping 1, AuthenticAMD
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.3912)
OS uptime: 0 days 6:12 hours
Hyper-V role detected

CPU: total 16 (initial active 16) (16 cores per cpu, 2 threads per core) family 23 model 96 stepping 1 microcode 0x8600106, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4a, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, hv, rdtscp, rdpid, f16c
Processor Information for the first 16 processors :
  Max Mhz: 2901, Current Mhz: 2901, Mhz Limit: 2901

Memory: 4k page, system-wide physical 7599M (748M free)
TotalPageFile size 31151M (AvailPageFile size 1M)
current process WorkingSet (physical memory assigned to process): 1399M, peak: 1399M
current process commit charge ("private bytes"): 1695M, peak: 1696M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+6-LTS) for windows-amd64 JRE (21.0.7+6-LTS), built on 2025-04-15T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
